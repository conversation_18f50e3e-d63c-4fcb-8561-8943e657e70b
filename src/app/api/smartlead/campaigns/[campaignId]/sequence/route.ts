import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch sequence details for a specific campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  const parameters = await context.params;
  const campaignId = parameters.campaignId;
  if (!campaignId) {
    return NextResponse.json({ error: 'Missing campaignId' }, { status: 400 });
  }
  try {
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/sequences?api_key=${SMARTLEAD_API_KEY}`;
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });
    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json({ error: errorText }, { status: response.status });
    }
    const data = await response.json();
    // data is an array of steps, each with subject, email_body, sequence_variants (often null)
    let sequence_variants = [];
    if (Array.isArray(data) && data.length > 0) {
      // For each step, create a variant (label: Step 1, Step 2, ...)
      sequence_variants = data.map((step, idx) => {
        // If step.sequence_variants is an array, flatten them in
        if (Array.isArray(step.sequence_variants) && step.sequence_variants.length > 0) {
          // Use the provided variants
          return step.sequence_variants.map((variant: any, vIdx: number) => ({
            variant_label: variant.variant_label || `Step ${idx + 1}${vIdx > 0 ? `.${vIdx + 1}` : ''}`,
            subject: variant.subject || step.subject || '',
            email_body: variant.email_body || step.email_body || '',
          }));
        } else {
          // No variants, use the step as a single variant
          return [{
            variant_label: `Step ${idx + 1}`,
            subject: step.subject || '',
            email_body: step.email_body || '',
          }];
        }
      }).flat();
    }
    return NextResponse.json({ sequence_variants });
  } catch (error) {
    return NextResponse.json({ error: (error as Error).message }, { status: 500 });
  }
}

/**
 * POST: Update sequence data for a specific campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const { campaignId } = context.params;
    const body = await req.json();
    const sequenceData = body.sequence_data;
    
    if (!sequenceData) {
      return NextResponse.json(
        { error: 'Sequence data is required' },
        { status: 400 }
      );
    }
    
    // For now, we're assuming the sequenceData is in the format expected by Smartlead
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/sequences?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sequenceData),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { error: `Failed to update sequence: ${response.status}` },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    return NextResponse.json({
      success: true,
      message: 'Sequence updated successfully',
      data
    });
    
  } catch (error) {
    return NextResponse.json(
      { error: `Error updating sequence: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 