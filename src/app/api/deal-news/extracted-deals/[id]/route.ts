import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export const dynamic = 'force-dynamic';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
    const parameters = await params;
    const dealId = parameters.id;
  
  if (!dealId || isNaN(Number(dealId))) {
    return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
  }

  try {
    const client = await pool.connect();
    try {
      // Get the main deal information
      const dealQuery = `
        SELECT 
          dnd.id,
          dnd.news_id,
          dnd.deal_type,
          dnd.deal_status,
          dnd.deal_date,
          dnd.deal_value,
          dnd.currency,
          dnd.cap_rate,
          dnd.ltv,
          dnd.confidence_score,
          dnd.property_type,
          dnd.property_size,
          dnd.property_size_unit,
          dnd.property_address,
          dnd.deal_details,
          dnd.created_at,
          dn.news_title,
          dn.news_date,
          dn.news_text,
          dn.url as news_url
        FROM deal_news_deals dnd
        LEFT JOIN deal_news dn ON dnd.news_id = dn.id
        WHERE dnd.id = $1
      `;

      const dealResult = await client.query(dealQuery, [dealId]);
      
      if (dealResult.rows.length === 0) {
        return NextResponse.json({ error: 'Deal not found' }, { status: 404 });
      }
      
      const deal = dealResult.rows[0];
      
      // Get companies involved in the deal
      const companiesQuery = `
        SELECT 
          id,
          company_name,
          role,
          confidence_score,
          participation_details,
          created_at
        FROM deal_news_companies
        WHERE deal_id = $1
      `;
      
      const companiesResult = await client.query(companiesQuery, [dealId]);
      
      // Get people involved in the deal
      const personsQuery = `
        SELECT 
          id,
          person_name,
          title,
          role,
          confidence_score,
          involvement_details,
          company_id,
          created_at
        FROM deal_news_persons
        WHERE deal_id = $1
      `;
      
      const personsResult = await client.query(personsQuery, [dealId]);
      
      // Combine all data
      const dealData = {
        ...deal,
        companies: companiesResult.rows,
        persons: personsResult.rows,
      };

      return NextResponse.json(dealData);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error(`Error fetching deal ${dealId}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch deal details' },
      { status: 500 }
    );
  }
} 