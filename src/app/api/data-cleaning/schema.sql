--
-- PostgreSQL database dump
--

-- Dumped from database version 17.2 (Ubuntu 17.2-1.pgdg22.04+1)
-- Dumped by pg_dump version 17.2 (Ubuntu 17.2-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: update_conversation_timestamp(); Type: FUNCTION; Schema: public; Owner: anax_user
--

CREATE FUNCTION public.update_conversation_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE conversations 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE conversation_id = NEW.conversation_id;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_conversation_timestamp() OWNER TO anax_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: action_results; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.action_results (
    result_id uuid DEFAULT gen_random_uuid() NOT NULL,
    nba_id uuid NOT NULL,
    outcome_type character varying(50) NOT NULL,
    result_data jsonb,
    notes text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.action_results OWNER TO anax_user;

--
-- Name: bounced_emails; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.bounced_emails (
    id integer NOT NULL,
    active_id text NOT NULL,
    email text NOT NULL,
    source text,
    removed_from_db boolean DEFAULT false
);


ALTER TABLE public.bounced_emails OWNER TO anax_user;

--
-- Name: bounced_emails_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.bounced_emails_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.bounced_emails_id_seq OWNER TO anax_user;

--
-- Name: bounced_emails_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.bounced_emails_id_seq OWNED BY public.bounced_emails.id;


--
-- Name: person_capital_type; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.person_capital_type (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.person_capital_type OWNER TO anax_user;

--
-- Name: capital_type_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.capital_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.capital_type_id_seq OWNER TO anax_user;

--
-- Name: capital_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.capital_type_id_seq OWNED BY public.person_capital_type.id;


--
-- Name: clean_criteria_values; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.clean_criteria_values (
    id integer NOT NULL,
    field character varying(255) NOT NULL,
    value character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.clean_criteria_values OWNER TO anax_user;

--
-- Name: clean_criteria_values_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.clean_criteria_values_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.clean_criteria_values_id_seq OWNER TO anax_user;

--
-- Name: clean_criteria_values_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.clean_criteria_values_id_seq OWNED BY public.clean_criteria_values.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.companies (
    company_id integer NOT NULL,
    company_name text,
    company_linkedin text,
    company_address text,
    company_city text,
    company_state text,
    company_zip text,
    company_website text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    industry text,
    contacts_guid text DEFAULT gen_random_uuid(),
    duplicate_set boolean DEFAULT false,
    company_master_id integer
);


ALTER TABLE public.companies OWNER TO anax_user;

--
-- Name: companies_backup; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.companies_backup (
    company_id integer,
    company_name text,
    company_linkedin text,
    company_address text,
    company_city text,
    company_state text,
    company_zip text,
    company_website text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    industry text,
    guid uuid
);


ALTER TABLE public.companies_backup OWNER TO anax_user;

--
-- Name: companies_company_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.companies_company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.companies_company_id_seq OWNER TO anax_user;

--
-- Name: companies_company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.companies_company_id_seq OWNED BY public.companies.company_id;


--
-- Name: contacts; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.contacts (
    capital_type text,
    contact_source text,
    email text,
    first_name text,
    last_name text,
    phone_number text,
    company_name text,
    person_linkedin text,
    investment_criteria_country text,
    investment_criteria_geographic_region text,
    investment_criteria_state text,
    investment_criteria_city text,
    job_title text,
    industry text,
    company_website text,
    company_address text,
    investment_criteria_deal_size text,
    investment_criteria_property_type text,
    investment_criteria_property_type_subcategory text,
    investment_criteria_asset_type text,
    investment_criteria_loan_type text,
    investment_criteria_loan_type_short_term text,
    investment_criteria_loan_type_long_term text,
    investment_criteria_loan_term_years text,
    investment_criteria_loan_interest_rate_basis text,
    investment_criteria_loan_interest_rate text,
    investment_criteria_loan_to_value text,
    investment_criteria_loan_to_cost text,
    investment_criteria_loan_origination_fee_pct text,
    investment_criteria_loan_exit_fee_pct text,
    investment_criteria_recourse_loan text,
    investment_criteria_loan_dscr text,
    investment_criteria_closing_time text,
    investment_criteria_tear_sheet text,
    notes text,
    id integer,
    guid uuid,
    active_campaign_id text
);


ALTER TABLE public.contacts OWNER TO anax_user;

--
-- Name: contacts_backup; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.contacts_backup (
    capital_type text,
    contact_source text,
    email text,
    first_name text,
    last_name text,
    phone_number text,
    company_name text,
    person_linkedin text,
    investment_criteria_country text,
    investment_criteria_geographic_region text,
    investment_criteria_state text,
    investment_criteria_city text,
    job_title text,
    industry text,
    company_website text,
    company_address text,
    investment_criteria_deal_size text,
    investment_criteria_property_type text,
    investment_criteria_property_type_subcategory text,
    investment_criteria_asset_type text,
    investment_criteria_loan_type text,
    investment_criteria_loan_type_short_term text,
    investment_criteria_loan_type_long_term text,
    investment_criteria_loan_term_years text,
    investment_criteria_loan_interest_rate_basis text,
    investment_criteria_loan_interest_rate text,
    investment_criteria_loan_to_value text,
    investment_criteria_loan_to_cost text,
    investment_criteria_loan_origination_fee_pct text,
    investment_criteria_loan_exit_fee_pct text,
    investment_criteria_recourse_loan text,
    investment_criteria_loan_dscr text,
    investment_criteria_closing_time text,
    investment_criteria_tear_sheet text,
    notes text,
    id integer,
    guid uuid,
    active_campaign_id text
);


ALTER TABLE public.contacts_backup OWNER TO anax_user;

--
-- Name: contacts_backup1; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.contacts_backup1 (
    capital_type text,
    contact_source text,
    email text,
    first_name text,
    last_name text,
    phone_number text,
    company_name text,
    person_linkedin text,
    investment_criteria_country text,
    investment_criteria_geographic_region text,
    investment_criteria_state text,
    investment_criteria_city text,
    job_title text,
    industry text,
    company_website text,
    company_address text,
    investment_criteria_deal_size text,
    investment_criteria_property_type text,
    investment_criteria_property_type_subcategory text,
    investment_criteria_asset_type text,
    investment_criteria_loan_type text,
    investment_criteria_loan_type_short_term text,
    investment_criteria_loan_type_long_term text,
    investment_criteria_loan_term_years text,
    investment_criteria_loan_interest_rate_basis text,
    investment_criteria_loan_interest_rate text,
    investment_criteria_loan_to_value text,
    investment_criteria_loan_to_cost text,
    investment_criteria_loan_origination_fee_pct text,
    investment_criteria_loan_exit_fee_pct text,
    investment_criteria_recourse_loan text,
    investment_criteria_loan_dscr text,
    investment_criteria_closing_time text,
    investment_criteria_tear_sheet text,
    notes text,
    id integer,
    guid uuid,
    active_campaign_id text
);


ALTER TABLE public.contacts_backup1 OWNER TO anax_user;

--
-- Name: conversation_context; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversation_context (
    context_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    context_type character varying(100) NOT NULL,
    context_data jsonb NOT NULL,
    relevance_score double precision,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    expires_at timestamp with time zone
);


ALTER TABLE public.conversation_context OWNER TO anax_user;

--
-- Name: conversation_files; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversation_files (
    file_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    message_id uuid,
    file_name text NOT NULL,
    file_type character varying(100),
    storage_path text NOT NULL,
    size_bytes bigint,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.conversation_files OWNER TO anax_user;

--
-- Name: conversations; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.conversations (
    conversation_id uuid DEFAULT gen_random_uuid() NOT NULL,
    person_id integer NOT NULL,
    title text,
    status character varying(50) DEFAULT 'active'::character varying,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb
);


ALTER TABLE public.conversations OWNER TO anax_user;

--
-- Name: COLUMN conversations.metadata; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.conversations.metadata IS 'Example structure:
{
    "source": "email/web/mobile",
    "language": "en",
    "timezone": "UTC-5",
    "tags": ["investment", "property_type_discussion"],
    "user_preferences": {
        "communication_style": "formal",
        "preferred_contact_method": "email"
    }
}';


--
-- Name: criteria_value_matches; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.criteria_value_matches (
    id integer NOT NULL,
    field text NOT NULL,
    raw_value text NOT NULL,
    suggested_clean_value_id integer,
    suggested_clean_value text,
    current_clean_value_id integer,
    current_clean_value text,
    confidence_score double precision,
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.criteria_value_matches OWNER TO anax_user;

--
-- Name: criteria_value_matches_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.criteria_value_matches_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.criteria_value_matches_id_seq OWNER TO anax_user;

--
-- Name: criteria_value_matches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.criteria_value_matches_id_seq OWNED BY public.criteria_value_matches.id;


--
-- Name: industries; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.industries (
    industry_id integer NOT NULL,
    industry_name character varying(255) NOT NULL
);


ALTER TABLE public.industries OWNER TO anax_user;

--
-- Name: industries_industry_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.industries_industry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.industries_industry_id_seq OWNER TO anax_user;

--
-- Name: industries_industry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.industries_industry_id_seq OWNED BY public.industries.industry_id;


--
-- Name: investment_criteria_asset_type; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_asset_type (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_asset_type OWNER TO anax_user;

--
-- Name: investment_criteria_asset_type_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_asset_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_asset_type_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_asset_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_asset_type_id_seq OWNED BY public.investment_criteria_asset_type.id;


--
-- Name: investment_criteria_city; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_city (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_city OWNER TO anax_user;

--
-- Name: investment_criteria_city_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_city_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_city_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_city_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_city_id_seq OWNED BY public.investment_criteria_city.id;


--
-- Name: investment_criteria_closing_time; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_closing_time (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_closing_time OWNER TO anax_user;

--
-- Name: investment_criteria_closing_time_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_closing_time_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_closing_time_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_closing_time_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_closing_time_id_seq OWNED BY public.investment_criteria_closing_time.id;


--
-- Name: investment_criteria_country; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_country (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_country OWNER TO anax_user;

--
-- Name: investment_criteria_country_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_country_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_country_id_seq OWNED BY public.investment_criteria_country.id;


--
-- Name: investment_criteria_deal_size; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_deal_size (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_deal_size OWNER TO anax_user;

--
-- Name: investment_criteria_deal_size_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_deal_size_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_deal_size_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_deal_size_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_deal_size_id_seq OWNED BY public.investment_criteria_deal_size.id;


--
-- Name: investment_criteria_geographic_region; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_geographic_region (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_geographic_region OWNER TO anax_user;

--
-- Name: investment_criteria_geographic_region_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_geographic_region_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_geographic_region_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_geographic_region_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_geographic_region_id_seq OWNED BY public.investment_criteria_geographic_region.id;


--
-- Name: investment_criteria_loan_dscr; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_dscr (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_dscr OWNER TO anax_user;

--
-- Name: investment_criteria_loan_dscr_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_dscr_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_dscr_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_dscr_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_dscr_id_seq OWNED BY public.investment_criteria_loan_dscr.id;


--
-- Name: investment_criteria_loan_exit_fee_pct; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_exit_fee_pct (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_exit_fee_pct OWNER TO anax_user;

--
-- Name: investment_criteria_loan_exit_fee_pct_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_exit_fee_pct_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_exit_fee_pct_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_exit_fee_pct_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_exit_fee_pct_id_seq OWNED BY public.investment_criteria_loan_exit_fee_pct.id;


--
-- Name: investment_criteria_loan_interest_rate; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_interest_rate (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_interest_rate OWNER TO anax_user;

--
-- Name: investment_criteria_loan_interest_rate_basis; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_interest_rate_basis (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_interest_rate_basis OWNER TO anax_user;

--
-- Name: investment_criteria_loan_interest_rate_basis_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_interest_rate_basis_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_interest_rate_basis_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_interest_rate_basis_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_interest_rate_basis_id_seq OWNED BY public.investment_criteria_loan_interest_rate_basis.id;


--
-- Name: investment_criteria_loan_interest_rate_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_interest_rate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_interest_rate_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_interest_rate_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_interest_rate_id_seq OWNED BY public.investment_criteria_loan_interest_rate.id;


--
-- Name: investment_criteria_loan_origination_fee_pct; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_origination_fee_pct (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_origination_fee_pct OWNER TO anax_user;

--
-- Name: investment_criteria_loan_origination_fee_pct_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_origination_fee_pct_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_origination_fee_pct_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_origination_fee_pct_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_origination_fee_pct_id_seq OWNED BY public.investment_criteria_loan_origination_fee_pct.id;


--
-- Name: investment_criteria_loan_term_years; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_term_years (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_term_years OWNER TO anax_user;

--
-- Name: investment_criteria_loan_term_years_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_term_years_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_term_years_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_term_years_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_term_years_id_seq OWNED BY public.investment_criteria_loan_term_years.id;


--
-- Name: investment_criteria_loan_to_cost; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_to_cost (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_to_cost OWNER TO anax_user;

--
-- Name: investment_criteria_loan_to_cost_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_to_cost_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_to_cost_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_to_cost_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_to_cost_id_seq OWNED BY public.investment_criteria_loan_to_cost.id;


--
-- Name: investment_criteria_loan_to_value; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_to_value (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_to_value OWNER TO anax_user;

--
-- Name: investment_criteria_loan_to_value_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_to_value_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_to_value_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_to_value_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_to_value_id_seq OWNED BY public.investment_criteria_loan_to_value.id;


--
-- Name: investment_criteria_loan_type; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_type (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_type OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_type_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_type_id_seq OWNED BY public.investment_criteria_loan_type.id;


--
-- Name: investment_criteria_loan_type_long_term; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_type_long_term (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_type_long_term OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_long_term_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_type_long_term_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_type_long_term_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_long_term_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_type_long_term_id_seq OWNED BY public.investment_criteria_loan_type_long_term.id;


--
-- Name: investment_criteria_loan_type_short_term; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_loan_type_short_term (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_loan_type_short_term OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_short_term_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_loan_type_short_term_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_loan_type_short_term_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_loan_type_short_term_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_loan_type_short_term_id_seq OWNED BY public.investment_criteria_loan_type_short_term.id;


--
-- Name: investment_criteria_property_type; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_property_type (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_property_type OWNER TO anax_user;

--
-- Name: investment_criteria_property_type_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_property_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_property_type_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_property_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_property_type_id_seq OWNED BY public.investment_criteria_property_type.id;


--
-- Name: investment_criteria_property_type_subcategory; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_property_type_subcategory (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    property_type_id integer
);


ALTER TABLE public.investment_criteria_property_type_subcategory OWNER TO anax_user;

--
-- Name: investment_criteria_property_type_subcategory_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_property_type_subcategory_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_property_type_subcategory_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_property_type_subcategory_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_property_type_subcategory_id_seq OWNED BY public.investment_criteria_property_type_subcategory.id;


--
-- Name: investment_criteria_recourse_loan; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_recourse_loan (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.investment_criteria_recourse_loan OWNER TO anax_user;

--
-- Name: investment_criteria_recourse_loan_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_recourse_loan_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_recourse_loan_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_recourse_loan_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_recourse_loan_id_seq OWNED BY public.investment_criteria_recourse_loan.id;


--
-- Name: investment_criteria_state; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.investment_criteria_state (
    id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    country_id integer
);


ALTER TABLE public.investment_criteria_state OWNER TO anax_user;

--
-- Name: investment_criteria_state_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.investment_criteria_state_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.investment_criteria_state_id_seq OWNER TO anax_user;

--
-- Name: investment_criteria_state_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.investment_criteria_state_id_seq OWNED BY public.investment_criteria_state.id;


--
-- Name: loan_interest_rate_indices; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.loan_interest_rate_indices (
    index_id integer NOT NULL,
    index_name character varying(100) NOT NULL
);


ALTER TABLE public.loan_interest_rate_indices OWNER TO anax_user;

--
-- Name: loan_interest_rate_indices_index_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.loan_interest_rate_indices_index_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.loan_interest_rate_indices_index_id_seq OWNER TO anax_user;

--
-- Name: loan_interest_rate_indices_index_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.loan_interest_rate_indices_index_id_seq OWNED BY public.loan_interest_rate_indices.index_id;


--
-- Name: mailer; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.mailer (
    active_campaign_id text,
    email text,
    first_name text,
    last_name text,
    phone_number text,
    date_created timestamp without time zone,
    ip_address text,
    user_agent text,
    property_address_4 text,
    original_balance text,
    loan_name text,
    property_address_10 text,
    property_address_9 text,
    property_address_8 text,
    property_address_7 text,
    property_address_6 text,
    property_address_5 text,
    maturity_date text,
    property_address_3 text,
    property_address_2 text,
    property_address_1 text,
    property_address text,
    details text,
    capital_type text,
    profile_link text,
    column_n text,
    transaction_size text,
    campaign_name text,
    person_linkedin text,
    stage text,
    target_investment_size text,
    typical_size text,
    sector text,
    specialization text,
    transaction_target text,
    recent_contact text,
    fund text,
    investment_criteria text,
    equity_investment_size text,
    geographic_focus text,
    title text,
    current_balance text,
    loan_status text,
    owner_email_2 text,
    origination_fee_pct text,
    company_address text,
    industry text,
    country text,
    interest_rate_based_off_sofr text,
    closing_time text,
    recourse text,
    exit_fee_pct text,
    deal_size text,
    term_years text,
    loan_interest_rate text,
    region text,
    company_website text,
    state text,
    city text,
    notes text,
    loan_to_cost text,
    date text,
    comment text,
    interaction_score text,
    script text,
    company_location text,
    gender text,
    instagram text,
    e_mail text,
    company_name text,
    loan_to_value text,
    link_to_term_sheet text,
    loan_type text,
    dscr text,
    loan_type_subcategory_long_term text,
    loan_type_subcategory_short_term text,
    investment_criteria_property_type text,
    investment_criteria_property_type_subcategory text,
    tags text,
    added_to_contacts boolean DEFAULT false,
    exists_in_contacts boolean DEFAULT false,
    job_title text
);


ALTER TABLE public.mailer OWNER TO anax_user;

--
-- Name: messages; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.messages (
    message_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    role character varying(50) NOT NULL,
    content text NOT NULL,
    tokens_used integer,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    metadata jsonb
);


ALTER TABLE public.messages OWNER TO anax_user;

--
-- Name: next_best_actions; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.next_best_actions (
    nba_id uuid DEFAULT gen_random_uuid() NOT NULL,
    conversation_id uuid NOT NULL,
    action_type character varying(100) NOT NULL,
    priority integer NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    due_date timestamp with time zone,
    description text,
    reasoning text,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    completed_at timestamp with time zone,
    created_by character varying(100),
    metadata jsonb
);


ALTER TABLE public.next_best_actions OWNER TO anax_user;

--
-- Name: COLUMN next_best_actions.metadata; Type: COMMENT; Schema: public; Owner: anax_user
--

COMMENT ON COLUMN public.next_best_actions.metadata IS 'Example structure:
{
    "email_template": "investment_followup",
    "required_documents": ["proof_of_funds", "investment_criteria"],
    "reminder_frequency": "daily",
    "importance_factors": {
        "deal_size": "high",
        "urgency": "medium",
        "relationship_stage": "initial_contact"
    }
}';


--
-- Name: persons; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.persons (
    person_id integer NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(255) NOT NULL,
    last_name character varying(255) NOT NULL,
    job_title character varying(255),
    phone_number character varying(100),
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    company_id integer,
    person_linkedin character varying(255),
    contacts_guid uuid
);


ALTER TABLE public.persons OWNER TO anax_user;

--
-- Name: persons_person_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.persons_person_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.persons_person_id_seq OWNER TO anax_user;

--
-- Name: persons_person_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.persons_person_id_seq OWNED BY public.persons.person_id;


--
-- Name: state_corrections; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.state_corrections (
    raw_state_name character varying(255) NOT NULL,
    corrected_state_abbreviation character(2) NOT NULL
);


ALTER TABLE public.state_corrections OWNER TO anax_user;

--
-- Name: user_interactions; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.user_interactions (
    interaction_id integer NOT NULL,
    user_id integer,
    person_id integer,
    interaction_type character varying(50),
    interaction_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    notes text
);


ALTER TABLE public.user_interactions OWNER TO anax_user;

--
-- Name: user_interactions_interaction_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.user_interactions_interaction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_interactions_interaction_id_seq OWNER TO anax_user;

--
-- Name: user_interactions_interaction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.user_interactions_interaction_id_seq OWNED BY public.user_interactions.interaction_id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: anax_user
--

CREATE TABLE public.users (
    user_id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    role character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.users OWNER TO anax_user;

--
-- Name: users_user_id_seq; Type: SEQUENCE; Schema: public; Owner: anax_user
--

CREATE SEQUENCE public.users_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_user_id_seq OWNER TO anax_user;

--
-- Name: users_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: anax_user
--

ALTER SEQUENCE public.users_user_id_seq OWNED BY public.users.user_id;


--
-- Name: bounced_emails id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.bounced_emails ALTER COLUMN id SET DEFAULT nextval('public.bounced_emails_id_seq'::regclass);


--
-- Name: clean_criteria_values id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values ALTER COLUMN id SET DEFAULT nextval('public.clean_criteria_values_id_seq'::regclass);


--
-- Name: companies company_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.companies ALTER COLUMN company_id SET DEFAULT nextval('public.companies_company_id_seq'::regclass);


--
-- Name: criteria_value_matches id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.criteria_value_matches ALTER COLUMN id SET DEFAULT nextval('public.criteria_value_matches_id_seq'::regclass);


--
-- Name: industries industry_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries ALTER COLUMN industry_id SET DEFAULT nextval('public.industries_industry_id_seq'::regclass);


--
-- Name: investment_criteria_asset_type id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_asset_type ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_asset_type_id_seq'::regclass);


--
-- Name: investment_criteria_city id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_city ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_city_id_seq'::regclass);


--
-- Name: investment_criteria_closing_time id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_closing_time ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_closing_time_id_seq'::regclass);


--
-- Name: investment_criteria_country id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_country ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_country_id_seq'::regclass);


--
-- Name: investment_criteria_deal_size id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_deal_size ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_deal_size_id_seq'::regclass);


--
-- Name: investment_criteria_geographic_region id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_geographic_region ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_geographic_region_id_seq'::regclass);


--
-- Name: investment_criteria_loan_dscr id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_dscr ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_dscr_id_seq'::regclass);


--
-- Name: investment_criteria_loan_exit_fee_pct id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_exit_fee_pct ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_exit_fee_pct_id_seq'::regclass);


--
-- Name: investment_criteria_loan_interest_rate id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_interest_rate_id_seq'::regclass);


--
-- Name: investment_criteria_loan_interest_rate_basis id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate_basis ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_interest_rate_basis_id_seq'::regclass);


--
-- Name: investment_criteria_loan_origination_fee_pct id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_origination_fee_pct ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_origination_fee_pct_id_seq'::regclass);


--
-- Name: investment_criteria_loan_term_years id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_term_years ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_term_years_id_seq'::regclass);


--
-- Name: investment_criteria_loan_to_cost id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_cost ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_to_cost_id_seq'::regclass);


--
-- Name: investment_criteria_loan_to_value id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_value ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_to_value_id_seq'::regclass);


--
-- Name: investment_criteria_loan_type id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_type_id_seq'::regclass);


--
-- Name: investment_criteria_loan_type_long_term id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_long_term ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_type_long_term_id_seq'::regclass);


--
-- Name: investment_criteria_loan_type_short_term id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_short_term ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_loan_type_short_term_id_seq'::regclass);


--
-- Name: investment_criteria_property_type id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_property_type_id_seq'::regclass);


--
-- Name: investment_criteria_property_type_subcategory id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type_subcategory ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_property_type_subcategory_id_seq'::regclass);


--
-- Name: investment_criteria_recourse_loan id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_recourse_loan ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_recourse_loan_id_seq'::regclass);


--
-- Name: investment_criteria_state id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_state ALTER COLUMN id SET DEFAULT nextval('public.investment_criteria_state_id_seq'::regclass);


--
-- Name: loan_interest_rate_indices index_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices ALTER COLUMN index_id SET DEFAULT nextval('public.loan_interest_rate_indices_index_id_seq'::regclass);


--
-- Name: person_capital_type id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type ALTER COLUMN id SET DEFAULT nextval('public.capital_type_id_seq'::regclass);


--
-- Name: persons person_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.persons ALTER COLUMN person_id SET DEFAULT nextval('public.persons_person_id_seq'::regclass);


--
-- Name: user_interactions interaction_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions ALTER COLUMN interaction_id SET DEFAULT nextval('public.user_interactions_interaction_id_seq'::regclass);


--
-- Name: users user_id; Type: DEFAULT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users ALTER COLUMN user_id SET DEFAULT nextval('public.users_user_id_seq'::regclass);


--
-- Name: action_results action_results_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.action_results
    ADD CONSTRAINT action_results_pkey PRIMARY KEY (result_id);


--
-- Name: bounced_emails bounced_emails_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.bounced_emails
    ADD CONSTRAINT bounced_emails_pkey PRIMARY KEY (id);


--
-- Name: person_capital_type capital_type_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type
    ADD CONSTRAINT capital_type_pkey PRIMARY KEY (id);


--
-- Name: person_capital_type capital_type_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.person_capital_type
    ADD CONSTRAINT capital_type_value_key UNIQUE (value);


--
-- Name: clean_criteria_values clean_criteria_values_field_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values
    ADD CONSTRAINT clean_criteria_values_field_value_key UNIQUE (field, value);


--
-- Name: clean_criteria_values clean_criteria_values_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.clean_criteria_values
    ADD CONSTRAINT clean_criteria_values_pkey PRIMARY KEY (id);


--
-- Name: companies companies_guid_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_guid_key UNIQUE (contacts_guid);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (company_id);


--
-- Name: conversation_context conversation_context_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_context
    ADD CONSTRAINT conversation_context_pkey PRIMARY KEY (context_id);


--
-- Name: conversation_files conversation_files_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_pkey PRIMARY KEY (file_id);


--
-- Name: conversations conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_pkey PRIMARY KEY (conversation_id);


--
-- Name: criteria_value_matches criteria_value_matches_field_raw_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.criteria_value_matches
    ADD CONSTRAINT criteria_value_matches_field_raw_value_key UNIQUE (field, raw_value);


--
-- Name: criteria_value_matches criteria_value_matches_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.criteria_value_matches
    ADD CONSTRAINT criteria_value_matches_pkey PRIMARY KEY (id);


--
-- Name: industries industries_industry_name_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries
    ADD CONSTRAINT industries_industry_name_key UNIQUE (industry_name);


--
-- Name: industries industries_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.industries
    ADD CONSTRAINT industries_pkey PRIMARY KEY (industry_id);


--
-- Name: investment_criteria_asset_type investment_criteria_asset_type_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_asset_type
    ADD CONSTRAINT investment_criteria_asset_type_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_asset_type investment_criteria_asset_type_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_asset_type
    ADD CONSTRAINT investment_criteria_asset_type_value_key UNIQUE (value);


--
-- Name: investment_criteria_city investment_criteria_city_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_city
    ADD CONSTRAINT investment_criteria_city_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_city investment_criteria_city_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_city
    ADD CONSTRAINT investment_criteria_city_value_key UNIQUE (value);


--
-- Name: investment_criteria_closing_time investment_criteria_closing_time_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_closing_time
    ADD CONSTRAINT investment_criteria_closing_time_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_closing_time investment_criteria_closing_time_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_closing_time
    ADD CONSTRAINT investment_criteria_closing_time_value_key UNIQUE (value);


--
-- Name: investment_criteria_country investment_criteria_country_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_country
    ADD CONSTRAINT investment_criteria_country_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_country investment_criteria_country_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_country
    ADD CONSTRAINT investment_criteria_country_value_key UNIQUE (value);


--
-- Name: investment_criteria_deal_size investment_criteria_deal_size_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_deal_size
    ADD CONSTRAINT investment_criteria_deal_size_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_deal_size investment_criteria_deal_size_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_deal_size
    ADD CONSTRAINT investment_criteria_deal_size_value_key UNIQUE (value);


--
-- Name: investment_criteria_geographic_region investment_criteria_geographic_region_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_geographic_region
    ADD CONSTRAINT investment_criteria_geographic_region_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_geographic_region investment_criteria_geographic_region_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_geographic_region
    ADD CONSTRAINT investment_criteria_geographic_region_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_dscr investment_criteria_loan_dscr_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_dscr
    ADD CONSTRAINT investment_criteria_loan_dscr_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_dscr investment_criteria_loan_dscr_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_dscr
    ADD CONSTRAINT investment_criteria_loan_dscr_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_exit_fee_pct investment_criteria_loan_exit_fee_pct_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_exit_fee_pct
    ADD CONSTRAINT investment_criteria_loan_exit_fee_pct_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_exit_fee_pct investment_criteria_loan_exit_fee_pct_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_exit_fee_pct
    ADD CONSTRAINT investment_criteria_loan_exit_fee_pct_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_interest_rate_basis investment_criteria_loan_interest_rate_basis_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate_basis
    ADD CONSTRAINT investment_criteria_loan_interest_rate_basis_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_interest_rate_basis investment_criteria_loan_interest_rate_basis_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate_basis
    ADD CONSTRAINT investment_criteria_loan_interest_rate_basis_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_interest_rate investment_criteria_loan_interest_rate_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate
    ADD CONSTRAINT investment_criteria_loan_interest_rate_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_interest_rate investment_criteria_loan_interest_rate_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_interest_rate
    ADD CONSTRAINT investment_criteria_loan_interest_rate_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_origination_fee_pct investment_criteria_loan_origination_fee_pct_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_origination_fee_pct
    ADD CONSTRAINT investment_criteria_loan_origination_fee_pct_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_origination_fee_pct investment_criteria_loan_origination_fee_pct_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_origination_fee_pct
    ADD CONSTRAINT investment_criteria_loan_origination_fee_pct_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_term_years investment_criteria_loan_term_years_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_term_years
    ADD CONSTRAINT investment_criteria_loan_term_years_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_term_years investment_criteria_loan_term_years_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_term_years
    ADD CONSTRAINT investment_criteria_loan_term_years_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_to_cost investment_criteria_loan_to_cost_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_cost
    ADD CONSTRAINT investment_criteria_loan_to_cost_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_to_cost investment_criteria_loan_to_cost_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_cost
    ADD CONSTRAINT investment_criteria_loan_to_cost_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_to_value investment_criteria_loan_to_value_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_value
    ADD CONSTRAINT investment_criteria_loan_to_value_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_to_value investment_criteria_loan_to_value_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_to_value
    ADD CONSTRAINT investment_criteria_loan_to_value_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_type_long_term investment_criteria_loan_type_long_term_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_long_term
    ADD CONSTRAINT investment_criteria_loan_type_long_term_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_type_long_term investment_criteria_loan_type_long_term_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_long_term
    ADD CONSTRAINT investment_criteria_loan_type_long_term_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_type investment_criteria_loan_type_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type
    ADD CONSTRAINT investment_criteria_loan_type_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_type_short_term investment_criteria_loan_type_short_term_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_short_term
    ADD CONSTRAINT investment_criteria_loan_type_short_term_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_loan_type_short_term investment_criteria_loan_type_short_term_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type_short_term
    ADD CONSTRAINT investment_criteria_loan_type_short_term_value_key UNIQUE (value);


--
-- Name: investment_criteria_loan_type investment_criteria_loan_type_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_loan_type
    ADD CONSTRAINT investment_criteria_loan_type_value_key UNIQUE (value);


--
-- Name: investment_criteria_property_type investment_criteria_property_type_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type
    ADD CONSTRAINT investment_criteria_property_type_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_property_type_subcategory investment_criteria_property_type_subcategory_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type_subcategory
    ADD CONSTRAINT investment_criteria_property_type_subcategory_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_property_type_subcategory investment_criteria_property_type_subcategory_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type_subcategory
    ADD CONSTRAINT investment_criteria_property_type_subcategory_value_key UNIQUE (value);


--
-- Name: investment_criteria_property_type investment_criteria_property_type_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type
    ADD CONSTRAINT investment_criteria_property_type_value_key UNIQUE (value);


--
-- Name: investment_criteria_recourse_loan investment_criteria_recourse_loan_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_recourse_loan
    ADD CONSTRAINT investment_criteria_recourse_loan_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_recourse_loan investment_criteria_recourse_loan_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_recourse_loan
    ADD CONSTRAINT investment_criteria_recourse_loan_value_key UNIQUE (value);


--
-- Name: investment_criteria_state investment_criteria_state_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_state
    ADD CONSTRAINT investment_criteria_state_pkey PRIMARY KEY (id);


--
-- Name: investment_criteria_state investment_criteria_state_value_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_state
    ADD CONSTRAINT investment_criteria_state_value_key UNIQUE (value);


--
-- Name: loan_interest_rate_indices loan_interest_rate_indices_index_name_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices
    ADD CONSTRAINT loan_interest_rate_indices_index_name_key UNIQUE (index_name);


--
-- Name: loan_interest_rate_indices loan_interest_rate_indices_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.loan_interest_rate_indices
    ADD CONSTRAINT loan_interest_rate_indices_pkey PRIMARY KEY (index_id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (message_id);


--
-- Name: next_best_actions next_best_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.next_best_actions
    ADD CONSTRAINT next_best_actions_pkey PRIMARY KEY (nba_id);


--
-- Name: persons persons_guid_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.persons
    ADD CONSTRAINT persons_guid_key UNIQUE (contacts_guid);


--
-- Name: persons persons_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.persons
    ADD CONSTRAINT persons_pkey PRIMARY KEY (person_id);


--
-- Name: state_corrections state_corrections_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.state_corrections
    ADD CONSTRAINT state_corrections_pkey PRIMARY KEY (raw_state_name);


--
-- Name: user_interactions user_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_pkey PRIMARY KEY (interaction_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: idx_context_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_context_conversation ON public.conversation_context USING btree (conversation_id);


--
-- Name: idx_conversations_person; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_conversations_person ON public.conversations USING btree (person_id);


--
-- Name: idx_messages_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_messages_conversation ON public.messages USING btree (conversation_id);


--
-- Name: idx_nba_conversation; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_nba_conversation ON public.next_best_actions USING btree (conversation_id);


--
-- Name: idx_nba_status; Type: INDEX; Schema: public; Owner: anax_user
--

CREATE INDEX idx_nba_status ON public.next_best_actions USING btree (status);


--
-- Name: messages update_conversation_timestamp; Type: TRIGGER; Schema: public; Owner: anax_user
--

CREATE TRIGGER update_conversation_timestamp AFTER INSERT ON public.messages FOR EACH ROW EXECUTE FUNCTION public.update_conversation_timestamp();


--
-- Name: action_results action_results_nba_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.action_results
    ADD CONSTRAINT action_results_nba_id_fkey FOREIGN KEY (nba_id) REFERENCES public.next_best_actions(nba_id);


--
-- Name: conversation_context conversation_context_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_context
    ADD CONSTRAINT conversation_context_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: conversation_files conversation_files_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: conversation_files conversation_files_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversation_files
    ADD CONSTRAINT conversation_files_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(message_id);


--
-- Name: conversations conversations_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id);


--
-- Name: criteria_value_matches criteria_value_matches_current_clean_value_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.criteria_value_matches
    ADD CONSTRAINT criteria_value_matches_current_clean_value_id_fkey FOREIGN KEY (current_clean_value_id) REFERENCES public.clean_criteria_values(id);


--
-- Name: criteria_value_matches criteria_value_matches_suggested_clean_value_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.criteria_value_matches
    ADD CONSTRAINT criteria_value_matches_suggested_clean_value_id_fkey FOREIGN KEY (suggested_clean_value_id) REFERENCES public.clean_criteria_values(id);


--
-- Name: investment_criteria_property_type_subcategory investment_criteria_property_type_subcate_property_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_property_type_subcategory
    ADD CONSTRAINT investment_criteria_property_type_subcate_property_type_id_fkey FOREIGN KEY (property_type_id) REFERENCES public.investment_criteria_property_type(id);


--
-- Name: investment_criteria_state investment_criteria_state_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.investment_criteria_state
    ADD CONSTRAINT investment_criteria_state_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.investment_criteria_country(id);


--
-- Name: messages messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: next_best_actions next_best_actions_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.next_best_actions
    ADD CONSTRAINT next_best_actions_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(conversation_id);


--
-- Name: persons persons_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.persons
    ADD CONSTRAINT persons_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(company_id);


--
-- Name: user_interactions user_interactions_person_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_person_id_fkey FOREIGN KEY (person_id) REFERENCES public.persons(person_id) ON DELETE CASCADE;


--
-- Name: user_interactions user_interactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: anax_user
--

ALTER TABLE ONLY public.user_interactions
    ADD CONSTRAINT user_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

