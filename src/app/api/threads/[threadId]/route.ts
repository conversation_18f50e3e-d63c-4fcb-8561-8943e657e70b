import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// GET thread details
export async function GET(
  req: NextRequest,
  context: { params: { threadId: string } }
) {
  const params = await context.params;
  const threadId = params.threadId;
  
  try {
    // Fetch thread details
    const threadQuery = `
      SELECT 
        t.thread_id,
        t.subject,
        t.status,
        t.created_at,
        t.updated_at,
        t.metadata,
        COUNT(DISTINCT m.message_id) as message_count,
        COUNT(DISTINCT tp.contact_id) as participant_count
      FROM threads t
      LEFT JOIN messages m ON t.thread_id = m.thread_id
      LEFT JOIN thread_participants tp ON t.thread_id = tp.thread_id
      WHERE t.thread_id = $1
      GROUP BY t.thread_id
    `;
    
    const threadResult = await pool.query(threadQuery, [threadId]);
    
    if (threadResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Thread not found' },
        { status: 404 }
      );
    }
    
    const thread = threadResult.rows[0];
    
    // Fetch participants with contact details
    const participantsQuery = `
      SELECT 
        tp.thread_id,
        tp.contact_id,
        tp.user_id,
        tp.participant_type,
        tp.email,
        c.first_name,
        c.last_name,
        co.company_name
      FROM thread_participants tp
      LEFT JOIN contacts c ON tp.contact_id = c.contact_id
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE tp.thread_id = $1
    `;
    
    const participantsResult = await pool.query(participantsQuery, [threadId]);
    
    // Fetch messages
    const messagesQuery = `
      SELECT 
        message_id,
        thread_id,
        from_email,
        to_email,
        subject,
        body,
        direction,
        role,
        sent_at,
        created_at,
        metadata
      FROM messages
      WHERE thread_id = $1
      ORDER BY sent_at ASC, created_at ASC
    `;
    
    const messagesResult = await pool.query(messagesQuery, [threadId]);
    
    // Combine everything
    const threadWithDetails = {
      ...thread,
      participants: participantsResult.rows,
      messages: messagesResult.rows
    };
    
    return NextResponse.json(threadWithDetails);
  } catch (error) {
    console.error(`Error fetching thread ${threadId}:`, error);
    return NextResponse.json(
      { error: `Error fetching thread: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

// DELETE thread and its related data
export async function DELETE(
  req: NextRequest,
  context: { params: { threadId: string } }
) {
  const params = await context.params;
  const threadId = params.threadId;
  
  try {
    // Log deletion attempt
    console.log(`Attempting to delete thread: ${threadId} with all related data`);
    
    // Start a transaction
    await pool.query('BEGIN');
    
    try {
      // Count related data before deletion for logging/confirmation
      const countQuery = `
        SELECT 
          (SELECT COUNT(*) FROM thread_participants WHERE thread_id = $1) as participant_count,
          (SELECT COUNT(*) FROM messages WHERE thread_id = $1) as message_count
      `;
      const countResult = await pool.query(countQuery, [threadId]);
      const counts = countResult.rows[0];
      
      console.log(`Found ${counts.participant_count} participants and ${counts.message_count} messages to delete`);
      
      // Delete the thread participants first (foreign key)
      const participantResult = await pool.query(
        'DELETE FROM thread_participants WHERE thread_id = $1 RETURNING contact_id',
        [threadId]
      );
      
      // Delete the messages (foreign key)
      const messageResult = await pool.query(
        'DELETE FROM messages WHERE thread_id = $1 RETURNING message_id',
        [threadId]
      );
      
      // Finally delete the thread
      const threadResult = await pool.query(
        'DELETE FROM threads WHERE thread_id = $1 RETURNING thread_id',
        [threadId]
      );
      
      // Commit transaction
      await pool.query('COMMIT');
      
      if (threadResult.rowCount === 0) {
        return NextResponse.json(
          { error: 'Thread not found' },
          { status: 404 }
        );
      }
      
      console.log(`Successfully deleted thread ${threadId} with ${participantResult.rowCount} participants and ${messageResult.rowCount} messages`);
      
      return NextResponse.json({
        success: true,
        message: 'Thread and related data deleted successfully',
        threadId,
        deletedData: {
          participants: participantResult.rowCount,
          messages: messageResult.rowCount
        }
      });
    } catch (error) {
      // Rollback transaction if anything goes wrong
      await pool.query('ROLLBACK');
      console.error(`Transaction rolled back for thread deletion ${threadId}:`, error);
      throw error;
    }
  } catch (error) {
    console.error(`Error deleting thread ${threadId}:`, error);
    return NextResponse.json(
      { error: `Error deleting thread: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 