import { pool } from '../db'

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export interface EntityData {
  id: number
}

export interface ProcessingFilters {
  source?: string
}

export interface ProcessorOptions {
  limit?: number
  singleId?: number
  filters?: ProcessingFilters
  batchSize?: number
}

export interface ProcessorResult {
  processed: number
  successful: number
  failed: number
  errors: string[]
}

export abstract class BaseProcessor {
  protected pool: typeof pool
  protected options: ProcessorOptions
  protected name: string

  constructor(name: string, options: ProcessorOptions = {}) {
    this.name = name
    this.pool = pool
    this.options = {
      limit: 10,
      ...options
    }
  }

  protected async query(sql: string, params: unknown[] = []): Promise<Record<string, unknown>[]> {
    const client = await this.pool.connect()
    try {
      const result = await client.query(sql, params)
      return result.rows
    } finally {
      client.release()
    }
  }

  protected log(level: LogLevel, message: string): void {
    const timestamp = new Date().toISOString()
    console.log(`[${timestamp}] [${this.name}] [${level.toUpperCase()}] ${message}`)
  }

  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  protected async rateLimitedCall<T>(
    fn: () => Promise<T>, 
    delayMs: number
  ): Promise<T> {
    await this.sleep(delayMs)
    return await fn()
  }

  // Abstract methods that must be implemented by concrete processors
  abstract getUnprocessedEntities(): Promise<EntityData[]>
  abstract processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }>
  abstract updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void>

  // Main processing loop
  async process(): Promise<ProcessorResult> {
    const startTime = Date.now()
    this.log('info', `Starting ${this.name} processing...`)
    
    const entities = await this.getUnprocessedEntities()
    this.log('info', `Found ${entities.length} entities to process`)
    
    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[]
    }
    
    // If batch size is specified, process in batches
    const batchSize = this.options.batchSize || entities.length
    
    // Process entities in batches
    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize)
      this.log('info', `Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(entities.length / batchSize)} (${batch.length} entities)`)
      
      // Process each entity in the batch
      for (const entity of batch) {
        try {
          this.log('debug', `Processing entity ${entity.id}`)
          
          const result = await this.processEntity(entity)
          
          await this.updateEntityStatus(entity.id, result.success, result.error)
          
          results.processed++
          if (result.success) {
            results.successful++
            this.log('debug', `Successfully processed entity ${entity.id}`)
          } else {
            results.failed++
            if (result.error) {
              results.errors.push(result.error)
            }
            this.log('warn', `Failed to process entity ${entity.id}: ${result.error || 'Unknown error'}`)
          }
        } catch (error) {
          results.processed++
          results.failed++
          const errorMessage = error instanceof Error ? error.message : String(error)
          results.errors.push(errorMessage)
          this.log('error', `Error processing entity ${entity.id}: ${errorMessage}`)
          
          try {
            await this.updateEntityStatus(entity.id, false, errorMessage)
          } catch (updateError) {
            this.log('error', `Failed to update entity status: ${updateError}`)
          }
        }
      }
    }
    
    const duration = Date.now() - startTime
    this.log('info', `${this.name} processing completed in ${duration}ms. Processed: ${results.processed}, Successful: ${results.successful}, Failed: ${results.failed}`)
    
    return results
  }

  // Specific database query methods for different entity types

  /**
   * Get contacts that need email validation
   */
  protected async getContactsForEmailValidation(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      '(COALESCE(email_verification_status, \'pending\') = \'pending\')',
      'email IS NOT NULL',
      'email != \'\'',
      'email != \' \''
    ]
    const params: unknown[] = []
    let paramIndex = 1
    
    // Add source filter if provided
    if (this.options.filters?.source && this.options.filters.source !== 'all') {
      conditions.push(`source = $${paramIndex}`)
      params.push(this.options.filters.source)
      paramIndex++
    }
    
    if (singleId) {
      // First check if the single contact already satisfies the condition
      this.log('debug', `Checking single contact ${singleId} for email validation`)
      const checkSql = `
        SELECT contact_id, first_name, last_name, email, email_status, email_verification_status, source
        FROM contacts
        WHERE contact_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Contact ${singleId} not found`)
          return []
        }
        
        const contact = checkResult[0]
        
        // Check if contact has an email
        if (!contact.email || contact.email === '' || contact.email === ' ') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) has no email address`)
          return []
        }
        
        // For single runs, allow reprocessing even if already completed
        // Just check if email exists and matches source filter
        
        // Check if contact matches source filter
        if (this.options.filters?.source && this.options.filters.source !== 'all' && contact.source !== this.options.filters.source) {
          this.log('info', `Contact ${singleId} source (${contact.source}) doesn't match filter (${this.options.filters.source})`)
          return []
        }
        
        // For single runs, we allow reprocessing
        this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) will be processed (current email_verification_status: ${contact.email_verification_status || 'null'}, email_status: ${contact.email_status || 'null'})`)
      } catch (error) {
        this.log('error', `Error checking single contact ${singleId}: ${error}`)
        return []
      }
      
      // For single contact, bypass the normal conditions and just select by ID
      const singleContactSql = `
        SELECT contact_id, first_name, last_name, email, source,
               email_status, email_verification_status, created_at, updated_at
        FROM contacts 
        WHERE contact_id = $1
        AND email IS NOT NULL
        AND email != ''
        AND email != ' '
        ${this.options.filters?.source && this.options.filters.source !== 'all' ? `AND source = $2` : ''}
      `
      
             try {
         const singleParams: unknown[] = [singleId]
         if (this.options.filters?.source && this.options.filters.source !== 'all') {
           singleParams.push(this.options.filters.source)
         }
         return await this.query(singleContactSql, singleParams)
      } catch (error) {
        this.log('error', `Error fetching single contact for email validation: ${error}`)
        return []
      }
    }
    
    const sql = `
      SELECT contact_id, first_name, last_name, email, source,
             email_status, created_at, updated_at
      FROM contacts 
      WHERE ${conditions.join(' AND ')}
      ORDER BY created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching contacts for email validation: ${error}`)
      return []
    }
  }

  /**
   * Get contacts that need OSINT research
   */
  protected async getContactsForOSINT(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'c.email_verification_status = \'completed\'',
      '(COALESCE(c.osint_status, \'pending\') = \'pending\')'
    ]
    const params: unknown[] = []
    let paramIndex = 1
    
    // Add source filter if provided
    if (this.options.filters?.source && this.options.filters.source !== 'all') {
      conditions.push(`c.source = $${paramIndex}`)
      params.push(this.options.filters.source)
      paramIndex++
    }
    
    if (singleId) {
      // First check if the single contact already satisfies the condition
      const checkSql = `
        SELECT contact_id, first_name, last_name, email_verification_status, osint_status, searched, source
        FROM contacts 
        WHERE contact_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Contact ${singleId} not found`)
          return []
        }
        
        const contact = checkResult[0]
        
        // Check if contact matches source filter
        if (this.options.filters?.source && this.options.filters.source !== 'all' && contact.source !== this.options.filters.source) {
          this.log('info', `Contact ${singleId} source (${contact.source}) doesn't match filter (${this.options.filters.source})`)
          return []
        }
        
        if (contact.email_verification_status !== 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) needs email validation first (status: ${contact.email_verification_status})`)
          return []
        }
        
        if (contact.osint_status === 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) already has OSINT research completed`)
          return []
        }
      } catch (error) {
        this.log('error', `Error checking single contact ${singleId}: ${error}`)
        return []
      }
      
      conditions.push(`c.contact_id = $${paramIndex}`)
      params.push(singleId)
      paramIndex++
    }
    
    const sql = `
      SELECT c.contact_id, c.first_name, c.last_name, c.email, c.title, 
             c.linkedin_url, c.company_id, co.company_name, co.company_website,
             co.industry, c.contact_country, c.searched, c.source, c.created_at, c.updated_at
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching contacts for OSINT research: ${error}`)
      return []
    }
  }

  /**
   * Get OSINT research for a contact
   */
  protected async getContactOSINTResearch(contactId: number): Promise<Record<string, unknown> | null> {
    const sql = `
      SELECT profile
      FROM contact_searched_data
      WHERE contact_id = $1
    `
    try {
      const result = await this.query(sql, [contactId])
      return result[0] || null
    } catch (error) {
      this.log('error', `Error fetching contact OSINT research: ${error}`)
      return null
    }
  }

  /**
   * Get contacts that need overview extraction
   */
  protected async getContactsForOverviewExtraction(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'c.osint_status = \'completed\''
    ]
    
    // For batch processing, only get pending contacts
    if (!singleId) {
      conditions.push('(c.overview_extraction_status IS NULL OR c.overview_extraction_status = \'pending\')')
    }
    
    const params: unknown[] = []
    
    if (singleId) {
      // First check if the single contact already satisfies the condition
      const checkSql = `
        SELECT contact_id, full_name, first_name, last_name, email_status, overview_extraction_status,
               title, email, linkedin_url, company_id, contact_country, extra_attrs
        FROM contacts 
        WHERE contact_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Contact ${singleId} not found`)
          return []
        }
        
        const contact = checkResult[0]
        
        if (contact.email_status !== 'Verified') {
          this.log('info', `Contact ${singleId} (${contact.full_name || contact.first_name} ${contact.last_name}) needs email validation first (status: ${contact.email_status})`)
          return []
        }
        
        if (contact.overview_extraction_status === 'completed') {
          this.log('info', `Contact ${singleId} (${contact.full_name || contact.first_name} ${contact.last_name}) already has overview extraction completed - will reprocess for single run`)
          // For single runs, allow reprocessing even if completed
        }
      } catch (error) {
        this.log('error', `Error checking single contact ${singleId}: ${error}`)
        return []
      }
      
      conditions.push('c.contact_id = $1')
      params.push(singleId)
    }
    
    const sql = `
      SELECT c.contact_id, c.full_name, c.first_name, c.last_name, c.email, c.title,
             c.linkedin_url, c.company_id, co.company_name, co.company_website,
            co.industry, c.contact_country, c.extra_attrs, c.overview_extraction_status, c.created_at, c.updated_at
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching contacts for extraction: ${error}`)
      return []
    }
  }

  /**
   * Get overview data for a contact
   */
  protected async getContactOverviewData(contactId: number): Promise<Record<string, unknown> | null> {
    const sql = `
      SELECT executive_summary, career_timeline, notable_activities,
          personal_tidbits, conversation_hooks, outreach_draft, sources
      FROM contact_extracted_data
      WHERE contact_id = $1
    `
    try {
      const result = await this.query(sql, [contactId])
      return result[0] || null
    } catch (error) {
      this.log('error', `Error fetching contact overview data: ${error}`)
      return null
    }
  }
  /**
   * Get contacts that need classification
   */
  protected async getContactsForClassification(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    //
    const conditions = [
      'c.overview_extraction_status = \'completed\''
    ]
    
    // For batch processing, only get pending contacts
    if (!singleId) {
      conditions.push('(COALESCE(c.classification_status, \'pending\') = \'pending\')')
    }
    
    const params: unknown[] = []
    
    if (singleId) {
      // First check if the single contact already satisfies the condition
      const checkSql = `
        SELECT contact_id, first_name, last_name, email_verification_status, classification_status, category
        FROM contacts 
        WHERE contact_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Contact ${singleId} not found`)
          return []
        }
        
        const contact = checkResult[0]
        
        if (contact.email_verification_status !== 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) needs email validation first (status: ${contact.email_verification_status})`)
          return []
        }
        
        if (contact.classification_status === 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) already has classification completed - will reprocess for single run`)
          // For single runs, allow reprocessing even if completed
        }
        
      } catch (error) {
        this.log('error', `Error checking single contact ${singleId}: ${error}`)
        return []
      }
      
      conditions.push('c.contact_id = $1')
      params.push(singleId)
    }
    
    const sql = `
      SELECT c.contact_id, c.first_name, c.last_name, c.email, c.title,
             c.linkedin_url, c.company_id, co.company_name, co.company_website,
             co.industry, c.contact_country, c.category, c.created_at, c.updated_at
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching contacts for classification: ${error}`)
      return []
    }
  }


  /**
   * Get contacts that need email generation
   */
  protected async getContactsForEmailGeneration(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'c.classification_status = \'completed\'',
      '(COALESCE(c.email_generation_status, \'pending\') = \'pending\')'
    ]
    const params: unknown[] = []
    
    if (singleId) {
      // First check if the single contact already satisfies the condition
      const checkSql = `
        SELECT contact_id, first_name, last_name, email_verification_status, osint_status, classification_status, email_generation_status
        FROM contacts 
        WHERE contact_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Contact ${singleId} not found`)
          return []
        }
        
        const contact = checkResult[0]
        
        if (contact.email_verification_status !== 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) needs email validation first (status: ${contact.email_verification_status})`)
          return []
        }
        
        if (contact.osint_status !== 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) needs OSINT research first (status: ${contact.osint_status})`)
          return []
        }
        
        if (contact.classification_status !== 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) needs classification first (status: ${contact.classification_status})`)
          return []
        }
        
        if (contact.email_generation_status === 'completed') {
          this.log('info', `Contact ${singleId} (${contact.first_name} ${contact.last_name}) already has email generation completed`)
          return []
        }
      } catch (error) {
        this.log('error', `Error checking single contact ${singleId}: ${error}`)
        return []
      }
      
      conditions.push('c.contact_id = $1')
      params.push(singleId)
    }
    
    const sql = `
      SELECT c.contact_id, c.first_name, c.last_name, c.email, c.title,
             c.linkedin_url, c.company_id, co.company_name, co.company_website,
             co.industry, c.contact_country, c.email_generated, c.created_at, c.updated_at
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching contacts for email generation: ${error}`)
      return []
    }
  }

  
  /**
   * Get companies that need web crawling
   */
  protected async getCompaniesForWebCrawling(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'c.company_website IS NOT NULL',
      'c.company_website != \'\'',
      '(COALESCE(c.website_scraping_status, \'pending\') = \'pending\')'
    ];
    
    const params: unknown[] = [];
    let paramIndex = 1;

    if (singleId) {
      // First check if the single company already satisfies the condition
      const checkSql = `
        SELECT company_id, company_name, company_website, website_scraping_status
        FROM companies 
        WHERE company_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Company ${singleId} not found`)
          return []
        }
        
        const company = checkResult[0]
        
        if (!company.company_website || company.company_website === '') {
          this.log('info', `Company ${singleId} (${company.company_name}) has no website URL, cannot process`)
          return []
        }
        
        if (company.website_scraping_status === 'completed') {
          this.log('info', `Company ${singleId} (${company.company_name}) already has website scraping completed`)
          return []
        }
      } catch (error) {
        this.log('error', `Error checking single company ${singleId}: ${error}`)
        return []
      }
      
      conditions.push(`c.company_id = $${paramIndex}`)
      params.push(singleId)
      paramIndex++
    }
    
    const sql = `
      SELECT c.company_id, c.company_name, c.company_website, c.industry,
             c.website_scraping_status, c.created_at, c.updated_at
      FROM companies c
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `;
    
    try {
      return await this.query(sql, params);
    } catch (error) {
      this.log('error', `Error fetching companies for web crawling: ${error}`);
      return [];
    }
  }

    /**
   * Get combined content from all scraped pages for a company
   */
  protected async getCompanyContent(companyId: number): Promise<string> {
    try {
      // Updated query to use company_web_pages instead of company_pages
      // Note: page_type column might not exist in company_web_pages table
      const sql = `
        SELECT url, extracted_text as content
        FROM company_web_pages
        WHERE company_id = $1
        AND extracted_text IS NOT NULL
        ORDER BY 
          LENGTH(extracted_text) DESC
        LIMIT 20
      `
      
      const pages = await this.query(sql, [companyId])
      
      if (pages.length === 0) {
        this.log('warn', `No content found for company ${companyId}`)
        return ''
      }

      // Combine content with URL context (removing page_type which might not exist)
      const combinedContent = pages
        .map((page: Record<string, unknown>) => 
          `\n=== URL: ${page.url as string} ===\n${page.content as string}`)
        .join('\n\n')
        

      this.log('info', `Combined ${pages.length} pages of content for company ${companyId}`)
      return combinedContent
    } catch (error) {
      this.log('error', `Error fetching company content for ${companyId}: ${error}`)
      return ''
    }
  }


    /**
   * Get companies that need overview extraction
   */
  protected async getCompaniesForOverview(limit?: number, singleId?: number): Promise<Record<string, unknown>[]> {
    const conditions = [
      'website_scraping_status = \'completed\''
    ]
    
    // For batch processing, only get pending companies
    if (!singleId) {
      conditions.push("(COALESCE(company_overview_status, 'pending') = 'pending')")
    }
    
    const params: unknown[] = []
    
    if (singleId) {
      // First check if the single company already satisfies the condition
      const checkSql = `
        SELECT company_id, company_name, company_website, website_scraping_status, company_overview_status
        FROM companies 
        WHERE company_id = $1
      `
      try {
        const checkResult = await this.query(checkSql, [singleId])
        if (checkResult.length === 0) {
          this.log('warn', `Company ${singleId} not found`)
          return []
        }
        
        const company = checkResult[0]
        
        if (company.website_scraping_status !== 'completed') {
          this.log('info', `Company ${singleId} (${company.company_name}) needs website scraping first (status: ${company.website_scraping_status})`)
          return []
        }
        
        if (company.company_overview_status === 'completed') {
          this.log('info', `Company ${singleId} (${company.company_name}) already has overview completed - will reprocess for single run`)
          // For single runs, allow reprocessing even if completed
        }
        
        
      } catch (error) {
        this.log('error', `Error checking single company ${singleId}: ${error}`)
        return []
      }
      
      conditions.push('company_id = $1')
      params.push(singleId)
    }
    
    const sql = `
      SELECT c.company_id, c.company_name, c.company_website, c.industry,
              c.processed, c.extracted, c.created_at, c.updated_at
      FROM companies c
      WHERE ${conditions.join(' AND ')}
      ORDER BY c.created_at ASC
      ${limit ? `LIMIT ${limit}` : ''}
    `
    
    try {
      return await this.query(sql, params)
    } catch (error) {
      this.log('error', `Error fetching companies for overview extraction: ${error}`)
      return []
    }
  }
  // Status update methods using new status columns

  /**
   * Update contact email verification status
   */
  protected async updateContactEmailVerificationStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_verification_status = $1::text, 
          email_verification_error = $2,
          email_verification_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_verification_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email verification status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email verification status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact OSINT research status
   */
  protected async updateContactOSINTStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET osint_status = $1::text, 
          osint_error = $2,
          searched = $3,
          osint_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE osint_date END,
          updated_at = NOW() 
      WHERE contact_id = $4
    `
    try {
      // Update legacy searched field for backward compatibility
      const searched = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, searched, contactId])
      this.log('debug', `Updated OSINT status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating OSINT status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact overview extraction status
   */
  protected async updateContactOverviewExtractionStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET overview_extraction_status = $1::text, 
          overview_extraction_error = $2,
          overview_extraction_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE overview_extraction_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated overview extraction status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating overview extraction status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact classification status
   */
  protected async updateContactClassificationStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET classification_status = $1::text, 
          classification_error = $2,
          extracted = $3,
          classification_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE classification_date END,
          updated_at = NOW() 
      WHERE contact_id = $4
    `
    try {
      // Update legacy extracted field for backward compatibility
      const extracted = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, extracted, contactId])
      this.log('debug', `Updated classification status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating classification status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email generation status
   */
  protected async updateContactEmailGenerationStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_generation_status = $1::text, 
          email_generation_error = $2,
          email_generated = $3,
          email_generation_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_generation_date END,
          updated_at = NOW() 
      WHERE contact_id = $4
    `
    try {
      // Update legacy email_generated field for backward compatibility
      const emailGenerated = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, emailGenerated, contactId])
      this.log('debug', `Updated email generation status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email generation status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update contact email sending status
   */
  protected async updateContactEmailSendingStatus(
    contactId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_sending_status = $1::text, 
          email_sending_error = $2,
          email_sending_date = CASE WHEN $1::text = 'completed' THEN NOW() ELSE email_sending_date END,
          updated_at = NOW() 
      WHERE contact_id = $3
    `
    try {
      await this.query(sql, [status, error || null, contactId])
      this.log('debug', `Updated email sending status for contact ${contactId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating email sending status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Update company website scraping status
   */
  protected async updateCompanyWebscrapingStatus(
    companyId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET website_scraping_status = $1::text, 
          website_scraping_error = $2,
          processed = $3,
          updated_at = NOW() 
      WHERE company_id = $4
    `
    try {
      // Update legacy processed field for backward compatibility
      const processed = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, processed, companyId])
      this.log('debug', `Updated website scraping status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating website scraping status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Update company overview extraction status
   */
  protected async updateCompanyOverviewStatus(
    companyId: number, 
    status: 'pending' | 'running' | 'completed' | 'failed' | 'error',
    error?: string
  ): Promise<void> {
    const sql = `
      UPDATE companies 
      SET company_overview_status = $1::text, 
          company_overview_error = $2,
          extracted = $3,
          updated_at = NOW() 
      WHERE company_id = $4
    `
    try {
      // Update legacy extracted field for backward compatibility
      const extracted = status === 'completed' ? true : false
      await this.query(sql, [status, error || null, extracted, companyId])
      this.log('debug', `Updated company overview status for company ${companyId}: ${status}`)
    } catch (error) {
      this.log('error', `Error updating company overview status for company ${companyId}: ${error}`)
    }
  }

  /**
   * Increment processing error count
   */
  protected async incrementProcessingErrorCount(entityType: 'contact' | 'company', entityId: number): Promise<void> {
    const table = entityType === 'contact' ? 'contacts' : 'companies'
    const idColumn = entityType === 'contact' ? 'contact_id' : 'company_id'
    
    const sql = `
      UPDATE ${table} 
      SET processing_error_count = COALESCE(processing_error_count, 0) + 1,
          updated_at = NOW() 
      WHERE ${idColumn} = $1
    `
    try {
      await this.query(sql, [entityId])
      this.log('debug', `Incremented processing error count for ${entityType} ${entityId}`)
    } catch (error) {
      this.log('error', `Error incrementing processing error count for ${entityType} ${entityId}: ${error}`)
    }
  }

  // Legacy status update methods (deprecated - use new status column methods above)
  
  /**
   * Use updateContactEmailVerificationStatus instead
   */
  protected async updateContactEmailStatus(contactId: number, status: string): Promise<void> {
    this.log('warn', 'updateContactEmailStatus is deprecated, use updateContactEmailVerificationStatus instead')
    const sql = `UPDATE contacts SET email_status = $1, updated_at = NOW() WHERE contact_id = $2`
    try {
      await this.query(sql, [status, contactId])
    } catch (error) {
      this.log('error', `Error updating email status for contact ${contactId}: ${error}`)
    }
  }

  /**
   * Use updateContactOSINTStatus instead
   */
  protected async updateContactSearchStatus(contactId: number, success: boolean): Promise<void> {
    this.log('warn', 'updateContactSearchStatus is deprecated, use updateContactOSINTStatus instead')
    const sql = `UPDATE contacts SET searched = $1, updated_at = NOW() WHERE contact_id = $2`
    try {
      await this.query(sql, [success, contactId])
    } catch (error) {
      this.log('error', `Error updating search status for contact ${contactId}: ${error}`)
    }
  }
}