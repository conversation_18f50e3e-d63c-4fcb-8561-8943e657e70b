'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  AlertTriangle, 
  Building2, 
  User, 
  Calendar, 
  Filter,
  Settings,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'
import { ConflictRecord, CompanyConflict, ContactConflict, ConflictResolution } from '@/types/conflict'
import ConflictResolutionModal from './ConflictResolutionModal'

interface ConflictDashboardProps {
  onNavigateToUpload?: () => void
}

export default function ConflictDashboard({ onNavigateToUpload }: ConflictDashboardProps) {
  const [conflicts, setConflicts] = useState<{
    companies: CompanyConflict[]
    contacts: ContactConflict[]
  }>({ companies: [], contacts: [] })
  
  const [pagination, setPagination] = useState({ page: 1, pageSize: 20, totalCompanies: 0, totalContacts: 0, total: 0, totalPages: 0 })
  const [selectedConflicts, setSelectedConflicts] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [resolving, setResolving] = useState(false)
  const [clearing, setClearing] = useState(false)
  const [filter, setFilter] = useState<'all' | 'company' | 'contact'>('all')
  const [sortBy, setSortBy] = useState<'created_at' | 'name' | 'conflicts_count'>('created_at')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedConflict, setSelectedConflict] = useState<ConflictRecord | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)

  useEffect(() => {
    fetchConflicts()
  }, [currentPage, pageSize, filter])

  const fetchConflicts = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
        status: 'pending'
      })
      
      if (filter !== 'all') {
        params.append('type', filter)
      }

      const response = await fetch(`/api/conflicts?${params}`)
      const data = await response.json()

      if (data.success) {
        setConflicts(data.data)
        setPagination(data.data.pagination)
      }
    } catch (error) {
      console.error('Error fetching conflicts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleResolveConflicts = async (resolutions: ConflictResolution[]) => {
    setResolving(true)
    try {
      const response = await fetch('/api/conflicts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resolutions })
      })

      const data = await response.json()
      
      if (data.success) {
        await fetchConflicts()
        setSelectedConflicts(new Set())
      }
    } catch (error) {
      console.error('Error resolving conflicts:', error)
    } finally {
      setResolving(false)
    }
  }

  const handleBulkResolve = async (strategy: 'keep_existing' | 'use_new') => {
    const allConflicts = [...conflicts.companies, ...conflicts.contacts]
    const selectedRecords = allConflicts.filter(conflict => 
      selectedConflicts.has(`${conflict.type}-${conflict.id}`)
    )

    const resolutions: ConflictResolution[] = []
    
    selectedRecords.forEach(record => {
      Object.keys(record.conflicts).forEach(fieldName => {
        resolutions.push({
          record_id: record.id,
          record_type: record.type,
          field_name: fieldName,
          resolution: strategy
        })
      })
    })

    await handleResolveConflicts(resolutions)
  }

  const clearAllConflicts = async () => {
    if (!confirm('Are you sure you want to clear ALL conflicts? This action cannot be undone.')) {
      return
    }

    setClearing(true)
    try {
      const response = await fetch('/api/conflicts', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      })

      const data = await response.json()
      
      if (data.success) {
        alert(`Successfully cleared ${data.cleared.total} conflicts (${data.cleared.companies} companies, ${data.cleared.contacts} contacts)`)
        await fetchConflicts()
        setSelectedConflicts(new Set())
      } else {
        alert('Failed to clear conflicts: ' + (data.error || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error clearing conflicts:', error)
      alert('Failed to clear conflicts: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setClearing(false)
    }
  }

  const toggleConflictSelection = (conflictId: string) => {
    const newSelection = new Set(selectedConflicts)
    if (newSelection.has(conflictId)) {
      newSelection.delete(conflictId)
    } else {
      newSelection.add(conflictId)
    }
    setSelectedConflicts(newSelection)
  }

  const toggleSelectAll = () => {
    const allConflicts = [...conflicts.companies, ...conflicts.contacts]
    const allIds = allConflicts.map(conflict => `${conflict.type}-${conflict.id}`)
    
    if (selectedConflicts.size === allIds.length) {
      setSelectedConflicts(new Set())
    } else {
      setSelectedConflicts(new Set(allIds))
    }
  }

  const openResolutionModal = (conflict: ConflictRecord) => {
    setSelectedConflict(conflict)
    setIsModalOpen(true)
  }

    const allConflicts = [...conflicts.companies, ...conflicts.contacts]
    
  // Filter by search term (client-side for current page)
  const displayedConflicts = allConflicts.filter(conflict => {
      if (searchTerm && !conflict.name.toLowerCase().includes(searchTerm.toLowerCase())) return false
      return true
    })

  // Sort conflicts (client-side for current page)
  displayedConflicts.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'conflicts_count':
          return Object.keys(b.conflicts).length - Object.keys(a.conflicts).length
        case 'created_at':
        default:
          return new Date(b.conflict_created_at || 0).getTime() - new Date(a.conflict_created_at || 0).getTime()
      }
    })

  const totalConflicts = pagination.totalCompanies + pagination.totalContacts

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
    setSelectedConflicts(new Set()) // Clear selections when changing pages
  }

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize)
    setCurrentPage(1) // Reset to first page
    setSelectedConflicts(new Set()) // Clear selections
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Conflict Management</h2>
          <p className="text-muted-foreground">
            Resolve data conflicts from CSV imports
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {totalConflicts > 0 && (
            <Button 
              variant="destructive" 
              onClick={clearAllConflicts}
              disabled={clearing}
            >
              {clearing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Clearing...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Conflicts
                </>
              )}
            </Button>
          )}
          {onNavigateToUpload && (
            <Button onClick={onNavigateToUpload}>
              Upload New Data
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conflicts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConflicts}</div>
            <p className="text-xs text-muted-foreground">
              Pending resolution
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Company Conflicts</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.totalCompanies}</div>
            <p className="text-xs text-muted-foreground">
              Companies with conflicts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Conflicts</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.totalContacts}</div>
            <p className="text-xs text-muted-foreground">
              Contacts with conflicts
            </p>
          </CardContent>
        </Card>
      </div>

      {totalConflicts === 0 && !loading ? (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            No conflicts found! All your data is clean and up to date.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-4">
          {/* Filters and Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex gap-2 flex-wrap">
              <Input
                placeholder="Search conflicts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
              
              <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="company">Companies</SelectItem>
                  <SelectItem value="contact">Contacts</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Date Created</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="conflicts_count">Conflict Count</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedConflicts.size > 0 && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkResolve('keep_existing')}
                  disabled={resolving}
                >
                  Keep Existing ({selectedConflicts.size})
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkResolve('use_new')}
                  disabled={resolving}
                >
                  Use New ({selectedConflicts.size})
                </Button>
              </div>
            )}
          </div>

          {/* Conflicts List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedConflicts.size === displayedConflicts.length && displayedConflicts.length > 0}
                    onCheckedChange={toggleSelectAll}
                  />
                  Conflicts ({displayedConflicts.length})
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="space-y-3">
                  {displayedConflicts.map((conflict: ConflictRecord) => {
                    const conflictId = `${conflict.type}-${conflict.id}`
                    const isSelected = selectedConflicts.has(conflictId)
                    
                    return (
                      <div
                        key={conflictId}
                        className={`flex items-center justify-between p-4 border rounded-lg ${
                          isSelected ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleConflictSelection(conflictId)}
                          />
                          
                          <div className="flex items-center gap-2">
                            {conflict.type === 'company' ? <Building2 className="h-4 w-4" /> : <User className="h-4 w-4" />}
                            <div>
                              <p className="font-medium">{conflict.name}</p>
                              <div className="flex items-center gap-2 text-sm text-gray-600 flex-wrap">
                                <Badge variant="outline">
                                  {Object.keys(conflict.conflicts).length} conflicts
                                </Badge>
                                
                                {/* Show unique match conditions */}
                                {(() => {
                                  const allMatchReasons = new Set<string>()
                                  
                                  // Collect match reasons from field conflicts
                                  Object.entries(conflict.conflicts).forEach(([fieldName, fieldConflict]) => {
                                    if (fieldConflict.field_type === 'company_selection' && Array.isArray(fieldConflict.existing_value)) {
                                      fieldConflict.existing_value.forEach((match: any) => {
                                        if (match.match_reason) allMatchReasons.add(match.match_reason)
                                      })
                                    } else if (fieldConflict.match_reason) {
                                      allMatchReasons.add(fieldConflict.match_reason)
                                    }
                                  })
                                  
                                  // Add record-level match reason if available
                                  if ((conflict as any).match_reason) {
                                    allMatchReasons.add((conflict as any).match_reason)
                                  }
                                  
                                  // Render unique match reason badges
                                  return Array.from(allMatchReasons).map((reason: string) => (
                                    <Badge 
                                      key={reason} 
                                      variant="secondary" 
                                      className={`text-xs ${
                                        conflict.type === 'company' 
                                          ? 'bg-blue-50 text-blue-700 border-blue-200' 
                                          : 'bg-purple-50 text-purple-700 border-purple-200'
                                      }`}
                                    >
                                      Matched by {reason}
                                    </Badge>
                                  ))
                                })()}
                                
                                <span>•</span>
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(conflict.conflict_created_at || '').toLocaleDateString()}</span>
                                {conflict.conflict_source && (
                                  <>
                                    <span>•</span>
                                    <span className="text-xs">{conflict.conflict_source}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="destructive">
                            {Object.keys(conflict.conflicts).length} fields
                          </Badge>
                          <Button
                            size="sm"
                            onClick={() => openResolutionModal(conflict)}
                          >
                            Resolve
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pagination Controls */}
          {totalConflicts > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>Show</span>
                <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(parseInt(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
                <span>per page</span>
                <span>•</span>
                <span>
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalConflicts)} of {totalConflicts} conflicts
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(pagination.totalPages - 4, currentPage - 2)) + i
                    if (pageNum > pagination.totalPages) return null
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      )}

      <ConflictResolutionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        conflict={selectedConflict}
        onResolve={handleResolveConflicts}
      />
    </div>
  )
} 