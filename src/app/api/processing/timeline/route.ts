import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '7d';
    const source = searchParams.get('source') || 'all';
    const entityType = searchParams.get('entityType') || 'all';
    const stage = searchParams.get('stage') || 'all';

    const client = await pool.connect();

    try {
      // Determine date range
      let days = 7;
      switch (dateRange) {
        case '1d': days = 1; break;
        case '7d': days = 7; break;
        case '30d': days = 30; break;
        case '90d': days = 90; break;
        default: days = 7;
      }

      // Build base WHERE clause
      let whereClause = `WHERE DATE(updated_at) >= CURRENT_DATE - INTERVAL '${days} days'`;
      const queryParams: unknown[] = [];
      let paramIndex = 1;

      // Add source filter
      if (source !== 'all') {
        whereClause += ` AND source = $${paramIndex}`;
        queryParams.push(source);
        paramIndex++;
      }

      // Contact timeline data
      let contactTimelineData = [];
      if (entityType === 'all' || entityType === 'contacts') {
        const contactQuery = `
          SELECT 
            DATE(updated_at) as date,
            SUM(CASE WHEN email_status = 'Verified' AND COALESCE(extracted, false) = true THEN 1 ELSE 0 END) as email_verification,
            SUM(CASE WHEN COALESCE(searched, false) = true THEN 1 ELSE 0 END) as osint_search,
            SUM(CASE WHEN contact_category IS NOT NULL THEN 1 ELSE 0 END) as classification,
            SUM(CASE WHEN COALESCE(email_generated, false) = true THEN 1 ELSE 0 END) as email_generation
          FROM contacts 
          ${whereClause}
          GROUP BY DATE(updated_at)
          ORDER BY DATE(updated_at)
        `;

        const contactResult = await client.query(contactQuery, queryParams);
        contactTimelineData = contactResult.rows;
      }

      // Company timeline data
      let companyTimelineData = [];
      if (entityType === 'all' || entityType === 'companies') {
        let companyWhereClause = `WHERE DATE(c.updated_at) >= CURRENT_DATE - INTERVAL '${days} days'`;
        const companyParams = [...queryParams]; // Clone the params
        let companyParamIndex = paramIndex;
        
        // Apply source filter differently for companies - match either direct source or via contacts
        if (source !== 'all') {
          companyWhereClause = companyWhereClause.replace('WHERE', 'WHERE (');
          companyWhereClause += ` AND c.source = $${paramIndex - 1})`;
        }
        
        const companyQuery = `
          SELECT 
            DATE(c.updated_at) as date,
            SUM(CASE WHEN COALESCE(c.extracted, false) = true THEN 1 ELSE 0 END) as company_scraping,
            SUM(CASE WHEN COALESCE(c.processed, false) = true THEN 1 ELSE 0 END) as company_overview
          FROM companies c
          ${companyWhereClause}
          GROUP BY DATE(c.updated_at)
          ORDER BY DATE(c.updated_at)
        `;

        const companyResult = await client.query(companyQuery, companyParams);
        companyTimelineData = companyResult.rows;
      }

      // Merge contact and company data by date
      const dateMap = new Map();
      
      // Initialize with all dates in range
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        dateMap.set(dateStr, {
          date: dateStr,
          email_verification: 0,
          osint_search: 0,
          classification: 0,
          email_generation: 0,
          company_scraping: 0,
          company_overview: 0
        });
      }

      // Add contact data
      contactTimelineData.forEach((row: Record<string, unknown>) => {
        const dateStr = String(row.date || '');
        if (dateMap.has(dateStr)) {
          const existing = dateMap.get(dateStr);
          dateMap.set(dateStr, {
            ...existing,
            email_verification: parseInt(String(row.email_verification || 0)),
            osint_search: parseInt(String(row.osint_search || 0)),
            classification: parseInt(String(row.classification || 0)),
            email_generation: parseInt(String(row.email_generation || 0))
          });
        }
      });

      // Add company data
      companyTimelineData.forEach((row: Record<string, unknown>) => {
        const dateStr = String(row.date || '');
        if (dateMap.has(dateStr)) {
          const existing = dateMap.get(dateStr);
          dateMap.set(dateStr, {
            ...existing,
            company_scraping: parseInt(String(row.company_scraping || 0)),
            company_overview: parseInt(String(row.company_overview || 0))
          });
        }
      });

      // Convert to array and sort by date
      const timeline = Array.from(dateMap.values()).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // Generate metadata about the timeline data
      const metadata = {
        dataSource: 'PostgreSQL Timeline Aggregation',
        lastUpdated: new Date().toISOString(),
        queryDetails: `Date Range: ${dateRange} (${days} days)${source !== 'all' ? ` | Source: ${source} (companies are filtered to those with matching source OR with contacts from this source)` : ''}${entityType !== 'all' ? ` | Entity Type: ${entityType}` : ''}${stage !== 'all' ? ` | Stage: ${stage}` : ''}`,
        dataPoints: timeline.length,
        dateRange: {
          start: timeline.length > 0 ? timeline[0].date : null,
          end: timeline.length > 0 ? timeline[timeline.length - 1].date : null
        },
        explanations: {
          email_verification: "Number of contacts with verified emails",
          osint_search: "Number of contacts that have been searched through OSINT sources",
          classification: "Number of contacts that have been classified",
          email_generation: "Number of contacts with generated emails",
          company_scraping: "Number of companies with websites scraped",
          company_overview: "Number of companies with overview information extracted",
          total_contacts: "Total number of contacts",
          total_companies: "Total number of companies"
        }
      };

      return NextResponse.json({
        success: true,
        data: {
          timeline,
          metadata,
          filters: {
            dateRange,
            source,
            entityType,
            stage
          }
        }
      });

    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Timeline data error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
} 