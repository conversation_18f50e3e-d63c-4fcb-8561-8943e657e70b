import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const table = searchParams.get('table')
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  const offset = (page - 1) * limit

  if (!table) {
    return NextResponse.json({ error: 'Table name is required' }, { status: 400 })
  }

  try {
    // Add logging to debug
    console.log('Fetching data for table:', table)

    // Get columns
    const columnsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = $1
      ORDER BY ordinal_position
    `
    const columnsResult = await pool.query(columnsQuery, [table])
    
    // Add logging to debug
    console.log('Columns result:', columnsResult.rows)

    const columns = columnsResult.rows.map(row => row.column_name)

    // Get data with proper SQL injection protection
    const dataQuery = {
      text: `SELECT * FROM "${table}" LIMIT $1 OFFSET $2`,
      values: [limit, offset]
    }
    
    const countQuery = {
      text: `SELECT COUNT(*) FROM "${table}"`,
      values: []
    }
    
    const [dataResult, countResult] = await Promise.all([
      pool.query(dataQuery),
      pool.query(countQuery)
    ])

    // Add logging to debug
    console.log('Data result:', dataResult.rows)
    console.log('Count result:', countResult.rows)

    return NextResponse.json({
      columns,
      rows: dataResult.rows,
      totalRows: parseInt(countResult.rows[0].count)
    })
  } catch (error: any) {
    // Improve error logging
    console.error('Database error:', error)
    return NextResponse.json({ 
      error: 'Database error', 
      details: error.message 
    }, { status: 500 })
  }
} 