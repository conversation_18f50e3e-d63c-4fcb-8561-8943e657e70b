import React, { forwardRef, useImperativeHandle } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Button } from "@/components/ui/button";
import { Code, Eye, Edit } from 'lucide-react';
import { Textarea } from "@/components/ui/textarea";
import styles from './RichTextEditor.module.css';

export interface RichTextEditorProps {
  value: string;
  onChange?: (content: string) => void;
  height?: number;
  placeholder?: string;
  showToolbar?: boolean;
  minimalControls?: boolean;
  className?: string;
  disabled?: boolean;
}

export interface RichTextEditorRef {
  getContent: () => string;
  setContent: (content: string) => void;
  focus: () => void;
}

// Default TinyMCE configuration
const defaultConfig = {
  menubar: false,
  plugins: [
    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',
    'paste'
  ],
  toolbar: 'undo redo | formatselect | ' +
    'bold italic backcolor | alignleft aligncenter ' +
    'alignright alignjustify | bullist numlist outdent indent | ' +
    'removeformat | link table | code',
  height: 300,
  branding: false,
  paste_data_images: true,
  paste_word_valid_elements: 'b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,table,tr,td,th,br,a,img',
  paste_retain_style_properties: 'all',
  paste_webkit_styles: 'all',
  paste_merge_formats: true,
  
  // Fix for list styles
  formats: {
    // Override default formats to handle lists properly
    alignleft: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li', styles: { textAlign: 'left' } },
    aligncenter: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li', styles: { textAlign: 'center' } },
    alignright: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li', styles: { textAlign: 'right' } },
    alignjustify: { selector: 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li', styles: { textAlign: 'justify' } }
  },
  
  // Content style to properly display lists
  content_style: `
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 14px; }
    p { margin: 0 0 1em 0; }
    ul, ol { margin: 0 0 1em 1.5em; padding: 0; }
    li { margin-bottom: 0.5em; }
    
    /* Improve list formatting */
    ul > li, ol > li {
      list-style-position: outside;
    }
    
    /* Fix for nested p tags in lists */
    li > p {
      margin: 0;
      display: inline;
    }
  `,
  valid_elements: '@[id|class|style|title|dir<ltr?rtl|lang|xml::lang|role],a[rel|rev|charset|hreflang|tabindex|accesskey|type|name|href|target|title|class],strong/b,em/i,strike,u,#p,-div[align],br,img[src|alt|title|width|height|align|style],sub,sup,blockquote,-code,-pre,ul[type|compact|style],ol[type|compact|start|style],li[style],dl,dt,dd,table[border=0|cellspacing|cellpadding|width|frame|rules|height|align|bgcolor|background|bordercolor],tr[rowspan|width|height|align|valign|bgcolor|background|bordercolor],th[colspan|rowspan|width|height|align|valign|scope|bgcolor|background|bordercolor],td[colspan|rowspan|width|height|align|valign|bgcolor|background|bordercolor|nowrap],article,section,hgroup,figure,figcaption,aside,main,header,footer,address,h1,h2,h3,h4,h5,h6',
  
  // Force list handling to be more consistent
  forced_root_block: 'p',
  force_br_newlines: false,
  force_p_newlines: true,
  convert_newlines_to_brs: false,
  remove_trailing_brs: true,
  
  // Prevent unwanted attributes on list items
  extended_valid_elements: 'li[style|class|id|type],ul[style|class|id|type],ol[style|class|id|type]',
  invalid_elements: 'dir',
  
  // Custom paste preprocessing to clean up list formatting
  paste_preprocess: function(plugin: any, args: { content: string }) {
    // Clean up pasted content with nested paragraphs in list items
    let content = args.content;
    
    // Log original content for debugging
    console.log('Original pasted content:', content);
    
    // Replace problematic list item pattern
    content = content.replace(/<li[^>]*><p[^>]*>(.*?)<\/p><\/li>/g, '<li>$1</li>');
    
    // Preserve bullet characters and special entities
    content = content.replace(/&bull;/g, '•');
    
    // Fix nested lists with list-style-type: none - this often removes bullet points
    // content = content.replace(/<ul[^>]*style="[^"]*list-style-type:\s*none[^"]*"[^>]*>/g, '<ul>');
    
    // Instead, preserve the list style but ensure bullets are visible
    content = content.replace(/<ul[^>]*style="[^"]*list-style-type:\s*none[^"]*"[^>]*>/g, 
      '<ul style="list-style-type: disc;">');
    
    // Fix for Microsoft Word list conversion that might lack proper list markup
    content = content.replace(/<p[^>]*>(?:&nbsp;|\u00A0)*([\u2022\u2023\u25E6\u2043\u2219\u25AA\u25CF\u25C6\u25CB\u25A0\u25A1])(?:&nbsp;|\u00A0)*(.*?)<\/p>/g, 
      '<ul><li>$2</li></ul>');
    
    // Clean up dir and style attributes but preserve list styling
    content = content.replace(/dir="(ltr|rtl)"/g, '');
    content = content.replace(/style="([^"]*)white-space:\\s*pre;([^"]*)"/, 'style="$1$2"');
    
    // Ensure all list elements have proper styling
    content = content.replace(/<ul/g, '<ul style="list-style-type: disc;"');
    content = content.replace(/<ol/g, '<ol style="list-style-type: decimal;"');
    
    // Log processed content
    console.log('Processed pasted content:', content);
    
    args.content = content;
  }
};

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>((props, ref) => {
  const {
    value,
    onChange,
    height = 300,
    placeholder,
    showToolbar = true,
    minimalControls = false,
    className = '',
    disabled = false,
  } = props;

  const [showHtml, setShowHtml] = React.useState(false);
  const [previewMode, setPreviewMode] = React.useState(false);
  const [htmlContent, setHtmlContent] = React.useState(value || '');
  const editorRef = React.useRef<any>(null);
  const isEditorReady = React.useRef(false);
  const internalContentChange = React.useRef(false);
  const lastSelectionRef = React.useRef<any>(null);
  const contentChanged = React.useRef(false);

  // Configure editor based on props
  const editorConfig = {
    ...defaultConfig,
    height,
    placeholder,
    toolbar: minimalControls 
      ? 'bold italic | bullist numlist | link'
      : defaultConfig.toolbar,
    readonly: disabled,
    // Add setup callback to handle editor events
    setup: (editor: any) => {
      editor.on('init', () => {
        isEditorReady.current = true;
      });
      
      // Save selection on keyup, mouseup, and focus for better cursor position tracking
      editor.on('keyup mouseup focus', () => {
        if (editor.selection) {
          lastSelectionRef.current = editor.selection.getBookmark(2, true);
        }
      });
      
      // Mark content as changed to avoid restoring selection when user is typing
      editor.on('keydown paste input', () => {
        contentChanged.current = true;
      });
    }
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getContent: () => {
      if (editorRef.current) {
        return editorRef.current.getContent();
      }
      return htmlContent;
    },
    setContent: (content: string) => {
      setHtmlContent(content);
      if (editorRef.current && isEditorReady.current) {
        internalContentChange.current = true;
        
        // Save selection before updating content
        if (editorRef.current.selection) {
          lastSelectionRef.current = editorRef.current.selection.getBookmark(2, true);
        }
        
        editorRef.current.setContent(content);
        
        // Restore selection after content update
        if (lastSelectionRef.current && editorRef.current.selection) {
          try {
            editorRef.current.selection.moveToBookmark(lastSelectionRef.current);
            editorRef.current.focus();
          } catch (e) {
            console.log('Selection restoration failed in setContent');
          }
        }
        
        internalContentChange.current = false;
      }
    },
    focus: () => {
      if (editorRef.current) {
        editorRef.current.focus();
      }
    }
  }));

  // Update internal state when value prop changes
  // Only update if the value has changed and isn't from internal editor changes
  React.useEffect(() => {
    if (value !== htmlContent && !internalContentChange.current) {
      setHtmlContent(value || '');
      
      // If editor is ready, update content while preserving cursor position
      if (editorRef.current && isEditorReady.current) {
        // Only set content if it's really different (to avoid cursor position loss)
        if (editorRef.current.getContent() !== value) {
          // Don't restore selection if the user is actively editing
          if (!contentChanged.current) {
            // Remember selection
            if (editorRef.current.selection) {
              lastSelectionRef.current = editorRef.current.selection.getBookmark(2, true);
            }
          }
          
          // Update content
          internalContentChange.current = true;
          editorRef.current.setContent(value || '');
          internalContentChange.current = false;
          
          // Restore selection if possible and user wasn't typing
          if (lastSelectionRef.current && !contentChanged.current) {
            try {
              editorRef.current.selection.moveToBookmark(lastSelectionRef.current);
              editorRef.current.focus();
            } catch (e) {
              console.log('Selection restoration failed, continuing');
            }
          }
          
          // Reset the contentChanged flag after updating
          contentChanged.current = false;
        }
      }
    }
  }, [value]);

  const handleEditorChange = (content: string) => {
    // Only update if content actually changed
    // console.log('content', content);
    if (content !== htmlContent) {
      // Save the selection for later restoration
      if (editorRef.current && editorRef.current.selection) {
        lastSelectionRef.current = editorRef.current.selection.getBookmark(2, true);
      }
      
      setHtmlContent(content);
      
      if (onChange) {
        internalContentChange.current = true;
        onChange(content);
        internalContentChange.current = false;
      }
    }
  };

  return (
    <div className={`${styles.editorContainer} ${className}`}>
      {showToolbar && (
        <div className={styles.editorToolbar}>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowHtml(!showHtml)}
            className="text-xs"
          >
            <Code className="h-3.5 w-3.5 mr-1" />
            {showHtml ? "Hide HTML" : "Show HTML"}
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setPreviewMode(!previewMode)}
            className="text-xs"
          >
            {previewMode ? (
              <>
                <Edit className="h-3.5 w-3.5 mr-1" />
                Edit
              </>
            ) : (
              <>
                <Eye className="h-3.5 w-3.5 mr-1" />
                Preview
              </>
            )}
          </Button>
        </div>
      )}
      
      {showHtml ? (
        <Textarea
          value={htmlContent}
          onChange={(e) => handleEditorChange(e.target.value)}
          className={`${styles.htmlEditor}`}
          style={{ height: `${height}px` }}
          disabled={disabled}
        />
      ) : previewMode ? (
        <div 
          className={`${styles.previewContainer}`}
          dangerouslySetInnerHTML={{ __html: htmlContent }}
          style={{ minHeight: `${height}px`, overflow: 'auto' }}
        />
      ) : (
        <Editor
          tinymceScriptSrc="https://unpkg.com/tinymce@6/tinymce.min.js"
          onInit={(evt: any, editor: any) => {
            editorRef.current = editor;
            isEditorReady.current = true;
          }}
          initialValue={value}
          init={editorConfig}
          onEditorChange={handleEditorChange}
          disabled={disabled}
        />
      )}
    </div>
  );
});

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;