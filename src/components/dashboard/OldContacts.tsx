"use client"

import React, { useState, useEffect } from 'react'
import { 
  Search, Building2, Mail, Phone, User, MapPin, 
  Globe, Briefcase, Calendar, DollarSign, Home,
  Clock, Percent, FileText, ArrowLeft, ArrowRight,
  LinkedinIcon, Hash, Building, Landmark, Info
} from 'lucide-react'
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

interface Contact {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  company_name: string;
  person_linkedin: string;
  job_title: string;
  industry: string;
  company_website: string;
  company_address: string;
  capital_type: string;
  contact_source: string;
  investment_criteria_country: string;
  investment_criteria_geographic_region: string;
  investment_criteria_state: string;
  investment_criteria_city: string;
  investment_criteria_deal_size: string;
  investment_criteria_property_type: string;
  investment_criteria_property_type_subcategory: string;
  investment_criteria_asset_type: string;
  investment_criteria_loan_type: string;
  investment_criteria_loan_type_short_term: string;
  investment_criteria_loan_type_long_term: string;
  investment_criteria_loan_term_years: string;
  investment_criteria_loan_interest_rate_basis: string;
  investment_criteria_loan_interest_rate: string;
  investment_criteria_loan_to_value: string;
  investment_criteria_loan_to_cost: string;
  investment_criteria_loan_origination_fee_pct: string;
  investment_criteria_loan_exit_fee_pct: string;
  investment_criteria_recourse_loan: string;
  investment_criteria_loan_dscr: string;
  investment_criteria_closing_time: string;
  investment_criteria_tear_sheet: string;
  notes: string;
}

interface SearchResult {
  first_name: string;
  last_name: string;
  company_name: string;
}

export const OldContacts: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const [currentContact, setCurrentContact] = useState<Contact | null>(null)
  const [currentOffset, setCurrentOffset] = useState(0)
  const [totalContacts, setTotalContacts] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [showSearchResults, setShowSearchResults] = useState(false)

  useEffect(() => {
    if (isActive) {
      fetchContact(0)
    }
  }, [isActive])

  const fetchContact = async (offset: number) => {
    try {
      const response = await fetch(`/api/contacts?offset=${offset}`)
      const data = await response.json()
      if (response.ok && data.contact) {
        setCurrentContact(data.contact)
        setCurrentOffset(data.offset)
        setTotalContacts(data.total)
      }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }

  const handleSearch = async (term: string) => {
    setSearchTerm(term)
    if (term.length < 2) {
      setSearchResults([])
      setShowSearchResults(false)
      return
    }

    try {
      const response = await fetch(`/api/contacts?search=${encodeURIComponent(term)}`)
      const data = await response.json()
      setSearchResults(data)
      setShowSearchResults(true)
    } catch (err) {
      console.error('Search error:', err)
    }
  }

  const navigateContact = (direction: 'next' | 'prev') => {
    const newOffset = direction === 'next' 
      ? Math.min(currentOffset + 1, totalContacts - 1)
      : Math.max(currentOffset - 1, 0)
    fetchContact(newOffset)
  }

  if (!isActive) return null

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="flex flex-col space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <div className="flex gap-4 mb-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search by name or company..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
                onFocus={() => setShowSearchResults(true)}
              />
            </div>
          </div>

          {/* Autocomplete Results */}
          {showSearchResults && searchResults.length > 0 && (
            <div className="absolute z-10 w-full bg-white border rounded-md shadow-lg mt-1">
              {searchResults.map((result, idx) => (
                <button
                  key={idx}
                  className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between"
                  onClick={() => {
                    // Implement selection logic
                  }}
                >
                  <span>{result.first_name} {result.last_name}</span>
                  <span className="text-gray-500 text-sm">{result.company_name}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Contact Card */}
        {currentContact && (
          <Card className="shadow-lg">
            <CardHeader className="border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">
                  {currentContact.first_name} {currentContact.last_name}
                </h2>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-500">
                    Contact {currentOffset + 1} of {totalContacts}
                  </span>
                  <div className="flex gap-2">
                    <button
                      onClick={() => navigateContact('prev')}
                      disabled={currentOffset === 0}
                      className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
                    >
                      <ArrowLeft className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => navigateContact('next')}
                      disabled={currentOffset === totalContacts - 1}
                      className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
                    >
                      <ArrowRight className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
              {/* Contact Information */}
              <div className="space-y-6">
                <Section title="Contact Details">
                  <InfoItem icon={Mail} label="Email" value={currentContact.email} />
                  <InfoItem icon={Phone} label="Phone" value={currentContact.phone_number} />
                  <InfoItem icon={Briefcase} label="Job Title" value={currentContact.job_title} />
                  <InfoItem icon={LinkedinIcon} label="LinkedIn" value={currentContact.person_linkedin} isLink />
                  <InfoItem icon={Info} label="Source" value={currentContact.contact_source} />
                </Section>

                <Section title="Company Information">
                  <InfoItem icon={Building} label="Company" value={currentContact.company_name} />
                  <InfoItem icon={Globe} label="Website" value={currentContact.company_website} isLink />
                  <InfoItem icon={MapPin} label="Address" value={currentContact.company_address} />
                  <InfoItem icon={Building2} label="Industry" value={currentContact.industry} />
                </Section>
              </div>

              {/* Investment Criteria */}
              <div className="space-y-6">
                <Section title="Investment Criteria">
                  <InfoItem icon={Landmark} label="Capital Type" value={currentContact.capital_type} />
                  <InfoItem icon={MapPin} label="Geographic Focus" 
                    value={[
                      currentContact.investment_criteria_country,
                      currentContact.investment_criteria_geographic_region,
                      currentContact.investment_criteria_state,
                      currentContact.investment_criteria_city
                    ].filter(Boolean).join(', ')} 
                  />
                  <InfoItem icon={DollarSign} label="Deal Size" value={currentContact.investment_criteria_deal_size} />
                  <InfoItem icon={Home} label="Property Type" value={currentContact.investment_criteria_property_type} />
                  <InfoItem icon={Building2} label="Property Subcategory" value={currentContact.investment_criteria_property_type_subcategory} />
                  <InfoItem icon={FileText} label="Asset Type" value={currentContact.investment_criteria_asset_type} />
                </Section>

                <Section title="Loan Criteria">
                  <InfoItem icon={FileText} label="Loan Type" value={currentContact.investment_criteria_loan_type} />
                  <InfoItem icon={Clock} label="Term (Years)" value={currentContact.investment_criteria_loan_term_years} />
                  <InfoItem icon={Percent} label="Interest Rate" 
                    value={currentContact.investment_criteria_loan_interest_rate_basis 
                      ? `${currentContact.investment_criteria_loan_interest_rate} (${currentContact.investment_criteria_loan_interest_rate_basis})`
                      : currentContact.investment_criteria_loan_interest_rate} 
                  />
                  <InfoItem icon={Hash} label="LTV" value={currentContact.investment_criteria_loan_to_value} />
                  <InfoItem icon={Hash} label="LTC" value={currentContact.investment_criteria_loan_to_cost} />
                  <InfoItem icon={Percent} label="Origination Fee" value={currentContact.investment_criteria_loan_origination_fee_pct} />
                  <InfoItem icon={Percent} label="Exit Fee" value={currentContact.investment_criteria_loan_exit_fee_pct} />
                  <InfoItem icon={Hash} label="DSCR" value={currentContact.investment_criteria_loan_dscr} />
                  <InfoItem icon={Clock} label="Closing Time" value={currentContact.investment_criteria_closing_time} />
                </Section>

                {currentContact.notes && (
                  <Section title="Notes">
                    <p className="text-gray-600 whitespace-pre-wrap">{currentContact.notes}</p>
                  </Section>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

interface SectionProps {
  title: string;
  children: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({ title, children }) => (
  <div className="space-y-4">
    <h3 className="text-lg font-semibold border-b pb-2">{title}</h3>
    <div className="space-y-3">
      {children}
    </div>
  </div>
)

interface InfoItemProps {
  icon: LucideIcon;
  label: string;
  value: string | null;
  isLink?: boolean;
}

const InfoItem: React.FC<InfoItemProps> = ({ icon: Icon, label, value, isLink }) => {
  if (!value) return null;
  
  return (
    <div className="flex items-center gap-2">
      <Icon className="h-5 w-5 text-gray-400 flex-shrink-0" />
      <div>
        <div className="text-sm text-gray-500">{label}</div>
        {isLink ? (
          <a 
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:underline"
          >
            {value}
          </a>
        ) : (
          <div className="font-medium">{value}</div>
        )}
      </div>
    </div>
  )
} 