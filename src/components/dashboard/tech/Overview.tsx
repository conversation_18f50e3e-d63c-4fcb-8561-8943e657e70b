"use client"

import React from 'react';
import { 
  Building2, 
  Target, 
  Database,
  FileText,
  MessageSquare,
  ChevronRight,
  ArrowUpRight,
  Calendar,
  Clock,
  CheckCircle,
  Users,
  <PERSON><PERSON><PERSON>,
  LucideIcon
} from 'lucide-react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

interface CardSectionProps {
  children: React.ReactNode;
}

interface StatusCardProps {
  icon: LucideIcon;
  title: string;
  value: string;
  subtitle: string;
  iconColor?: string;
}

interface PriorityArea {
  title: string;
  priority: string;
  description: string;
  progress: number;
}

interface AssessmentItem {
  icon: LucideIcon;
  iconColor: string;
  title: string;
  description: string;
}

interface Milestone {
  icon: LucideIcon;
  title: string;
  date: string;
}

const Card: React.FC<CardProps> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<CardSectionProps> = ({ children }) => (
  <div className="border-b border-gray-200 p-4 sm:p-6">{children}</div>
);

const CardContent: React.FC<CardSectionProps> = ({ children }) => (
  <div className="p-4 sm:p-6">{children}</div>
);

const StatusCard: React.FC<StatusCardProps> = ({ icon: Icon, title, value, subtitle, iconColor = 'text-gray-400' }) => (
  <Card>
    <CardContent>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={`h-8 w-8 ${iconColor}`} />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500">{title}</dt>
            <dd className="text-lg font-semibold text-gray-900">{value}</dd>
            <dd className="text-sm text-gray-600">{subtitle}</dd>
          </dl>
        </div>
      </div>
    </CardContent>
  </Card>
);

const Overview: React.FC = () => {
  const priorityAreas: PriorityArea[] = [
    {
      title: "Deal Flow Automation",
      priority: "High Priority",
      description: "Automated contact enrichment, lead scoring, and capital source matching",
      progress: 30
    },
    {
      title: "Marketing Automation",
      priority: "Medium Priority",
      description: "Content generation, distribution, and engagement tracking",
      progress: 15
    },
    {
      title: "Capital Provider Database",
      priority: "High Priority",
      description: "Intelligent categorization and preference mapping",
      progress: 25
    }
  ];

  const assessmentItems: AssessmentItem[] = [
    {
      icon: CheckCircle,
      iconColor: "text-green-500",
      title: "Contact Database Analysis",
      description: "Data quality and structure assessment complete"
    },
    {
      icon: Clock,
      iconColor: "text-yellow-500",
      title: "Deal Flow Pipeline",
      description: "Process mapping and automation planning in progress"
    },
    {
      icon: Clock,
      iconColor: "text-yellow-500",
      title: "Integration Requirements",
      description: "Technical architecture review ongoing"
    }
  ];

  const milestones: Milestone[] = [
    {
      icon: Calendar,
      title: "Vision Workshop",
      date: "Nov 15, 2024 - 10:00 AM EST"
    },
    {
      icon: Users,
      title: "Technical Deep Dive",
      date: "Nov 18-19, 2024"
    },
    {
      icon: PieChart,
      title: "Phase 1 Review",
      date: "Dec 1, 2024"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <StatusCard
          icon={Target}
          title="Implementation Phase"
          value="Vision Alignment"
          subtitle="45% Complete"
          iconColor="text-blue-600"
        />
        <StatusCard
          icon={Users}
          title="Team Size"
          value="2 stakeholders"
          subtitle="Key decision makers"
        />
        <StatusCard
          icon={Database}
          title="Data Assessment"
          value="35,000 records"
          subtitle="Initial analysis complete"
        />
      </div>

      <Card>
        <CardHeader>
          <h3 className="text-base font-semibold text-gray-900">Priority Implementation Areas</h3>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {priorityAreas.map((area, index) => (
              <div key={index} className={`${index !== priorityAreas.length - 1 ? 'border-b border-gray-100 pb-4' : 'pb-4'}`}>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{area.title}</h4>
                  <span className="text-sm text-blue-600">{area.priority}</span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{area.description}</p>
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${area.progress}%` }} />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-base font-semibold text-gray-900">Data Infrastructure Assessment</h3>
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              {assessmentItems.map((item, index) => (
                <li key={index} className="flex items-start gap-3">
                  <item.icon className={`h-5 w-5 ${item.iconColor} mt-0.5`} />
                  <div>
                    <h4 className="font-medium text-gray-900">{item.title}</h4>
                    <p className="mt-1 text-sm text-gray-500">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-base font-semibold text-gray-900">Upcoming Milestones</h3>
          </CardHeader>
          <CardContent>
            <ul className="space-y-4">
              {milestones.map((milestone, index) => (
                <li key={index} className="flex gap-3">
                  <div className="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg bg-blue-50">
                    <milestone.icon className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{milestone.title}</p>
                    <p className="text-sm text-gray-500">{milestone.date}</p>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export { Overview };
export default Overview;