"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw, Search, Plus, Mail, Trash2, Trash, ExternalLink, Info, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { toast } from "sonner";

// Import sequence components from TrainingDataTab sections
import CampaignSelector from '../people/sections/CampaignSelector';

interface Lead {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  status?: string;
  added_at?: string;
  custom_fields?: {
    subject?: string;
    html_body?: string;
    [key: string]: any;
  };
  contact_info?: {
    contact_id: string;
    company_id?: number;
    contact_first_name?: string;
    contact_last_name?: string;
    contact_title?: string;
    company_name?: string;
    company_website?: string;
    industry?: string;
  } | null;
  lead_data?: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
    phone_number?: string;
    company_name?: string;
    website?: string;
    location?: string;
    custom_fields?: Record<string, any>;
    linkedin_profile?: string;
    company_url?: string;
    is_unsubscribed?: boolean;
    unsubscribed_client_id_map?: Record<string, any>;
  };
}

interface Campaign {
  id: string;
  name: string;
}

interface LeadResponseItem {
  campaign_lead_map_id: string;
  lead_category_id: string | null;
  status: string;
  created_at: string;
  lead: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
    company_name?: string;
    custom_fields?: Record<string, any>;
  };
  contact_info?: {
    contact_id: string;
    company_id?: number;
    contact_first_name?: string;
    contact_last_name?: string;
    contact_title?: string;
    company_name?: string;
    company_website?: string;
    industry?: string;
  } | null;
}

interface CampaignSequence {
  id: string;
  name: string;
  sequence_variants: {
    variant_label: string;
    subject: string;
    email_body: string;
  }[];
  variables?: {
    name: string;
    default_value: string;
  }[];
}

interface LeadsTabProps {
  onContactSelect?: (contactId: number) => void;
}

const LeadsTab: React.FC<LeadsTabProps> = ({ onContactSelect }) => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [showAddLeadDialog, setShowAddLeadDialog] = useState(false);
  const [newLead, setNewLead] = useState({
    email: '',
    first_name: '',
    last_name: '',
    company_name: ''
  });
  const [isAddingLead, setIsAddingLead] = useState(false);
  const [showEmailPreviewDialog, setShowEmailPreviewDialog] = useState(false);
  const [previewLead, setPreviewLead] = useState<Lead | null>(null);
  
  // Selection and deletion state
  const [selectedLeads, setSelectedLeads] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false);
  const [showBulkDeleteConfirmDialog, setShowBulkDeleteConfirmDialog] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState<Lead | null>(null);
  const [isDeletingLead, setIsDeletingLead] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // Sequence preview state
  const [selectedSequenceCampaignId, setSelectedSequenceCampaignId] = useState<string>('all');
  const [currentSequence, setCurrentSequence] = useState<CampaignSequence | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<string>('');
  const [loadingSequence, setLoadingSequence] = useState(false);
  const [sequenceVariables, setSequenceVariables] = useState<Record<string, string>>({});

  // State for lead info dialog
  const [showLeadInfoDialog, setShowLeadInfoDialog] = useState(false);
  const [selectedLeadInfo, setSelectedLeadInfo] = useState<Lead | null>(null);

  // Search and sorting state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    fetchCampaigns();
    
    // Check if a campaign was selected from the campaigns tab
    const storedCampaignId = localStorage.getItem('selectedCampaignId');
    if (storedCampaignId) {
      setSelectedCampaignId(storedCampaignId);
      localStorage.removeItem('selectedCampaignId'); // Clear after use
    }
  }, []);

  useEffect(() => {
    if (selectedCampaignId) {
      fetchLeads(selectedCampaignId);
    }
  }, [selectedCampaignId]);

  // Reset selection when campaign changes
  useEffect(() => {
    setSelectedLeads(new Set());
    setSelectAll(false);
  }, [selectedCampaignId]);

  // Reset search when campaign changes
  useEffect(() => {
    setSearchTerm('');
  }, [selectedCampaignId]);

  // Update selectAll state based on individual selections
  useEffect(() => {
    const visibleLeads = getFilteredAndSortedLeads();
    if (visibleLeads.length > 0) {
      const allSelected = visibleLeads.every(lead => selectedLeads.has(lead.id));
      setSelectAll(allSelected);
    } else {
      setSelectAll(false);
    }
  }, [selectedLeads, leads, searchTerm, sortOrder]);

  // Load sequence data when sequence campaign changes
  useEffect(() => {
    if (selectedSequenceCampaignId && selectedSequenceCampaignId !== 'all') {
      loadCampaignSequence(selectedSequenceCampaignId);
    } else {
      setCurrentSequence(null);
      setSelectedVariant('');
      setSequenceVariables({});
    }
  }, [selectedSequenceCampaignId]);

  const fetchCampaigns = async () => {
    try {
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      
      const data = await response.json();
      if (data && data.campaigns) {
        setCampaigns(data.campaigns);
        
        // If we have campaigns but none selected, select the first one
        if (data.campaigns.length > 0 && !selectedCampaignId) {
          setSelectedCampaignId(data.campaigns[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error(`Failed to fetch campaigns: ${(error as Error).message}`);
    }
  };

  const fetchLeads = async (campaignId: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/leads`);
      if (!response.ok) {
        throw new Error(`Failed to fetch leads: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Leads data:`, data);
      
      if (data && Array.isArray(data)) {
        // Map the nested lead structure correctly
        const formattedLeads = data.map((item: LeadResponseItem) => ({
          id: item.campaign_lead_map_id,
          email: item.lead?.email || '',
          first_name: item.lead?.first_name || '',
          last_name: item.lead?.last_name || '',
          company_name: item.lead?.company_name || '',
          status: item.status,
          added_at: item.created_at,
          custom_fields: item.lead?.custom_fields,
          contact_info: item.contact_info || null,
          // Store the full lead data for info display
          lead_data: item.lead
        }));
        setLeads(formattedLeads);
      } else {
        setLeads([]);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast.error(`Failed to fetch leads: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadCampaignSequence = async (campaignId: string) => {
    setLoadingSequence(true);
    try {
      // First try to fetch from the training data API (campaign sequence)
      const response = await fetch(`/api/campaigns/${campaignId}/sequence`);
      
      if (response.ok) {
        const sequenceData = await response.json();
        setCurrentSequence(sequenceData);
        
        if (sequenceData?.sequence_variants?.length) {
          const firstVariant = sequenceData.sequence_variants[0];
          setSelectedVariant(firstVariant.variant_label);
          
          // Initialize variables for the lead
          const allVars = extractVariables(firstVariant.subject, firstVariant.email_body);
          const initialValues: Record<string, string> = {};
          
          allVars.forEach(variable => {
            if (previewLead) {
              initialValues[variable] = getVariableValue(variable, previewLead);
            } else {
              initialValues[variable] = sequenceData.variables?.find((v: any) => v.name === variable)?.default_value || `[${variable}]`;
            }
          });
          
          setSequenceVariables(initialValues);
        }
      } else {
        // Fallback: create a simple sequence from campaign data
        const campaign = campaigns.find(c => c.id === campaignId);
        if (campaign) {
          setCurrentSequence({
            id: campaignId,
            name: campaign.name,
            sequence_variants: [{
              variant_label: '1',
              subject: 'Default Subject',
              email_body: 'Default email content for this campaign.'
            }]
          });
          setSelectedVariant('1');
          setSequenceVariables({});
        }
      }
    } catch (error) {
      console.error('Error loading campaign sequence:', error);
      toast.error('Failed to load campaign sequence');
    } finally {
      setLoadingSequence(false);
    }
  };

  const extractVariables = (subject: string, body: string): string[] => {
    const text = `${subject} ${body}`;
    const matches = text.match(/{{\s*([^}]+)\s*}}/g) || [];
    return matches
      .map(match => match.replace(/^{{\s*|\s*}}$/g, ''))
      .filter((value, index, self) => self.indexOf(value) === index);
  };

  const getVariableValue = (variable: string, lead: Lead): string => {
    switch (variable) {
      case 'first_name':
        return lead.first_name || '';
      case 'last_name':
        return lead.last_name || '';
      case 'email':
        return lead.email || '';
      case 'company_name':
        return lead.company_name || '';
      default:
        return `[${variable}]`;
    }
  };

  const replaceVariables = (template: string, variables: Record<string, string>): string => {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, value);
    });
    return result;
  };

  const handleAddLead = async () => {
    if (!selectedCampaignId) {
      toast.error('Please select a campaign first');
      return;
    }

    if (!newLead.email) {
      toast.error('Email is required');
      return;
    }

    setIsAddingLead(true);
    try {
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaignId}/leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leads: [newLead]
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add lead: ${response.status}`);
      }

      toast.success('Lead added successfully');
      setNewLead({
        email: '',
        first_name: '',
        last_name: '',
        company_name: ''
      });
      setShowAddLeadDialog(false);
      fetchLeads(selectedCampaignId); // Refresh the list
    } catch (error) {
      console.error('Error adding lead:', error);
      toast.error(`Failed to add lead: ${(error as Error).message}`);
    } finally {
      setIsAddingLead(false);
    }
  };

  const handleSelectAll = (checked: boolean | 'indeterminate') => {
    const visibleLeads = getFilteredAndSortedLeads();
    if (checked) {
      setSelectedLeads(new Set(visibleLeads.map(lead => lead.id)));
    } else {
      // Remove only the visible leads from selection, keep others
      const newSelectedLeads = new Set(selectedLeads);
      visibleLeads.forEach(lead => newSelectedLeads.delete(lead.id));
      setSelectedLeads(newSelectedLeads);
    }
    setSelectAll(!!checked);
  };

  const handleSelectLead = (leadId: string, checked: boolean) => {
    const newSelectedLeads = new Set(selectedLeads);
    if (checked) {
      newSelectedLeads.add(leadId);
    } else {
      newSelectedLeads.delete(leadId);
    }
    setSelectedLeads(newSelectedLeads);
  };

  const openDeleteConfirmDialog = (lead: Lead) => {
    setLeadToDelete(lead);
    setShowDeleteConfirmDialog(true);
  };

  const handleDeleteLead = async () => {
    if (!leadToDelete || !selectedCampaignId) return;

    setIsDeletingLead(true);
    try {
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaignId}/leads/${leadToDelete.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete lead: ${response.status}`);
      }

      toast.success('Lead deleted successfully');
      setShowDeleteConfirmDialog(false);
      setLeadToDelete(null);
      
      // Remove from selection if it was selected
      const newSelectedLeads = new Set(selectedLeads);
      newSelectedLeads.delete(leadToDelete.id);
      setSelectedLeads(newSelectedLeads);
      
      fetchLeads(selectedCampaignId); // Refresh the list
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error(`Failed to delete lead: ${(error as Error).message}`);
    } finally {
      setIsDeletingLead(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedLeads.size === 0 || !selectedCampaignId) return;

    setIsBulkDeleting(true);
    try {
      const response = await fetch(`/api/smartlead/campaigns/${selectedCampaignId}/leads/bulk-delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          leadIds: Array.from(selectedLeads)
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to bulk delete leads: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Successfully deleted ${result.successCount} leads`);
      } else {
        toast.warning(`Deleted ${result.successCount} leads, ${result.errorCount} failed`);
      }

      setShowBulkDeleteConfirmDialog(false);
      setSelectedLeads(new Set()); // Clear selection
      fetchLeads(selectedCampaignId); // Refresh the list
    } catch (error) {
      console.error('Error bulk deleting leads:', error);
      toast.error(`Failed to bulk delete leads: ${(error as Error).message}`);
    } finally {
      setIsBulkDeleting(false);
    }
  };

  const handleLeadClick = (lead: Lead) => {
    if (!onContactSelect) {
      console.warn('Cannot navigate to contact: missing callback');
      return;
    }

    // Only navigate if we have contact_info with contact_id
    if (lead.contact_info?.contact_id) {
      console.log(`Navigating to contact: ${lead.contact_info.contact_id}`);
      onContactSelect(parseInt(lead.contact_info.contact_id));
    } else {
      toast.info('This lead is not linked to any contact in the database');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const openEmailPreview = (lead: Lead) => {
    setPreviewLead(lead);
    setSelectedSequenceCampaignId('all'); // Reset sequence selection
    setShowEmailPreviewDialog(true);
  };

  const handleVariantChange = (variantLabel: string) => {
    if (!currentSequence) return;
    
    const variant = currentSequence.sequence_variants.find(v => v.variant_label === variantLabel);
    if (variant && previewLead) {
      setSelectedVariant(variantLabel);
      
      // Update variables for the new variant
      const allVars = extractVariables(variant.subject, variant.email_body);
      const newValues: Record<string, string> = {};
      
      allVars.forEach(variable => {
        newValues[variable] = getVariableValue(variable, previewLead);
      });
      
      setSequenceVariables(newValues);
    }
  };

  const openLeadInfo = (lead: Lead) => {
    setSelectedLeadInfo(lead);
    setShowLeadInfoDialog(true);
  };

  // Filter and sort leads
  const getFilteredAndSortedLeads = () => {
    let filteredLeads = leads;

    // Apply search filter
    if (searchTerm.trim()) {
      filteredLeads = leads.filter(lead => {
        const fullName = `${lead.first_name || ''} ${lead.last_name || ''}`.toLowerCase();
        const email = (lead.email || '').toLowerCase();
        const company = (lead.company_name || '').toLowerCase();
        const dbName = `${lead.contact_info?.contact_first_name || ''} ${lead.contact_info?.contact_last_name || ''}`.toLowerCase();
        const searchLower = searchTerm.toLowerCase();
        
        return fullName.includes(searchLower) || 
               email.includes(searchLower) || 
               company.includes(searchLower) ||
               dbName.includes(searchLower);
      });
    }

    // Sort by name
    filteredLeads.sort((a, b) => {
      const nameA = `${a.first_name || ''} ${a.last_name || ''}`.trim().toLowerCase();
      const nameB = `${b.first_name || ''} ${b.last_name || ''}`.trim().toLowerCase();
      
      if (sortOrder === 'asc') {
        return nameA.localeCompare(nameB);
      } else {
        return nameB.localeCompare(nameA);
      }
    });

    return filteredLeads;
  };

  const filteredAndSortedLeads = getFilteredAndSortedLeads();

  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>Campaign Leads</CardTitle>
            <div className="flex space-x-2">
              <Select value={selectedCampaignId} onValueChange={setSelectedCampaignId}>
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder="Select a campaign" />
                </SelectTrigger>
                <SelectContent>
                  {campaigns.map((campaign) => (
                    <SelectItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {selectedLeads.size > 0 && (
                <Button 
                  variant="destructive"
                  onClick={() => setShowBulkDeleteConfirmDialog(true)}
                  disabled={isBulkDeleting}
                >
                  {isBulkDeleting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash className="h-4 w-4 mr-2" />
                      Delete Selected ({selectedLeads.size})
                    </>
                  )}
                </Button>
              )}
              
              <Dialog open={showAddLeadDialog} onOpenChange={setShowAddLeadDialog}>
                <DialogTrigger asChild>
                  <Button className="flex items-center" disabled={!selectedCampaignId}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Lead
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Lead</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          placeholder="Email address"
                          value={newLead.email}
                          onChange={(e) => setNewLead({...newLead, email: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="first_name">First Name</Label>
                        <Input
                          id="first_name"
                          placeholder="First name"
                          value={newLead.first_name}
                          onChange={(e) => setNewLead({...newLead, first_name: e.target.value})}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="last_name">Last Name</Label>
                        <Input
                          id="last_name"
                          placeholder="Last name"
                          value={newLead.last_name}
                          onChange={(e) => setNewLead({...newLead, last_name: e.target.value})}
                        />
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowAddLeadDialog(false)}
                      disabled={isAddingLead}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleAddLead}
                      disabled={isAddingLead}
                    >
                      {isAddingLead ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Adding...
                        </>
                      ) : 'Add Lead'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
          
          {/* Search and Filter Controls */}
          <div className="flex items-center space-x-2 mt-3">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by name, email, or company..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSortOrder}
              className="flex items-center space-x-1"
              title={`Sort by name ${sortOrder === 'asc' ? '(A-Z)' : '(Z-A)'}`}
            >
              {sortOrder === 'asc' ? (
                <ArrowUp className="h-4 w-4" />
              ) : (
                <ArrowDown className="h-4 w-4" />
              )}
              <span className="hidden sm:inline">Name</span>
            </Button>
            {(searchTerm || filteredAndSortedLeads.length !== leads.length) && (
              <div className="text-sm text-gray-500">
                Showing {filteredAndSortedLeads.length} of {leads.length} leads
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px] rounded-md border">
            {!selectedCampaignId ? (
              <div className="flex items-center justify-center h-40 text-gray-500">
                Please select a campaign to view leads
              </div>
            ) : loading ? (
              <div className="flex items-center justify-center h-40">
                <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : filteredAndSortedLeads.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                {searchTerm ? (
                  <>
                    <p>No leads found matching "{searchTerm}"</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setSearchTerm('')}
                      className="mt-2"
                    >
                      Clear search
                    </Button>
                  </>
                ) : (
                  <p>No leads found in this campaign</p>
                )}
              </div>
            ) : (
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                        disabled={filteredAndSortedLeads.length === 0}
                      />
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Email</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Company</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Title</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Database Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Lead Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Added</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndSortedLeads.map((lead) => (
                    <tr 
                      key={lead.id} 
                      className="border-b hover:bg-gray-50 cursor-pointer"
                      onClick={(e) => {
                        // Don't trigger row click if clicking on checkbox or action buttons
                        const target = e.target as HTMLElement;
                        if (
                          target.closest('input[type="checkbox"]') ||
                          target.closest('button') ||
                          target.closest('[role="checkbox"]')
                        ) {
                          return;
                        }
                        handleLeadClick(lead);
                      }}
                    >
                      <td className="px-4 py-3 text-sm">
                        <Checkbox
                          checked={selectedLeads.has(lead.id)}
                          onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectLead(lead.id, !!checked)}
                        />
                      </td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">
                        <div className="flex items-center">
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {lead.first_name || ''} {lead.last_name || ''}
                            </span>
                            {lead.contact_info?.contact_first_name && (
                              <span className="text-xs text-gray-500">
                                DB: {lead.contact_info.contact_first_name} {lead.contact_info.contact_last_name}
                              </span>
                            )}
                          </div>
                          {onContactSelect && lead.contact_info?.contact_id && (
                            <ExternalLink className="h-3 w-3 ml-2 text-blue-500" />
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">{lead.email}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {lead.contact_info?.company_name || lead.company_name || 'N/A'}
                          </span>
                          {lead.contact_info?.industry && (
                            <span className="text-xs text-gray-500">{lead.contact_info.industry}</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {lead.contact_info?.contact_title || 'N/A'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        {lead.contact_info ? (
                          <div className="flex flex-col">
                            <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 inline-block mb-1">
                              ✓ In Database
                            </span>
                            <span className="text-xs text-gray-600">
                              ID: {lead.contact_info.contact_id}
                            </span>
                          </div>
                        ) : (
                          <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                            Not in Database
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          lead.status === 'STARTED' ? 'bg-green-100 text-green-800' :
                          lead.status === 'PAUSED' ? 'bg-yellow-100 text-yellow-800' :
                          lead.status === 'COMPLETED' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {lead.status || 'Pending'}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">{formatDate(lead.added_at)}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              openLeadInfo(lead);
                            }}
                            title="View lead details"
                          >
                            <Info className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              openEmailPreview(lead);
                            }}
                            title="Preview email"
                          >
                            <Mail className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              openDeleteConfirmDialog(lead);
                            }}
                            disabled={isDeletingLead}
                            title="Delete lead"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Individual Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirmDialog} onOpenChange={setShowDeleteConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this lead?</p>
            {leadToDelete && (
              <div className="mt-2 p-3 bg-gray-100 rounded-md">
                <p><strong>Name:</strong> {leadToDelete.first_name} {leadToDelete.last_name}</p>
                <p><strong>Email:</strong> {leadToDelete.email}</p>
              </div>
            )}
            <p className="mt-2 text-sm text-red-600">This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteConfirmDialog(false)}
              disabled={isDeletingLead}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeleteLead}
              disabled={isDeletingLead}
            >
              {isDeletingLead ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : 'Delete Lead'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Confirmation Dialog */}
      <Dialog open={showBulkDeleteConfirmDialog} onOpenChange={setShowBulkDeleteConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Delete</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete <strong>{selectedLeads.size}</strong> selected leads?</p>
            <p className="mt-2 text-sm text-red-600">This action cannot be undone.</p>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowBulkDeleteConfirmDialog(false)}
              disabled={isBulkDeleting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isBulkDeleting}
            >
              {isBulkDeleting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : `Delete ${selectedLeads.size} Leads`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Enhanced Email Preview Dialog with Sequence Functionality */}
      <Dialog open={showEmailPreviewDialog} onOpenChange={setShowEmailPreviewDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Email Preview with Metadata & Sequence Selection</DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden">
            {previewLead && (
              <Tabs defaultValue="metadata" className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="metadata">Metadata</TabsTrigger>
                  <TabsTrigger value="original">Original Email</TabsTrigger>
                  <TabsTrigger value="sequence">Sequence Preview</TabsTrigger>
                </TabsList>
                
                <TabsContent value="metadata" className="flex-1 overflow-auto">
                  <div className="space-y-4">
                    {/* Lead Metadata */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Lead Metadata</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Name</Label>
                            <p className="text-sm text-gray-900">{previewLead.first_name} {previewLead.last_name}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Email</Label>
                            <p className="text-sm text-gray-900">{previewLead.email}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Company</Label>
                            <p className="text-sm text-gray-900">{previewLead.company_name || 'N/A'}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Lead ID</Label>
                            <p className="text-sm text-gray-900">{previewLead.id}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Status</Label>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              previewLead.status === 'STARTED' ? 'bg-green-100 text-green-800' :
                              previewLead.status === 'PAUSED' ? 'bg-yellow-100 text-yellow-800' :
                              previewLead.status === 'COMPLETED' ? 'bg-blue-100 text-blue-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {previewLead.status || 'Pending'}
                            </span>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Date Added</Label>
                            <p className="text-sm text-gray-900">{formatDate(previewLead.added_at)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Campaign Metadata */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Campaign Metadata</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Campaign ID</Label>
                            <p className="text-sm text-gray-900">{selectedCampaignId}</p>
                          </div>
                          <div>
                            <Label className="text-sm font-medium text-gray-600">Campaign Name</Label>
                            <p className="text-sm text-gray-900">
                              {campaigns.find(c => c.id === selectedCampaignId)?.name || 'N/A'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Database Connection Status */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Database Connection</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {previewLead.contact_info ? (
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <span className="px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                                ✓ Connected to Database
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <Label className="text-sm font-medium text-gray-600">Contact ID</Label>
                                <p className="text-sm text-gray-900">{previewLead.contact_info.contact_id}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-gray-600">Company ID</Label>
                                <p className="text-sm text-gray-900">{previewLead.contact_info.company_id || 'N/A'}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-gray-600">Title</Label>
                                <p className="text-sm text-gray-900">{previewLead.contact_info.contact_title || 'N/A'}</p>
                              </div>
                              <div>
                                <Label className="text-sm font-medium text-gray-600">Industry</Label>
                                <p className="text-sm text-gray-900">{previewLead.contact_info.industry || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center py-4">
                            <span className="px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                              Not Connected to Database
                            </span>
                            <p className="text-sm text-gray-500 mt-2">
                              This lead is not linked to any contact in the database
                            </p>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Smartlead Lead Details */}
                    {previewLead.lead_data && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Smartlead Lead Details</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium text-gray-600">Smartlead ID</Label>
                              <p className="text-sm text-gray-900">{previewLead.lead_data.id}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-600">Phone</Label>
                              <p className="text-sm text-gray-900">{previewLead.lead_data.phone_number || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-600">Website</Label>
                              <p className="text-sm text-gray-900">{previewLead.lead_data.website || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-600">Location</Label>
                              <p className="text-sm text-gray-900">{previewLead.lead_data.location || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-600">LinkedIn</Label>
                              <p className="text-sm text-gray-900">{previewLead.lead_data.linkedin_profile || 'N/A'}</p>
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-600">Unsubscribed</Label>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                previewLead.lead_data.is_unsubscribed ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                              }`}>
                                {previewLead.lead_data.is_unsubscribed ? 'Yes' : 'No'}
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Email Variables/Custom Fields */}
                    {previewLead.custom_fields && Object.keys(previewLead.custom_fields).length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Email Variables & Custom Fields</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {Object.entries(previewLead.custom_fields).map(([key, value]) => (
                              <div key={key} className="border-l-4 border-blue-200 pl-3">
                                <Label className="text-sm font-medium text-gray-600 capitalize">
                                  {key.replace(/_/g, ' ')}
                                </Label>
                                <div className="text-sm text-gray-900 mt-1">
                                  {key === 'subject' && (
                                    <div className="font-medium text-blue-800">{String(value)}</div>
                                  )}
                                  {key === 'html_body' && typeof value === 'string' ? (
                                    <div className="max-h-32 overflow-y-auto p-2 bg-gray-50 rounded border">
                                      <div className="text-xs" dangerouslySetInnerHTML={{ __html: value.substring(0, 500) + (value.length > 500 ? '...' : '') }} />
                                    </div>
                                  ) : key !== 'subject' && key !== 'html_body' && (
                                    <p className="text-gray-700">{typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}</p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Smartlead Custom Fields */}
                    {previewLead.lead_data?.custom_fields && Object.keys(previewLead.lead_data.custom_fields).length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Smartlead Custom Variables</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {Object.entries(previewLead.lead_data.custom_fields).map(([key, value]) => (
                              <div key={key} className="border-l-4 border-green-200 pl-3">
                                <Label className="text-sm font-medium text-gray-600 capitalize">
                                  {key.replace(/_/g, ' ')}
                                </Label>
                                <div className="text-sm text-gray-900 mt-1">
                                  <p className="text-gray-700">{typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="original" className="flex-1 overflow-auto">
                  <div className="space-y-4">
                    <div className="bg-gray-100 p-4 rounded-md border">
                      <div className="mb-2">
                        <span className="font-semibold text-gray-700">To:</span> {previewLead.email}
                      </div>
                      {previewLead.custom_fields?.subject && (
                        <div className="mb-2">
                          <span className="font-semibold text-gray-700">Subject:</span> {previewLead.custom_fields.subject}
                        </div>
                      )}
                    </div>
                    
                    <div className="border rounded-md overflow-hidden shadow-sm">
                      <div className="bg-white p-6">
                        {previewLead.custom_fields?.html_body ? (
                          <div 
                            className="prose prose-sm max-w-none"
                            style={{ 
                              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' 
                            }}
                            dangerouslySetInnerHTML={{ 
                              __html: previewLead.custom_fields.html_body
                            }} 
                          />
                        ) : (
                          <div className="text-gray-500 italic">No email content available</div>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="sequence" className="flex-1 overflow-auto">
                  <div className="space-y-4">
                    {/* Campaign Selector */}
                    <div className="bg-gray-50 p-4 rounded-md">
                      <Label className="text-sm font-medium mb-2 block">Select Campaign for Sequence</Label>
                      <Select value={selectedSequenceCampaignId} onValueChange={setSelectedSequenceCampaignId}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Choose a campaign" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">No Campaign Selected</SelectItem>
                          {campaigns.map((campaign) => (
                            <SelectItem key={campaign.id} value={campaign.id}>
                              {campaign.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {loadingSequence && (
                      <div className="flex items-center justify-center py-8">
                        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                        <span className="ml-2 text-gray-500">Loading sequence...</span>
                      </div>
                    )}

                    {currentSequence && !loadingSequence && (
                      <div className="space-y-4">
                        {/* Variant Selector */}
                        {currentSequence.sequence_variants.length > 1 && (
                          <div>
                            <Label className="text-sm font-medium mb-2 block">Sequence Variant</Label>
                            <Select value={selectedVariant} onValueChange={handleVariantChange}>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select variant" />
                              </SelectTrigger>
                              <SelectContent>
                                {currentSequence.sequence_variants.map((variant) => (
                                  <SelectItem key={variant.variant_label} value={variant.variant_label}>
                                    Variant {variant.variant_label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {selectedVariant && (
                          <>
                            {/* Variables Editor */}
                            {Object.keys(sequenceVariables).length > 0 && (
                              <div className="bg-gray-50 p-4 rounded-md">
                                <Label className="text-sm font-medium mb-2 block">Template Variables</Label>
                                <div className="grid grid-cols-2 gap-4">
                                  {Object.entries(sequenceVariables).map(([variable, value]) => (
                                    <div key={variable}>
                                      <Label className="text-xs text-gray-600">{variable}</Label>
                                      <Input
                                        value={value}
                                        onChange={(e) => setSequenceVariables(prev => ({
                                          ...prev,
                                          [variable]: e.target.value
                                        }))}
                                        className="mt-1"
                                      />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Preview */}
                            <div className="bg-gray-100 p-4 rounded-md border">
                              <div className="mb-2">
                                <span className="font-semibold text-gray-700">To:</span> {previewLead.email}
                              </div>
                              <div className="mb-2">
                                <span className="font-semibold text-gray-700">Subject:</span> {
                                  replaceVariables(
                                    currentSequence.sequence_variants.find(v => v.variant_label === selectedVariant)?.subject || '',
                                    sequenceVariables
                                  )
                                }
                              </div>
                            </div>
                            
                            <div className="border rounded-md overflow-hidden shadow-sm">
                              <div className="bg-white p-6">
                                <div 
                                  className="prose prose-sm max-w-none"
                                  style={{ 
                                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' 
                                  }}
                                  dangerouslySetInnerHTML={{ 
                                    __html: replaceVariables(
                                      currentSequence.sequence_variants.find(v => v.variant_label === selectedVariant)?.email_body || '',
                                      sequenceVariables
                                    )
                                  }} 
                                />
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    )}

                    {selectedSequenceCampaignId !== 'all' && !currentSequence && !loadingSequence && (
                      <div className="text-center py-8 text-gray-500">
                        <p>No sequence data available for this campaign</p>
                        <p className="text-sm">Try selecting a different campaign</p>
                      </div>
                    )}

                    {selectedSequenceCampaignId === 'all' && (
                      <div className="text-center py-8 text-gray-500">
                        <p>Please select a campaign to view sequence preview</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </div>
          
          <DialogFooter>
            <Button onClick={() => setShowEmailPreviewDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Lead Info Dialog */}
      <Dialog open={showLeadInfoDialog} onOpenChange={setShowLeadInfoDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Lead Information</DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-auto">
            {selectedLeadInfo && (
              <div className="space-y-6">
                {/* Basic Lead Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Name</Label>
                        <p className="text-sm text-gray-900">{selectedLeadInfo.first_name} {selectedLeadInfo.last_name}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Email</Label>
                        <p className="text-sm text-gray-900">{selectedLeadInfo.email}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Company</Label>
                        <p className="text-sm text-gray-900">{selectedLeadInfo.company_name || 'N/A'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Status</Label>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          selectedLeadInfo.status === 'STARTED' ? 'bg-green-100 text-green-800' :
                          selectedLeadInfo.status === 'PAUSED' ? 'bg-yellow-100 text-yellow-800' :
                          selectedLeadInfo.status === 'COMPLETED' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedLeadInfo.status || 'Pending'}
                        </span>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Date Added</Label>
                        <p className="text-sm text-gray-900">{formatDate(selectedLeadInfo.added_at)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-600">Lead ID</Label>
                        <p className="text-sm text-gray-900">{selectedLeadInfo.id}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Complete Lead Data from Smartlead */}
                {selectedLeadInfo.lead_data && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Smartlead Lead Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Smartlead ID</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.id}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Phone Number</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.phone_number || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Website</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.website || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Location</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.location || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">LinkedIn Profile</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.linkedin_profile || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Company URL</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.lead_data.company_url || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Unsubscribed</Label>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            selectedLeadInfo.lead_data.is_unsubscribed ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {selectedLeadInfo.lead_data.is_unsubscribed ? 'Yes' : 'No'}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
                
                {/* Database Contact Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Database Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedLeadInfo.contact_info ? (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Contact ID</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.contact_id}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Company ID</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.company_id || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">DB First Name</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.contact_first_name || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">DB Last Name</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.contact_last_name || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Title</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.contact_title || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">DB Company Name</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.company_name || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Company Website</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.company_website || 'N/A'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-600">Industry</Label>
                          <p className="text-sm text-gray-900">{selectedLeadInfo.contact_info.industry || 'N/A'}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <p>This lead is not linked to any contact in the database</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {/* Custom Fields / Variables */}
                {selectedLeadInfo.custom_fields && Object.keys(selectedLeadInfo.custom_fields).length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Custom Fields & Variables</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(selectedLeadInfo.custom_fields).map(([key, value]) => (
                          <div key={key} className="border-b pb-2">
                            <Label className="text-sm font-medium text-gray-600 capitalize">
                              {key.replace(/_/g, ' ')}
                            </Label>
                            <div className="text-sm text-gray-900 mt-1">
                              {typeof value === 'string' && value.length > 100 ? (
                                <div className="max-h-32 overflow-y-auto p-2 bg-gray-50 rounded border">
                                  <pre className="whitespace-pre-wrap text-xs">{value}</pre>
                                </div>
                              ) : (
                                <p>{typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Lead Data Custom Fields */}
                {selectedLeadInfo.lead_data?.custom_fields && Object.keys(selectedLeadInfo.lead_data.custom_fields).length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Smartlead Custom Fields</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(selectedLeadInfo.lead_data.custom_fields).map(([key, value]) => (
                          <div key={key} className="border-b pb-2">
                            <Label className="text-sm font-medium text-gray-600 capitalize">
                              {key.replace(/_/g, ' ')}
                            </Label>
                            <div className="text-sm text-gray-900 mt-1">
                              {typeof value === 'string' && value.length > 100 ? (
                                <div className="max-h-32 overflow-y-auto p-2 bg-gray-50 rounded border">
                                  <pre className="whitespace-pre-wrap text-xs">{value}</pre>
                                </div>
                              ) : (
                                <p>{typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button onClick={() => setShowLeadInfoDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LeadsTab; 