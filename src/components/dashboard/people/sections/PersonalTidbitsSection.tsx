"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Contact } from '../types';

interface PersonalTidbitsSectionProps {
  contact?: Contact;
}

const PersonalTidbitsSection: React.FC<PersonalTidbitsSectionProps> = ({ contact }) => {
  if (!Array.isArray(contact?.personal_tidbits) || contact.personal_tidbits.length === 0) return null;
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Personal Tidbits</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {contact.personal_tidbits.map((item, index) => (
            <li key={index} className="relative pl-6 text-sm">
              <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
              {typeof item === 'string' ? item : item.tidbit || item.description || JSON.stringify(item)}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};

export default PersonalTidbitsSection; 