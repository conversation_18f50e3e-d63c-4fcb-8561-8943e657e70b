import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import { ContactProcessingState } from '../../types/processing'
import { CONTACT_SEARCH_SYSTEM_PROMPT, CONTACT_SEARCH_USER_TEMPLATE_FUNCTION } from '../prompts'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'

interface ContactData extends EntityData {
  contact_id: number
  first_name: string
  last_name: string
  title?: string
  email: string
  linkedin_url?: string
  company_id?: number
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  processing_state: ContactProcessingState
  searched?: boolean
}

export class ContactSearchProcessor extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    super('ContactSearch', options)
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Create LLM provider with Perplexity as primary and OpenAI as fallback
    this.llmProvider = LLMFactory.createWithFallback(
      'perplexity',
      'openai',
      loggerAdapter,
      process.env.PERPLEXITY_API_KEY,
      process.env.OPENAI_API_KEY,
      { 
        temperature: 0.2,
        maxTokens: 8000,
        model: 'r1-1776'  // Using latest Perplexity model for research
      }
    );
  }

  async getUnprocessedEntities(): Promise<EntityData[]> {
    const contacts = await this.getContactsForOSINT(
      this.options.limit,
      this.options.singleId
    )
    
    return contacts.map(contact => ({
      id: contact.contact_id as number,
      ...contact
    }))
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const contact = entity as ContactData
    
    try {
      this.log('info', `Starting OSINT research for contact ${contact.contact_id}: ${contact.first_name} ${contact.last_name}`)
      
      // Set status to running
      await this.updateContactOSINTStatus(contact.contact_id, 'running')
      
      // Perform OSINT research
      const osintResult = await this.performOSINTResearch(contact)
      
      if (osintResult.success) {
        this.log('info', `OSINT research completed for ${contact.first_name} ${contact.last_name}`)
        return { success: true }
      } else {
        return { success: false, error: osintResult.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in OSINT research for contact ${contact.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // OSINT research completed successfully
      await this.updateContactOSINTStatus(entityId, 'completed')
    } else {
      // OSINT research failed
      await this.updateContactOSINTStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Perform OSINT research on a contact using Perplexity API
   */
  private async performOSINTResearch(contact: ContactData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Starting OSINT research for contact ${contact.contact_id}`)
      
      // Build the search prompt
      const prompt = await this.buildOSINTPrompt(contact)
      
      // Call Perplexity API
      this.log('debug', `Calling Perplexity API for contact ${contact.contact_id}`)
      const apiResponse = await this.callLLMAPI(prompt)
      
      if (!apiResponse.success) {
        return { success: false, error: apiResponse.error }
      }
      
      // Parse the response to extract profile data
      const profileData = this.parseOSINTResponse(apiResponse.content!)
      if (!profileData) {
        return { success: false, error: 'Failed to parse OSINT response' }
      }
      
      // Save the results to database
      const saveResult = await this.saveOSINTResults(contact.contact_id, profileData, prompt, apiResponse.tokens!)
      
      if (saveResult.success) {
        this.log('info', `OSINT research completed and saved for contact ${contact.contact_id}`)
        return { success: true }
      } else {
        return { success: false, error: saveResult.error }
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in OSINT research: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Build OSINT search prompt for contact
   */
  private async buildOSINTPrompt(contact: ContactData): Promise<{ system: string; user: string }> {
    const userPrompt = CONTACT_SEARCH_USER_TEMPLATE_FUNCTION({
      first_name: contact.first_name,
      last_name: contact.last_name,
      email: contact.email,
      title: contact.title,
      company_name: contact.company_name,
      company_website: contact.company_website,
      linkedin_url: contact.linkedin_url,
      industry: contact.industry,
      contact_country: contact.contact_country,
      extra_attrs: null
    })

    return { 
      system: CONTACT_SEARCH_SYSTEM_PROMPT, 
      user: userPrompt 
    }
  }

  /**
   * Call LLM API for OSINT research
   */
  private async callLLMAPI(
    prompt: { system: string; user: string }
  ): Promise<{ success: boolean; content?: string; tokens?: number; error?: string }> {
    try {
      const messages: LLMMessage[] = [
        { role: 'system', content: prompt.system },
        { role: 'user', content: prompt.user }
      ];

      // Use the LLM provider with fallback support
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.2,
        maxTokens: 8000
      });

      if (!response.content) {
        return { success: false, error: `No content in API response from ${response.provider}` };
      }

      const tokens = response.usage?.totalTokens || 0;
      this.log('info', `API call successful using ${response.provider} (${response.model}). Tokens used: ${tokens}`);
      
      return { 
        success: true, 
        content: response.content, 
        tokens: tokens 
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `LLM API call failed: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Parse OSINT response to extract profile data
   */
  private parseOSINTResponse(content: string): { profile: string } | null {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*?"profile"[\s\S]*?\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        if (parsed.profile && typeof parsed.profile === 'string') {
          return { profile: parsed.profile }
        }
      }

      // If no JSON found, try to use the entire content as profile
      if (content.trim()) {
        return { profile: content.trim() }
      }

      return null
    } catch (error) {
      this.log('warn', `Failed to parse JSON response, using raw content`)
      return { profile: content.trim() }
    }
  }

  /**
   * Save OSINT results to database
   */
  private async saveOSINTResults(
    contactId: number, 
    profileData: { profile: string }, 
    prompt: { system: string; user: string },
    tokens: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Build input data
      const inputData = {
        contact_id: contactId,
        prompt_used: prompt,
        timestamp: new Date().toISOString()
      }

      // Build full prompt content
      const fullPrompt = `SYSTEM:\n${prompt.system}\n\nUSER:\n${prompt.user}`

      // Insert/update contact_searched_data
      const upsertSql = `
        INSERT INTO contact_searched_data (
          contact_id, profile, input_data, prompt_content, tokens_used, created_at, updated_at
        )
        VALUES ($1, $2, $3::jsonb, $4, $5, NOW(), NOW())
        ON CONFLICT (contact_id) DO UPDATE SET
          profile = EXCLUDED.profile,
          input_data = EXCLUDED.input_data,
          prompt_content = EXCLUDED.prompt_content,
          tokens_used = EXCLUDED.tokens_used,
          updated_at = NOW()
      `

      await this.query(upsertSql, [
        contactId,
        profileData.profile,
        JSON.stringify(inputData),
        fullPrompt,
        tokens
      ])

      this.log('debug', `Saved OSINT data to contact_searched_data for contact ${contactId}`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Failed to save OSINT results: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }
}