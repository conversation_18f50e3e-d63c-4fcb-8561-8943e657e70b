import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import OpenAI from 'openai';

export const dynamic = 'force-dynamic';

// Keep track of processing state
let isProcessing = false;
let currentProgress = {
  processed: 0,
  total: 0,
  logs: [] as string[],
  errors: [] as { id: number; error: string }[],
  results: [] as any[],
  lastUpdate: new Date()
};

// Add helper at the top
function isError(error: unknown): error is Error {
  return error instanceof Error;
}

function parseDate(dateStr: string): string | null {
  if (!dateStr || dateStr.toLowerCase() === 'null') return null;
  
  try {
    // Try different date formats
    const possibleFormats = [
      // Standard ISO format
      /^\d{4}-\d{2}-\d{2}/,
      // US format MM/DD/YYYY
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})/,
      // Text format like "January 1, 2024"
      /^[A-Za-z]+ \d{1,2},? \d{4}/
    ];

    for (const format of possibleFormats) {
      if (format.test(dateStr)) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
      }
    }

    // If no format matches, try direct parsing
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }

    console.log('Could not parse date:', dateStr);
    return null;
  } catch (e) {
    console.error('Date parsing error:', e, 'for date:', dateStr);
    return null;
  }
}

// Add at the top
function logWithTimestamp(message: unknown) {
  const timestamp = new Date().toISOString();
  const formattedMessage = String(message);
  console.log(`[${timestamp}] ${formattedMessage}`);
  return `[${timestamp}] ${formattedMessage}`;
}

// Start processing function
export async function POST(request: Request) {
  console.log('\n\n=== NEW REQUEST ===');
  logWithTimestamp('Received new request');
  
  try {
    const body = await request.json();
    logWithTimestamp(`Request body: ${JSON.stringify(body)}`);
    
    const { testMode = false, limit = 3, action = 'start' } = body;

    // Status check endpoint
    if (action === 'status') {
      logWithTimestamp(`Status check - isProcessing: ${isProcessing}, processed: ${currentProgress.processed}/${currentProgress.total}`);
      return NextResponse.json({
        isProcessing,
        ...currentProgress,
        success: true
      });
    }

    // Don't start if already processing
    if (isProcessing) {
      logWithTimestamp('Rejected: Already processing');
      return NextResponse.json({
        success: false,
        error: 'Processing already in progress',
        isProcessing,
        ...currentProgress
      });
    }

    logWithTimestamp(`Starting new processing job - testMode: ${testMode}, limit: ${limit}`);
    
    // Reset progress
    isProcessing = true;
    currentProgress = {
      processed: 0,
      total: 0,
      logs: [logWithTimestamp('Starting processing...')],
      errors: [],
      results: [],
      lastUpdate: new Date()
    };

    // Start background processing
    processItems(testMode, limit).catch(error => {
      console.error('Processing error:', error);
      logWithTimestamp(`Fatal error in background process: ${error.message}`);
      logWithTimestamp(error.stack || 'No stack trace available');
      isProcessing = false;
      currentProgress.logs.push(logWithTimestamp(`Fatal error: ${error.message}`));
      currentProgress.errors.push({ id: 0, error: error.message });
    });

    return NextResponse.json({
      success: true,
      message: 'Processing started',
      isProcessing,
      ...currentProgress
    });

  } catch (error: unknown) {
    console.error('Request handling error:', error);
    const errorMessage = isError(error) ? error.message : 'Unknown error';
    const errorStack = isError(error) ? error.stack : 'No stack trace available';
    
    logWithTimestamp(`Error handling request: ${errorMessage}`);
    logWithTimestamp(errorStack);
    return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
  }
}

async function processItems(testMode: boolean, limit: number) {
  logWithTimestamp('Starting processItems function');
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
  });
  logWithTimestamp('OpenAI client initialized');

  let client;
  try {
    logWithTimestamp('Connecting to database...');
    client = await pool.connect();
    logWithTimestamp('Database connected');
    
    // Get total count
    logWithTimestamp('Querying for total count...');
    const { rows: countRows } = await client.query(`
      SELECT COUNT(*) as total
      FROM deal_news 
      WHERE raw_html IS NOT NULL 
      AND news_text IS NULL
      AND processed = false;
    `);
    
    currentProgress.total = testMode ? Math.min(limit, parseInt(countRows[0].total)) : parseInt(countRows[0].total);
    logWithTimestamp(`Found ${currentProgress.total} total items to process`);

    while (currentProgress.processed < currentProgress.total) {
      // Get single item to process
      logWithTimestamp('Querying for next item to process...');
      const { rows } = await client.query(`
        SELECT id, raw_html 
        FROM deal_news 
        WHERE raw_html IS NOT NULL 
        AND news_text IS NULL
        AND news_source = 'Bisnow'
        AND processed = false
        ORDER BY id ASC
        LIMIT 1
        FOR UPDATE SKIP LOCKED;
      `);

      if (rows.length === 0) {
        logWithTimestamp('No more items to process');
        break;
      }

      const row = rows[0];
      try {
        logWithTimestamp(`\n--- Processing item ${row.id} ---`);
        currentProgress.logs.push(logWithTimestamp(`Processing item ${row.id}...`));

        // Clean HTML
        logWithTimestamp(`Cleaning HTML for item ${row.id}`);
        const cleanText = row.raw_html
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();
        logWithTimestamp(`HTML cleaned, length: ${cleanText.length} characters`);

        // Extract with GPT
        logWithTimestamp(`Calling OpenAI API for item ${row.id}`);
        const response = await openai.chat.completions.create({
          model: "gpt-4o-mini",
          messages: [
            { role: "system", content: "You extract fields from the text without summarizing or altering content." },
            { role: "user", content: `Extract the following fields from the raw text of a news article:
              1. Title: The title of the article.
              2. Date: The publication date of the article (if available, otherwise return null).
              3. Body: The full body text of the article.

              Raw Text:
              ${cleanText}

              Output format:
              Title: <title>
              Date: <date>
              Body: <body>` }
          ]
        });
        logWithTimestamp(`Received OpenAI response for item ${row.id}`);

        const content = response.choices[0]?.message?.content;
        if (!content) {
          throw new Error('No content in OpenAI response');
        }

        logWithTimestamp(`Parsing OpenAI output for item ${row.id}`);
        const [titlePart = "", rest = ""] = content.split("\nDate: ");
        const [dateStr = "", bodyPart = ""] = rest.split("\nBody: ");
        
        const title = titlePart.replace("Title: ", "").trim();
        const parsedDate = parseDate(dateStr.trim());
        const body = bodyPart.trim();
        logWithTimestamp(`Parsed content - Title length: ${title.length}, Date: ${parsedDate}, Body length: ${body.length}`);

        if (!testMode) {
          logWithTimestamp(`Updating database for item ${row.id}`);
          await client.query(`
            UPDATE deal_news 
            SET news_text = $1, 
                news_title = $2, 
                news_date = $3, 
                processed = true 
            WHERE id = $4
          `, [body, title, parsedDate, row.id]);
          logWithTimestamp(`Database updated for item ${row.id}`);
        }

        currentProgress.processed++;
        currentProgress.results.push({
          id: row.id,
          originalHtml: row.raw_html.substring(0, 500) + '...',
          cleanedText: cleanText.substring(0, 500) + '...',
          extractedContent: { title, date: parsedDate, body }
        });
        currentProgress.lastUpdate = new Date();
        logWithTimestamp(`Completed processing item ${row.id}`);

      } catch (error: unknown) {
        console.error(`Error processing item ${row.id}:`, error);
        const errorMessage = isError(error) ? error.message : 'Unknown error';
        const errorStack = isError(error) ? error.stack : 'No stack trace available';
        
        logWithTimestamp(`Error processing item ${row.id}: ${errorMessage}`);
        logWithTimestamp(errorStack);
        currentProgress.errors.push({ id: row.id, error: errorMessage });
        currentProgress.logs.push(logWithTimestamp(`Error processing item ${row.id}: ${errorMessage}`));
      }

      if (testMode && currentProgress.processed >= limit) {
        logWithTimestamp('Test mode limit reached');
        break;
      }
    }

  } catch (error: unknown) {
    console.error('Processing error:', error);
    const errorMessage = isError(error) ? error.message : 'Unknown error';
    const errorStack = isError(error) ? error.stack : 'No stack trace available';
    
    logWithTimestamp(`Processing error: ${errorMessage}`);
    logWithTimestamp(errorStack);
    throw error;
  } finally {
    if (client) {
      logWithTimestamp('Releasing database connection');
      client.release();
    }
    isProcessing = false;
    logWithTimestamp('Processing complete');
    currentProgress.logs.push(logWithTimestamp('Processing complete'));
  }
}