# News Scraping Pipeline

This project contains scripts for scraping news articles from various sources including Bisnow, PincusCo, TheRealDeal, and GlobeSt.

## Setup

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment variables:
   - Copy `.env.example` to `.env`
   - Update the values in `.env` with your configuration

3. Chrome Setup:
   - Ensure Google Chrome is installed
   - Update the Chrome paths in `.env` if different from defaults

## Scripts

### 1. fetchurl.py
Scrapes news websites to discover new article URLs and stores them in the database.

```bash
python scripts/fetchurl.py
```

### 2. fetch_news.py
Fetches the actual HTML content for discovered URLs and stores it in the database.

```bash
python scripts/fetch_news.py
```

## Environment Variables

- `DB_*`: Database connection settings
- `CHROME_*`: Chrome browser configuration
- `WAIT_TIME`: Delay between requests

## Requirements

- Python 3.8+
- Google Chrome
- PostgreSQL database
