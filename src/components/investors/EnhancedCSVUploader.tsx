'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { CheckCircle, XCircle, Upload, FileText, AlertTriangle, Eye, Database, Users, ArrowUpDown, ArrowUp, ArrowDown, Zap, TrendingUp, Activity, BarChart3, Building2, Brain, ChevronDown, ChevronUp, ArrowRight, RefreshCw, X, Clock } from 'lucide-react'
import Papa from 'papaparse'
import * as XLSX from 'xlsx'
import { ConflictUploadResult, ConflictPreviewRow } from '@/types/conflict'
import ConflictDashboard from '../conflicts/ConflictDashboard'

interface CSVRow {
  'Company': string
  'Company Website': string
  'First Name': string
  'Last Name': string
  'Job Title': string
  'Email': string
  'LinkedIn URL': string
  'Company LinkedIn': string
  'Company Address': string
  'City': string
  'State': string
  'Zip': string
  'Country': string
  'Company Phone': string
  'Industry': string
  'Capital Type': string
  'Phone Number': string
  'Linked-In Profile': string
  [key: string]: string
}

type SortColumn = keyof ConflictPreviewRow | 'status'
type SortDirection = 'asc' | 'desc' | null

export default function EnhancedCSVUploader() {
  const [file, setFile] = useState<File | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [previewData, setPreviewData] = useState<ConflictPreviewRow[]>([])
  const [loading, setLoading] = useState(false)
  const [checking, setChecking] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [result, setResult] = useState<ConflictUploadResult | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [sortColumn, setSortColumn] = useState<SortColumn | null>(null)
  const [sortDirection, setSortDirection] = useState<SortDirection>(null)
  const [activeTab, setActiveTab] = useState('upload')
  const [currentConflictCount, setCurrentConflictCount] = useState(0)

  const [csvHeaders, setCsvHeaders] = useState<string[]>([])
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null)
  const [headerMappings, setHeaderMappings] = useState<Record<string, string>>({})
  const [mappingConfirmed, setMappingConfirmed] = useState(false)
  const [showMappingSection, setShowMappingSection] = useState(false)
  const [mappingData, setMappingData] = useState<any>(null)
  const [databaseFields, setDatabaseFields] = useState<any[]>([])
  const [loadingMapping, setLoadingMapping] = useState(false)
  const [hasInitialMapping, setHasInitialMapping] = useState(false)
  const [loadingFields, setLoadingFields] = useState(false)

  // Fetch current conflict count from database
  const fetchCurrentConflictCount = async () => {
    try {
      const response = await fetch('/api/conflicts?page=1&pageSize=1&status=pending')
      const data = await response.json()
      if (data.success) {
        setCurrentConflictCount(data.data.pagination.total)
      }
    } catch (error) {
      console.error('Error fetching conflict count:', error)
    }
  }

  // Fetch conflict count on component mount and when switching to conflicts tab
  useEffect(() => {
    fetchCurrentConflictCount()
  }, [])

  useEffect(() => {
    if (activeTab === 'conflicts') {
      fetchCurrentConflictCount()
    }
  }, [activeTab])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    const files = Array.from(e.dataTransfer.files)
    const validFile = files.find(file => 
      file.type === 'text/csv' || 
      file.name.endsWith('.csv') ||
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.name.endsWith('.xlsx')
    )
    
    if (validFile) {
      setFile(validFile)
      parseFile(validFile)
    }
  }, [])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      parseFile(selectedFile)
    }
  }

  const parseFile = async (file: File) => {
    setLoading(true)
          setResult(null)
      setSampleData(null)
      setMappingConfirmed(false)
      setHasInitialMapping(false) // Reset mapping cache for new file
    
    try {
      const content = await file.arrayBuffer()
      let parsedData: any[] = []
      let headers: string[] = []

      if (file.name.endsWith('.xlsx')) {
        const workbook = XLSX.read(content)
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        parsedData = XLSX.utils.sheet_to_json(worksheet)
        // Get headers from the first row
        if (parsedData.length > 0) {
          headers = Object.keys(parsedData[0])
        }
      } else {
        const text = new TextDecoder().decode(content)
        const parseResult = Papa.parse<CSVRow>(text, {
          header: true,
          skipEmptyLines: true,
          transformHeader: (header) => header.trim()
        })
        
        if (parseResult.errors.length > 0) {
          throw new Error('Failed to parse CSV: ' + parseResult.errors[0].message)
        }
        
        parsedData = parseResult.data
        headers = parseResult.meta.fields || []
      }

      // Store headers for mapping
      setCsvHeaders(headers)
      
      // Store first row as sample data for mapping reference
      if (parsedData.length > 0) {
        setSampleData(parsedData[0])
      }

      const previewRows: ConflictPreviewRow[] = parsedData.map((row, index) => ({
        ...row,
        index,
        selected: true,
        conflict_type: 'none'
      }))

      setPreviewData(previewRows)
      setShowPreview(true)
    } catch (error) {
      console.error('File parsing error:', error)
      alert('Failed to parse file: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setLoading(false)
    }
  }

  const checkConflicts = async () => {
    if (previewData.length === 0) return
    
    if (!mappingConfirmed) {
      alert('Please map headers first before checking conflicts.')
      return
    }
    
    setChecking(true)
    
    try {
      // Send raw data with mappings - let backend handle transformation and conflict checking
      const response = await fetch('/api/investors/check-conflicts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          data: previewData,
          headerMappings,
          source: `CSV Upload Preview ${new Date().toISOString().split('T')[0]}`
        })
      })

      const conflictData = await response.json()
      
      if (conflictData.success) {
        // Update preview data with conflict information
        const updatedData = previewData.map((row, index) => {
          // Get mapped values for this row
          const getMappedValue = (csvHeader: string) => {
            const dbField = headerMappings[csvHeader]
            return dbField ? row[csvHeader] : null
          }
          
          const companyName = getMappedValue(Object.keys(headerMappings).find(h => headerMappings[h] === 'company_name') || '')?.trim() || ''
          const firstName = getMappedValue(Object.keys(headerMappings).find(h => headerMappings[h] === 'first_name') || '')?.trim() || ''
          const lastName = getMappedValue(Object.keys(headerMappings).find(h => headerMappings[h] === 'last_name') || '')?.trim() || ''
          const fullName = `${firstName} ${lastName}`.trim()
          const email = getMappedValue(Object.keys(headerMappings).find(h => headerMappings[h] === 'email') || '')?.trim() || ''
          
          // Check for company conflicts
          const companyConflict = conflictData.conflicts.companies.find(
            (c: any) => c.company_name === companyName
          )
          
          // Check for contact conflicts
          const contactConflict = conflictData.conflicts.contacts.find(
            (c: any) => c.full_name === fullName && c.company_name === companyName
          )
          
          // Check for email conflicts
          const emailConflict = conflictData.conflicts.emails.find(
            (c: any) => c.email === email
          )

          let conflictType: ConflictPreviewRow['conflict_type'] = 'none'
          let conflicts = {}
          let existingRecordId: number | undefined

          if (emailConflict) {
            conflictType = 'duplicate_email'
            existingRecordId = emailConflict.existing_id
          } else if (contactConflict) {
            conflictType = 'field_conflict'
            conflicts = contactConflict.conflicts
            existingRecordId = contactConflict.existing_id
          } else if (companyConflict) {
            conflictType = 'field_conflict'
            conflicts = companyConflict.conflicts
            existingRecordId = companyConflict.existing_id
          }

          return {
            ...row,
            conflict_type: conflictType,
            conflicts,
            existing_record_id: existingRecordId
          }
        })
        
        setPreviewData(updatedData)
      }
    } catch (error) {
      console.error('Error checking conflicts:', error)
    } finally {
      setChecking(false)
    }
  }

  const uploadWithConflicts = async () => {
    const selectedRows = previewData.filter(row => row.selected)
    
    if (selectedRows.length === 0) {
      alert('Please select at least one row to upload.')
      return
    }

    if (!mappingConfirmed) {
      alert('Please map headers first before uploading.')
      return
    }

    setUploading(true)
    setProgress(0)

    const progressInterval = setInterval(() => {
      setProgress(prev => Math.min(prev + 10, 90))
    }, 300)

    try {
      // Send raw data with mappings - let backend handle transformation
      const response = await fetch('/api/investors/upload-with-conflicts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          data: selectedRows,
          headerMappings,
          source: `CSV Upload ${new Date().toISOString().split('T')[0]}`
        })
      })

      const data: ConflictUploadResult = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Upload failed')
      }

      setProgress(100)
      setResult(data)
      
      // Update current conflict count after upload
      await fetchCurrentConflictCount()
      
      // If there are conflicts, switch to conflicts tab
      if (data.conflicts && (data.conflicts.companies.length > 0 || data.conflicts.contacts.length > 0)) {
        setActiveTab('conflicts')
      }
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        stats: {
          companies: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 },
          contacts: { total: 0, added: 0, updated_with_conflicts: 0, skipped: 0, error: 0 }
        },
        conflicts: { companies: [], contacts: [] },
        logs: []
      })
    } finally {
      clearInterval(progressInterval)
      setUploading(false)
    }
  }

  const resetUpload = () => {
    setFile(null)
    setPreviewData([])
    setShowPreview(false)
    setResult(null)
    setProgress(0)
    setSortColumn(null)
    setSortDirection(null)
    setActiveTab('upload')
    setShowMappingSection(false)
    setCsvHeaders([])
    setSampleData(null)
    setHeaderMappings({})
    setMappingConfirmed(false)
    setHasInitialMapping(false) // Reset mapping cache for new file
  }

  const toggleRowSelection = (index: number) => {
    const updated = previewData.map(row => 
      row.index === index ? { ...row, selected: !row.selected } : row
    )
    setPreviewData(updated)
  }

  const toggleSelectAll = () => {
    const allSelected = previewData.every(row => row.selected)
    const updated = previewData.map(row => ({ ...row, selected: !allSelected }))
    setPreviewData(updated)
  }

  const handleHeaderMappingConfirm = (mappings: Record<string, string>) => {
    setHeaderMappings(mappings)
    setMappingConfirmed(true)
    setShowMappingSection(false)
  }

  const fetchDatabaseFields = async () => {
    if (loadingFields) return
    
    try {
      setLoadingFields(true)
      const response = await fetch('/api/investors/get-database-fields')
      const data = await response.json()
      
      if (data.success) {
        const fields = [
          ...data.companies.map((field: string) => ({
            value: field,
            label: field.split('_').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            table: 'companies'
          })),
          ...data.contacts.map((field: string) => ({
            value: field,
            label: field.split('_').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
            table: 'contacts'
          }))
        ]
        setDatabaseFields(fields)
      }
    } catch (error) {
      console.error('Failed to fetch database fields:', error)
    } finally {
      setLoadingFields(false)
    }
  }

  const fetchHeaderMappings = async () => {
    if (hasInitialMapping) return // Don't fetch if we already have mappings
    
    setLoadingMapping(true)
    try {
      const response = await fetch('/api/investors/map-headers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          headers: csvHeaders,
          context: sampleData 
            ? `Equity investor data with companies and contacts. Sample data: ${JSON.stringify(sampleData)}`
            : 'Equity investor data with companies and contacts'
        })
      })

      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to map headers')
      }

      setMappingData(data)
      
      // Initialize user mappings with AI suggestions
      const initialMappings: Record<string, string> = {}
      
      // Process company mappings
      if (data.company_mappings) {
        Object.entries(data.company_mappings).forEach(([dbField, csvHeaders]) => {
          if (Array.isArray(csvHeaders) && csvHeaders.length > 0) {
            initialMappings[csvHeaders[0]] = dbField
          }
        })
      }
      
      // Process contact mappings
      if (data.contact_mappings) {
        Object.entries(data.contact_mappings).forEach(([dbField, csvHeaders]) => {
          if (Array.isArray(csvHeaders) && csvHeaders.length > 0) {
            initialMappings[csvHeaders[0]] = dbField
          }
        })
      }
      
      setHeaderMappings(initialMappings)
      setHasInitialMapping(true)
    } catch (error) {
      console.error('Header mapping error:', error)
    } finally {
      setLoadingMapping(false)
    }
  }

  const toggleMappingSection = async () => {
    if (!showMappingSection) {
      // Opening mapping section - fetch data if needed
      if (databaseFields.length === 0) {
        await fetchDatabaseFields()
      }
      if (!hasInitialMapping) {
        await fetchHeaderMappings()
      }
    }
    setShowMappingSection(!showMappingSection)
  }

  const reAnalyzeMappings = async () => {
    setHasInitialMapping(false)
    await fetchHeaderMappings()
  }

  const isAISuggestion = (header: string) => {
    if (!mappingData) return false
    
    // Check if this header was suggested by AI in company mappings
    if (mappingData.company_mappings) {
      for (const [dbField, csvHeaders] of Object.entries(mappingData.company_mappings)) {
        if (Array.isArray(csvHeaders) && csvHeaders.includes(header) && headerMappings[header] === dbField) {
          return true
        }
      }
    }
    
    // Check if this header was suggested by AI in contact mappings
    if (mappingData.contact_mappings) {
      for (const [dbField, csvHeaders] of Object.entries(mappingData.contact_mappings)) {
        if (Array.isArray(csvHeaders) && csvHeaders.includes(header) && headerMappings[header] === dbField) {
          return true
        }
      }
    }
    
    return false
  }

  const isAIOriginalSuggestion = (header: string, dbField: string) => {
    if (!mappingData) return false
    
    // Check if this header-dbField combination was originally suggested by AI
    if (mappingData.company_mappings) {
      const csvHeaders = mappingData.company_mappings[dbField]
      if (Array.isArray(csvHeaders) && csvHeaders.includes(header)) {
        return true
      }
    }
    
    if (mappingData.contact_mappings) {
      const csvHeaders = mappingData.contact_mappings[dbField]
      if (Array.isArray(csvHeaders) && csvHeaders.includes(header)) {
        return true
      }
    }
    
    return false
  }

  const getDisplayValue = (row: any, dbField: string) => {
    // First check if the mapped field exists
    if (row[dbField]) {
      return row[dbField]
    }
    
    // Find the original CSV header that maps to this DB field
    const csvHeader = Object.keys(headerMappings).find(header => headerMappings[header] === dbField)
    if (csvHeader && row[csvHeader]) {
      return row[csvHeader]
    }
    
    return null
  }

  const getConflictBadge = (row: ConflictPreviewRow) => {
    switch (row.conflict_type) {
      case 'field_conflict':
        return (
          <Badge variant="destructive" className="text-xs">
            {Object.keys(row.conflicts || {}).length} Field Conflicts
          </Badge>
        )
      case 'duplicate_email':
        return (
          <Badge variant="secondary" className="text-xs">
            Email Exists
          </Badge>
        )
      case 'duplicate_company':
        return (
          <Badge variant="secondary" className="text-xs">
            Company Exists
          </Badge>
        )
      case 'duplicate_contact':
        return (
          <Badge variant="secondary" className="text-xs">
            Contact Exists
          </Badge>
        )
      default:
        return (
          <Badge variant="default" className="text-xs">
            New
          </Badge>
        )
    }
  }

  const selectedCount = previewData.filter(row => row.selected).length
  const conflictCount = previewData.filter(row => row.conflict_type !== 'none').length
  const newCount = previewData.filter(row => row.conflict_type === 'none').length

  // Reusable mapping button component
  const MappingButton = ({ 
    onClick, 
    disabled, 
    loading, 
    children, 
    variant = "outline", 
    className = "" 
  }: {
    onClick: () => void
    disabled?: boolean
    loading?: boolean
    children: React.ReactNode
    variant?: "outline" | "default"
    className?: string
  }) => (
    <Button 
      variant={variant}
      onClick={onClick}
      disabled={disabled || loading}
      className={`${className} ${loading ? 'cursor-not-allowed' : ''}`}
    >
      {loading ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          Loading...
        </>
      ) : (
        children
      )}
    </Button>
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Data Upload Center</h1>
            <p className="text-gray-600 mt-1">Upload and manage investor data with intelligent conflict resolution</p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="px-3 py-1">
              <Activity className="h-4 w-4 mr-2" />
              Smart Processing
            </Badge>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 h-12 bg-white shadow-sm">
            <TabsTrigger value="upload" className="flex items-center gap-2 text-base font-medium">
              <Upload className="h-5 w-5" />
              Upload Data
            </TabsTrigger>
            <TabsTrigger value="conflicts" className="flex items-center gap-2 text-base font-medium">
              <AlertTriangle className="h-5 w-5" />
              Manage Conflicts
              {currentConflictCount > 0 && (
                <Badge variant="destructive" className="ml-1 text-xs">
                  {currentConflictCount}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6 mt-6">
            {/* Upload Section */}
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl flex items-center gap-2">
                  <Database className="h-6 w-6 text-blue-600" />
                  Upload Equity Investors Data
                </CardTitle>
                <p className="text-gray-600">
                  Upload CSV or XLSX files containing equity investor companies and contacts data.
                  Our intelligent system automatically detects and resolves conflicts.
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Zap className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-blue-900">Smart Conflict Detection</h3>
                        <p className="text-sm text-blue-700">Automatically identifies data conflicts</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-green-900">Domain Matching</h3>
                        <p className="text-sm text-green-700">Intelligent website domain comparison</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-100">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-purple-900">Real-time Stats</h3>
                        <p className="text-sm text-purple-700">Live processing statistics</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {!file && (
                  <div
                    className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
                      dragActive 
                        ? 'border-blue-500 bg-blue-50/50 scale-[1.02]' 
                        : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50/50'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <div className="flex flex-col items-center">
                      <div className="p-4 bg-blue-100 rounded-full mb-4">
                        <FileText className="h-12 w-12 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        Drop your files here
                      </h3>
                      <p className="text-gray-500 mb-6 max-w-sm">
                        Support for CSV and XLSX files up to 10MB. 
                        Drag and drop or click to browse.
                      </p>
                      <input
                        type="file"
                        accept=".csv,.xlsx,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        onChange={handleFileChange}
                        className="hidden"
                        id="csv-file-input"
                      />
                      <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                        <label htmlFor="csv-file-input" className="cursor-pointer">
                          <Upload className="h-5 w-5 mr-2" />
                          Browse Files
                        </label>
                      </Button>
                    </div>
                  </div>
                )}

                {file && loading && (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center gap-3 bg-white p-6 rounded-xl shadow-sm">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <div className="text-left">
                        <p className="font-medium text-gray-900">Processing file...</p>
                        <p className="text-sm text-gray-500">Analyzing data structure</p>
                      </div>
                    </div>
                  </div>
                )}

                {file && showPreview && !result && (
                  <div className="space-y-6">
                    {/* File Info Card */}
                    <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="p-3 bg-blue-100 rounded-lg">
                              <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{file.name}</h3>
                              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                <span className="flex items-center gap-1">
                                  <Database className="h-4 w-4" />
                                  {previewData.length} rows
                                </span>
                                <span className="flex items-center gap-1">
                                  <Activity className="h-4 w-4" />
                                  {(file.size / 1024).toFixed(1)} KB
                                </span>
                                {mappingConfirmed && (
                                  <span className="flex items-center gap-1 text-green-600">
                                    <Brain className="h-4 w-4" />
                                    Headers Mapped
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-3">
                            <Button variant="outline" onClick={resetUpload}>
                              Remove
                            </Button>
                            <MappingButton
                              onClick={toggleMappingSection}
                              loading={loadingMapping || loadingFields}
                              className="border-purple-200 text-purple-700 hover:bg-purple-50"
                            >
                              <Brain className="h-4 w-4 mr-2" />
                              {showMappingSection ? 'Hide Mapping' : (mappingConfirmed ? 'Edit Mapping' : 'Map Headers')}
                              {showMappingSection ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                            </MappingButton>
                            <MappingButton 
                              onClick={checkConflicts} 
                              disabled={!mappingConfirmed}
                              loading={checking}
                              variant="default"
                              className="bg-orange-600 hover:bg-orange-700 text-white"
                            >
                              <Zap className="h-4 w-4 mr-2" />
                              Check Conflicts
                            </MappingButton>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Header Mapping Status */}
                    {!mappingConfirmed && (
                      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-indigo-50">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="p-3 bg-purple-100 rounded-lg">
                                <Brain className="h-8 w-8 text-purple-600" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-purple-900">Header Mapping Required</h3>
                                <p className="text-purple-700 text-sm mt-1">
                                  Map your CSV headers to database fields using AI assistance before proceeding.
                                </p>
                              </div>
                            </div>
                            <MappingButton 
                              onClick={toggleMappingSection}
                              loading={loadingMapping || loadingFields}
                              variant="default"
                              className="bg-purple-600 hover:bg-purple-700 text-white"
                            >
                              <Brain className="h-4 w-4 mr-2" />
                              {showMappingSection ? 'Hide Mapping' : 'Map Headers'}
                              {showMappingSection ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                            </MappingButton>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {mappingConfirmed && (
                      <Card className="border-green-200 bg-gradient-to-r from-green-50 to-emerald-50">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="p-3 bg-green-100 rounded-lg">
                                <CheckCircle className="h-8 w-8 text-green-600" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-green-900">Headers Successfully Mapped</h3>
                                <p className="text-green-700 text-sm mt-1">
                                  {Object.keys(headerMappings).filter(k => headerMappings[k]).length} headers mapped to database fields.
                                </p>
                              </div>
                            </div>
                            <MappingButton 
                              onClick={toggleMappingSection}
                              loading={loadingMapping || loadingFields}
                              className="border-green-200 text-green-700 hover:bg-green-50"
                            >
                              <Brain className="h-4 w-4 mr-2" />
                              {showMappingSection ? 'Hide Mapping' : 'Edit Mapping'}
                              {showMappingSection ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                            </MappingButton>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Inline Header Mapping Section */}
                    {showMappingSection && (
                      <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                        <CardHeader className="pb-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-blue-100 rounded-lg">
                                <Brain className="h-6 w-6 text-blue-600" />
                              </div>
                              <div>
                                <CardTitle className="text-lg">Smart Header Mapping</CardTitle>
                                <p className="text-sm text-blue-700 mt-1">
                                  Map your CSV headers to database fields. AI suggestions are applied automatically.
                                  {hasInitialMapping && (
                                    <span className="text-green-600 font-medium"> Mappings cached.</span>
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {hasInitialMapping && (
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                  AI Analysis Cached
                                </Badge>
                              )}
                              <MappingButton
                                onClick={reAnalyzeMappings}
                                loading={loadingMapping}
                                className="border-purple-200 text-purple-700 hover:bg-purple-50"
                              >
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Re-analyze
                              </MappingButton>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowMappingSection(false)}
                                className="border-gray-200"
                              >
                                <ChevronUp className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {loadingMapping ? (
                            <div className="py-12">
                              <div className="max-w-md mx-auto">
                                {/* Main Loading Card */}
                                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-xl border border-purple-200 shadow-lg">
                                  <div className="text-center space-y-4">
                                    {/* Animated Brain Icon */}
                                    <div className="relative">
                                      <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                                        <Brain className="h-8 w-8 text-purple-600" />
                                      </div>
                                      <div className="absolute inset-0 w-16 h-16 mx-auto border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin"></div>
                                    </div>
                                    
                                    {/* Loading Text */}
                                    <div>
                                      <h3 className="text-lg font-semibold text-purple-900 mb-1">
                                        AI Analysis in Progress
                                      </h3>
                                      <p className="text-sm text-purple-700 mb-3">
                                        Analyzing {csvHeaders.length} headers and mapping to database fields
                                      </p>
                                    </div>
                                    
                                    {/* Progress Steps */}
                                    <div className="space-y-2">
                                      <div className="flex items-center justify-between text-xs text-purple-600">
                                        <span>Processing headers</span>
                                        <div className="flex space-x-1">
                                          <div className="w-2 h-2 bg-purple-600 rounded-full animate-pulse"></div>
                                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                                          <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
                                        </div>
                                      </div>
                                      
                                      {/* Progress Bar */}
                                      <div className="w-full bg-purple-200 rounded-full h-2">
                                        <div className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full animate-pulse" style={{width: '70%'}}></div>
                                      </div>
                                      
                                      <div className="text-xs text-purple-600 space-y-1">
                                        <div className="flex items-center gap-2">
                                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                          <span>Analyzing data structure</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                          <span>Matching field patterns</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse"></div>
                                          <span>Generating suggestions</span>
                                        </div>
                                      </div>
                                    </div>
                                    
                                    {/* Estimated Time */}
                                    <div className="pt-2 border-t border-purple-200">
                                      <p className="text-xs text-purple-600">
                                        <Clock className="h-3 w-3 inline mr-1" />
                                        Usually takes 3-5 seconds
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                
                                {/* Background Info */}
                                <div className="mt-4 text-center">
                                  <p className="text-xs text-gray-500">
                                    Our AI is analyzing your data to suggest the best field mappings
                                  </p>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {/* AI Analysis Summary */}
                              {mappingData && (
                                <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border border-purple-200">
                                  <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                      <Brain className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                      <h4 className="font-semibold text-purple-900">AI Analysis Results</h4>
                                      <p className="text-sm text-purple-700">
                                        Smart mapping suggestions based on your data structure and sample content
                                      </p>
                                    </div>
                                  </div>
                                  
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="bg-white p-3 rounded-lg border border-purple-100">
                                      <div className="flex items-center gap-2 mb-1">
                                        <Building2 className="h-4 w-4 text-blue-600" />
                                        <span className="font-medium text-gray-900">Company Fields</span>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        {mappingData.company_mappings ? Object.keys(mappingData.company_mappings).length : 0} fields mapped
                                      </p>
                                    </div>
                                    
                                    <div className="bg-white p-3 rounded-lg border border-purple-100">
                                      <div className="flex items-center gap-2 mb-1">
                                        <Users className="h-4 w-4 text-purple-600" />
                                        <span className="font-medium text-gray-900">Contact Fields</span>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        {mappingData.contact_mappings ? Object.keys(mappingData.contact_mappings).length : 0} fields mapped
                                      </p>
                                    </div>
                                    
                                    <div className="bg-white p-3 rounded-lg border border-purple-100">
                                      <div className="flex items-center gap-2 mb-1">
                                        <X className="h-4 w-4 text-gray-600" />
                                        <span className="font-medium text-gray-900">Unmapped</span>
                                      </div>
                                      <p className="text-sm text-gray-600">
                                        {mappingData.unmapped_headers ? mappingData.unmapped_headers.length : 0} headers skipped
                                      </p>
                                    </div>
                                  </div>
                                  
                                  {mappingData.unmapped_headers && mappingData.unmapped_headers.length > 0 && (
                                    <div className="mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                      <p className="text-sm font-medium text-yellow-800 mb-1">Unmapped Headers:</p>
                                      <div className="flex flex-wrap gap-1">
                                        {mappingData.unmapped_headers.map((header: string) => (
                                          <Badge key={header} variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300 text-xs">
                                            {header}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* Data Quality Suggestions */}
                                  {mappingData.suggestions && (
                                    <div className="mt-3 space-y-3">
                                      {mappingData.suggestions.data_quality_notes && mappingData.suggestions.data_quality_notes.length > 0 && (
                                        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                          <div className="flex items-center gap-2 mb-2">
                                            <AlertTriangle className="h-4 w-4 text-blue-600" />
                                            <p className="text-sm font-medium text-blue-800">Data Quality Suggestions</p>
                                          </div>
                                          <ul className="space-y-1">
                                            {mappingData.suggestions.data_quality_notes.map((note: string, index: number) => (
                                              <li key={index} className="text-sm text-blue-700 flex items-start gap-2">
                                                <span className="text-blue-400 mt-1">•</span>
                                                <span>{note}</span>
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                      )}

                                      {mappingData.suggestions.missing_recommended_fields && mappingData.suggestions.missing_recommended_fields.length > 0 && (
                                        <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
                                          <div className="flex items-center gap-2 mb-2">
                                            <AlertTriangle className="h-4 w-4 text-amber-600" />
                                            <p className="text-sm font-medium text-amber-800">Missing Recommended Fields</p>
                                          </div>
                                          <div className="flex flex-wrap gap-1">
                                            {mappingData.suggestions.missing_recommended_fields.map((field: string, index: number) => (
                                              <Badge key={index} variant="outline" className="bg-amber-100 text-amber-800 border-amber-300 text-xs">
                                                {field}
                                              </Badge>
                                            ))}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              )}
                              
                              {csvHeaders.map((header) => (
                                <div key={header} className="p-4 border rounded-lg bg-white">
                                  <div className="flex items-start justify-between gap-4">
                                    <div className="flex-1 min-w-0">
                                                                             <div className="flex items-center gap-3 mb-2">
                                         <h4 className="font-medium text-gray-900">{header}</h4>
                                         {headerMappings[header] && (
                                           <>
                                             <ArrowRight className="h-4 w-4 text-gray-400" />
                                             <div className="flex items-center gap-2">
                                               {/* Table indicator */}
                                               {headerMappings[header].startsWith('company_') || headerMappings[header] === 'industry' ? (
                                                 <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                                   <Building2 className="h-3 w-3 mr-1" />
                                                   Companies
                                                 </Badge>
                                               ) : (
                                                 <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                                                   <Users className="h-3 w-3 mr-1" />
                                                   Contacts
                                                 </Badge>
                                               )}
                                               <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                                 {databaseFields.find(f => f.value === headerMappings[header])?.label || headerMappings[header]}
                                               </Badge>
                                               {isAISuggestion(header) && (
                                                 <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                                                   <Brain className="h-3 w-3 mr-1" />
                                                   AI Pick
                                                 </Badge>
                                               )}
                                             </div>
                                           </>
                                         )}
                                       </div>
                                      
                                      {/* Sample Data Display */}
                                      {sampleData && sampleData[header] && (
                                        <div className="bg-gray-100 px-3 py-2 rounded-md border mb-3">
                                          <div className="flex items-center gap-2">
                                            <span className="text-xs font-medium text-gray-600">Sample:</span>
                                            <span className="text-sm text-gray-800 font-mono bg-white px-2 py-1 rounded border">
                                              {String(sampleData[header]).length > 50 
                                                ? `${String(sampleData[header]).substring(0, 50)}...`
                                                : String(sampleData[header])
                                              }
                                            </span>
                                          </div>
                                        </div>
                                      )}
                                      
                                      <Select
                                        value={headerMappings[header] || ''}
                                        onValueChange={(value) => {
                                          const newMappings = { ...headerMappings }
                                          if (value === '__skip__') {
                                            delete newMappings[header]
                                          } else {
                                            newMappings[header] = value
                                          }
                                          setHeaderMappings(newMappings)
                                        }}
                                      >
                                        <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select database field or skip" />
                                        </SelectTrigger>
                                        <SelectContent className="max-h-80">
                                          <SelectItem value="__skip__">
                                            <div className="flex items-center gap-2">
                                              <X className="h-4 w-4 text-gray-400" />
                                              <span>Skip this field</span>
                                            </div>
                                          </SelectItem>
                                          
                                          {/* Companies Section */}
                                          <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-blue-50 border-b">
                                            <div className="flex items-center gap-1">
                                              <Building2 className="h-3 w-3" />
                                              COMPANIES
                                            </div>
                                          </div>
                                                                                     {databaseFields
                                             .filter((field) => field.table === 'companies')
                                             .map((field) => (
                                             <SelectItem key={field.value} value={field.value}>
                                               <div className="flex items-center gap-2 w-full">
                                                 <span className="flex-1">{field.label}</span>
                                                 {isAIOriginalSuggestion(header, field.value) && (
                                                   <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                                                     <Brain className="h-3 w-3 mr-1" />
                                                     AI Pick
                                                   </Badge>
                                                 )}
                                               </div>
                                             </SelectItem>
                                           ))}
                                          
                                          {/* Contacts Section */}
                                          <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 bg-purple-50 border-b border-t">
                                            <div className="flex items-center gap-1">
                                              <Users className="h-3 w-3" />
                                              CONTACTS
                                            </div>
                                          </div>
                                                                                     {databaseFields
                                             .filter((field) => field.table === 'contacts')
                                             .map((field) => (
                                             <SelectItem key={field.value} value={field.value}>
                                               <div className="flex items-center gap-2 w-full">
                                                 <span className="flex-1">{field.label}</span>
                                                 {isAIOriginalSuggestion(header, field.value) && (
                                                   <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                                                     <Brain className="h-3 w-3 mr-1" />
                                                     AI Pick
                                                   </Badge>
                                                 )}
                                               </div>
                                             </SelectItem>
                                           ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                </div>
                              ))}
                              
                              {/* Database Impact Summary */}
                              {Object.keys(headerMappings).length > 0 && (
                                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
                                  <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                      <Database className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div>
                                      <h4 className="font-semibold text-green-900">Database Impact Summary</h4>
                                      <p className="text-sm text-green-700">
                                        Preview of which database tables will be populated
                                      </p>
                                    </div>
                                  </div>
                                  
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="bg-white p-3 rounded-lg border border-green-100">
                                      <div className="flex items-center gap-2 mb-2">
                                        <Building2 className="h-4 w-4 text-blue-600" />
                                        <span className="font-medium text-gray-900">Companies Table</span>
                                      </div>
                                      <p className="text-sm text-gray-600 mb-2">
                                        {Object.values(headerMappings).filter(field => field.startsWith('company_') || field === 'industry').length} fields will be populated
                                      </p>
                                      <div className="flex flex-wrap gap-1">
                                        {Object.values(headerMappings)
                                          .filter(field => field.startsWith('company_') || field === 'industry')
                                          .map((field, index) => (
                                            <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                              {databaseFields.find(f => f.value === field)?.label || field}
                                            </Badge>
                                          ))
                                        }
                                      </div>
                                    </div>
                                    
                                    <div className="bg-white p-3 rounded-lg border border-green-100">
                                      <div className="flex items-center gap-2 mb-2">
                                        <Users className="h-4 w-4 text-purple-600" />
                                        <span className="font-medium text-gray-900">Contacts Table</span>
                                      </div>
                                      <p className="text-sm text-gray-600 mb-2">
                                        {Object.values(headerMappings).filter(field => !field.startsWith('company_') && field !== 'industry').length} fields will be populated
                                      </p>
                                      <div className="flex flex-wrap gap-1">
                                        {Object.values(headerMappings)
                                          .filter(field => !field.startsWith('company_') && field !== 'industry')
                                          .map((field, index) => (
                                            <Badge key={index} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                                              {databaseFields.find(f => f.value === field)?.label || field}
                                            </Badge>
                                          ))
                                        }
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}

                              <div className="flex items-center justify-between pt-4 border-t">
                                <div className="text-sm text-gray-600">
                                  {Object.keys(headerMappings).length} of {csvHeaders.length} headers mapped
                                </div>
                                <Button
                                  onClick={() => {
                                    setMappingConfirmed(true)
                                    setShowMappingSection(false)
                                  }}
                                  disabled={Object.keys(headerMappings).length === 0}
                                  className="bg-blue-600 hover:bg-blue-700"
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Confirm Mapping ({Object.keys(headerMappings).length} mapped)
                                </Button>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-blue-700">Total Records</p>
                              <p className="text-2xl font-bold text-blue-900">{previewData.length}</p>
                            </div>
                            <Database className="h-8 w-8 text-blue-600" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-green-700">New Records</p>
                              <p className="text-2xl font-bold text-green-900">{newCount}</p>
                            </div>
                            <CheckCircle className="h-8 w-8 text-green-600" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-orange-700">Conflicts</p>
                              <p className="text-2xl font-bold text-orange-900">{conflictCount}</p>
                            </div>
                            <AlertTriangle className="h-8 w-8 text-orange-600" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-purple-700">Selected</p>
                              <p className="text-2xl font-bold text-purple-900">{selectedCount}</p>
                            </div>
                            <Eye className="h-8 w-8 text-purple-600" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Action Bar */}
                    <div className="flex items-center justify-between p-4 bg-white rounded-lg border shadow-sm">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleSelectAll}
                          className="flex items-center gap-2"
                        >
                          <Checkbox 
                            checked={previewData.length > 0 && previewData.every(row => row.selected)}
                          />
                          Select All ({selectedCount}/{previewData.length})
                        </Button>
                        
                        <div className="flex gap-2">
                          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
                            {newCount} New
                          </Badge>
                          <Badge variant="destructive" className="bg-orange-100 text-orange-800 border-orange-200">
                            {conflictCount} Conflicts
                          </Badge>
                        </div>
                      </div>

                      {selectedCount > 0 && mappingConfirmed && (
                        <Button 
                          onClick={uploadWithConflicts}
                          disabled={uploading}
                          size="lg"
                          className="bg-blue-600 hover:bg-blue-700 shadow-lg"
                        >
                          {uploading ? (
                            <>
                              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                              Processing...
                            </>
                          ) : (
                            <>
                              <Upload className="h-5 w-5 mr-2" />
                              Upload {selectedCount} Records
                            </>
                          )}
                        </Button>
                      )}

                      {selectedCount > 0 && !mappingConfirmed && (
                        <MappingButton 
                          onClick={toggleMappingSection}
                          loading={loadingMapping || loadingFields}
                          variant="default"
                          className="bg-purple-600 hover:bg-purple-700 shadow-lg text-white px-6 py-3 text-base"
                        >
                          <Brain className="h-5 w-5 mr-2" />
                          {showMappingSection ? 'Hide Mapping' : 'Map Headers First'}
                          {showMappingSection ? <ChevronUp className="h-5 w-5 ml-2" /> : <ChevronDown className="h-5 w-5 ml-2" />}
                        </MappingButton>
                      )}
                    </div>

                    {uploading && (
                      <Card className="bg-blue-50 border-blue-200">
                        <CardContent className="p-6">
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                <div>
                                  <p className="font-medium text-blue-900">Processing {selectedCount} records...</p>
                                  <p className="text-sm text-blue-700">Detecting conflicts and updating database</p>
                                </div>
                              </div>
                              <span className="text-lg font-bold text-blue-900">{progress}%</span>
                            </div>
                            <Progress value={progress} className="w-full h-2" />
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Preview Table */}
                    <Card className="overflow-hidden shadow-lg">
                      <CardHeader className="bg-gray-50 border-b">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">Data Preview</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              {csvHeaders.length} columns
                            </Badge>
                            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                              {previewData.length} rows
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="max-h-96 overflow-auto">
                          <table className="w-full text-sm">
                            <thead className="bg-gray-100 sticky top-0">
                              <tr>
                                <th className="p-3 text-left font-medium text-gray-700 sticky left-0 bg-gray-100 z-10 border-r">
                                  Select
                                </th>
                                {csvHeaders.map((header) => (
                                  <th key={header} className="p-3 text-left font-medium text-gray-700 min-w-32">
                                    <div className="flex flex-col gap-1">
                                      <span className="truncate" title={header}>{header}</span>
                                      {headerMappings[header] && (
                                        <div className="flex items-center gap-1">
                                          {headerMappings[header].startsWith('company_') || headerMappings[header] === 'industry' ? (
                                            <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 text-xs">
                                              <Building2 className="h-2 w-2 mr-1" />
                                              Co
                                            </Badge>
                                          ) : (
                                            <Badge variant="outline" className="bg-purple-50 text-purple-600 border-purple-200 text-xs">
                                              <Users className="h-2 w-2 mr-1" />
                                              Ct
                                            </Badge>
                                          )}
                                          {isAISuggestion(header) && (
                                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 text-xs">
                                              AI
                                            </Badge>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </th>
                                ))}
                                <th className="p-3 text-left font-medium text-gray-700 sticky right-0 bg-gray-100 z-10 border-l">
                                  Status
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              {previewData.slice(0, 10).map((row) => (
                                <tr key={row.index} className={`border-t hover:bg-gray-50 transition-colors ${row.selected ? 'bg-blue-50' : ''}`}>
                                  <td className="p-3 sticky left-0 bg-white z-10 border-r">
                                    <Checkbox
                                      checked={row.selected}
                                      onCheckedChange={() => toggleRowSelection(row.index)}
                                    />
                                  </td>
                                  {csvHeaders.map((header) => (
                                    <td key={header} className="p-3">
                                      <div className="max-w-32 truncate text-gray-700" title={row[header] || ''}>
                                        {row[header] || '-'}
                                      </div>
                                    </td>
                                  ))}
                                  <td className="p-3 sticky right-0 bg-white z-10 border-l">
                                    {getConflictBadge(row)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                          {previewData.length > 10 && (
                            <div className="p-4 bg-gray-50 border-t text-center">
                              <p className="text-sm text-gray-600">
                                Showing first 10 rows of {previewData.length} total rows
                              </p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {result && (
                  <div className="space-y-6">
                    {result.success ? (
                      <Alert className="border-green-200 bg-green-50">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <AlertDescription className="text-green-800">
                          <span className="font-semibold">Success!</span> Processed {selectedCount} rows successfully.
                          {(result.conflicts.companies.length + result.conflicts.contacts.length > 0) && 
                            ` Found ${result.conflicts.companies.length + result.conflicts.contacts.length} conflicts that need resolution.`
                          }
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <Alert variant="destructive">
                        <XCircle className="h-5 w-5" />
                        <AlertDescription>
                          <span className="font-semibold">Upload Failed:</span> {result.error || 'Unknown error occurred'}
                        </AlertDescription>
                      </Alert>
                    )}

                    {result.success && result.stats && (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card className="shadow-lg">
                          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Building2 className="h-5 w-5 text-blue-600" />
                              Companies Processing Results
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-6 space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center p-3 bg-gray-50 rounded-lg">
                                <p className="text-2xl font-bold text-gray-900">{result.stats.companies.total}</p>
                                <p className="text-sm text-gray-600">Total Processed</p>
                              </div>
                              <div className="text-center p-3 bg-green-50 rounded-lg">
                                <p className="text-2xl font-bold text-green-600">{result.stats.companies.added}</p>
                                <p className="text-sm text-green-700">New Added</p>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center p-3 bg-orange-50 rounded-lg">
                                <p className="text-2xl font-bold text-orange-600">{result.stats.companies.updated_with_conflicts}</p>
                                <p className="text-sm text-orange-700">Updated/Conflicts</p>
                              </div>
                              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                <p className="text-2xl font-bold text-yellow-600">{result.stats.companies.skipped}</p>
                                <p className="text-sm text-yellow-700">Skipped</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="shadow-lg">
                          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Users className="h-5 w-5 text-purple-600" />
                              Contacts Processing Results
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-6 space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center p-3 bg-gray-50 rounded-lg">
                                <p className="text-2xl font-bold text-gray-900">{result.stats.contacts.total}</p>
                                <p className="text-sm text-gray-600">Total Processed</p>
                              </div>
                              <div className="text-center p-3 bg-green-50 rounded-lg">
                                <p className="text-2xl font-bold text-green-600">{result.stats.contacts.added}</p>
                                <p className="text-sm text-green-700">New Added</p>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center p-3 bg-orange-50 rounded-lg">
                                <p className="text-2xl font-bold text-orange-600">{result.stats.contacts.updated_with_conflicts}</p>
                                <p className="text-sm text-orange-700">Updated/Conflicts</p>
                              </div>
                              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                                <p className="text-2xl font-bold text-yellow-600">{result.stats.contacts.skipped}</p>
                                <p className="text-sm text-yellow-700">Skipped</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-3">
                      <Button onClick={resetUpload} variant="outline" size="lg">
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Another File
                      </Button>
                      {(result.conflicts.companies.length + result.conflicts.contacts.length > 0) && (
                        <Button 
                          onClick={() => setActiveTab('conflicts')} 
                          className="bg-orange-600 hover:bg-orange-700"
                          size="lg"
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Resolve {result.conflicts.companies.length + result.conflicts.contacts.length} Conflicts
                        </Button>
                      )}
                      <Button 
                        onClick={() => window.location.reload()}
                        variant="outline"
                        size="lg"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Updated Data
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="conflicts" className="space-y-4 mt-6">
            <ConflictDashboard onNavigateToUpload={() => setActiveTab('upload')} />
          </TabsContent>
        </Tabs>

        {/* Header Mapping Modal */}
        {/* Header mapping will be inline now, remove modal */}
      </div>
    </div>
  )
} 