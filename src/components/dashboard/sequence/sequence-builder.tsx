import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Mail, Phone, Calendar, MessageSquare, Clock, Trash2, GripVertical, Settings, PlusCircle, Eye, Edit } from 'lucide-react';
import SequencePreview from './sequence-preview';
import TemplateEditor from './template-editor';

const SequenceBuilder = () => {
  const [view, setView] = useState('builder'); // 'builder', 'preview', or 'template'
  const [steps, setSteps] = useState([
    { type: 'email', delay: 0, template: 'initial_outreach' },
    { type: 'email', delay: 3, template: 'follow_up_1' },
    { type: 'call', delay: 5, template: 'phone_script_1' },
  ]);

  if (view === 'preview') {
    return <SequencePreview onBack={() => setView('builder')} />;
  }

  if (view === 'template') {
    return <TemplateEditor onBack={() => setView('builder')} />;
  }

  return (
    <div className="h-screen bg-gray-50 overflow-auto p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Create Engagement Sequence</h1>
          <p className="text-gray-500 mt-1">Design an automated engagement workflow</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => setView('template')}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Templates
          </Button>
          <Button variant="outline" onClick={() => setView('preview')}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button>Activate Sequence</Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2 space-y-6">
          {/* Sequence Details */}
          <Card>
            <CardHeader>
              <CardTitle>Sequence Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Sequence Name</label>
                  <Input placeholder="e.g., Q4 Investment Opportunities" className="mt-1" />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea 
                    placeholder="Describe the purpose and goals of this sequence" 
                    className="mt-1"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Target Audience</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select audience type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="equity">Equity Investors</SelectItem>
                        <SelectItem value="debt">Debt Providers</SelectItem>
                        <SelectItem value="developers">Developers</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Deal Type</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select deal type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="acquisition">Acquisition</SelectItem>
                        <SelectItem value="development">Development</SelectItem>
                        <SelectItem value="refinance">Refinance</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sequence Steps */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Sequence Steps</CardTitle>
                <Button variant="outline" onClick={() => setSteps([...steps, { type: 'email', delay: 1, template: '' }])}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Step
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <SequenceStep 
                    key={index}
                    step={step}
                    index={index}
                    onDelete={() => {
                      const newSteps = [...steps];
                      newSteps.splice(index, 1);
                      setSteps(newSteps);
                    }}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* AI Assistant */}
          <Card className="bg-gradient-to-br from-blue-50 to-white border-blue-100">
            <CardHeader>
              <CardTitle>AI Sequence Assistant</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button className="w-full justify-start" variant="ghost">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Generate follow-up templates
                </Button>
                <Button className="w-full justify-start" variant="ghost">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Optimize timing
                </Button>
                <Button className="w-full justify-start" variant="ghost">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Suggest personalization
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Templates */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Message Templates</CardTitle>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <TemplateItem 
                  name="Initial Outreach"
                  type="email"
                  lastUsed="2 days ago"
                />
                <TemplateItem 
                  name="Follow-up 1"
                  type="email"
                  lastUsed="1 week ago"
                />
                <TemplateItem 
                  name="Phone Script 1"
                  type="call"
                  lastUsed="3 days ago"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const SequenceStep = ({ step, index, onDelete }) => {
  const icons = {
    email: <Mail className="h-4 w-4" />,
    call: <Phone className="h-4 w-4" />,
    meeting: <Calendar className="h-4 w-4" />,
    message: <MessageSquare className="h-4 w-4" />
  };

  return (
    <div className="flex items-center space-x-4 p-4 border rounded-lg bg-white">
      <div className="cursor-move">
        <GripVertical className="h-4 w-4 text-gray-400" />
      </div>
      <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
        {icons[step.type]}
      </div>
      <div className="flex-1">
        <Select defaultValue={step.type}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="call">Phone Call</SelectItem>
            <SelectItem value="meeting">Meeting</SelectItem>
            <SelectItem value="message">Message</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="w-48">
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-gray-400" />
          <Select defaultValue={step.delay.toString()}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">Immediately</SelectItem>
              <SelectItem value="1">1 day after</SelectItem>
              <SelectItem value="2">2 days after</SelectItem>
              <SelectItem value="3">3 days after</SelectItem>
              <SelectItem value="5">5 days after</SelectItem>
              <SelectItem value="7">1 week after</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="w-48">
        <Select defaultValue={step.template}>
          <SelectTrigger>
            <SelectValue placeholder="Select template" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="initial_outreach">Initial Outreach</SelectItem>
            <SelectItem value="follow_up_1">Follow-up 1</SelectItem>
            <SelectItem value="phone_script_1">Phone Script 1</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <Button variant="ghost" size="icon" onClick={onDelete}>
        <Trash2 className="h-4 w-4 text-gray-400" />
      </Button>
    </div>
  );
};

const TemplateItem = ({ name, type, lastUsed }) => {
  const icons = {
    email: <Mail className="h-4 w-4" />,
    call: <Phone className="h-4 w-4" />
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex items-center space-x-3">
        <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
          {icons[type]}
        </div>
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">Last used {lastUsed}</div>
        </div>
      </div>
      <Button variant="ghost" size="icon">
        <Settings className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default SequenceBuilder;