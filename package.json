{"name": "dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3030", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@livekit/components-react": "^2.9.2", "@livekit/components-styles": "^1.1.5", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@tinymce/tinymce-react": "^6.1.0", "@types/pg": "^8.11.13", "axios": "^1.9.0", "calendar": "^0.1.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "livekit-client": "^2.11.2", "livekit-server-sdk": "^2.12.0", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "mermaid": "^11.6.0", "next": "15.0.2", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "openai": "^4.95.0", "p-limit": "^6.2.0", "papaparse": "^5.5.2", "pg": "^8.14.1", "playwright": "^1.52.0", "puppeteer": "^23.11.1", "react": "^18.3.1", "react-datasheet-grid": "^4.11.5", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-intersection-observer": "^9.16.0", "recharts": "^2.15.3", "sonner": "^1.7.4", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zlib": "^1.0.5", "zod": "^3.24.3"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "^20.17.30", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.36", "eslint": "^9.24.0", "eslint-config-next": "15.0.2", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}