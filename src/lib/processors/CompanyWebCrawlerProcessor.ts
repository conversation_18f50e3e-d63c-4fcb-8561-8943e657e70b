import { BaseProcessor, ProcessorOptions, EntityData } from './BaseProcessor';
import { CompanyProcessingState } from '../../types/processing';
import { v4 as uuidv4 } from 'uuid';
import * as playwright from 'playwright';
import * as cheerio from 'cheerio';
import { URL } from 'url';
import * as zlib from 'zlib';
import { promisify } from 'util';
import { createHash } from 'crypto';

const gzip = promisify(zlib.gzip);

interface CompanyData extends EntityData {
  company_id: number;
  company_name: string;
  company_website: string;
  processing_state: CompanyProcessingState;
}

interface CrawledPage {
  id: string;
  company_id: number;
  url: string;
  parent_url: string | null;
  crawl_depth: number;
  crawl_run_id: string;
  html_gzip: Buffer;
  extracted_text: string;
}

interface ProcessorOptionsExt extends ProcessorOptions {
  concurrency?: number;
}

/**
 * CompanyWebCrawlerProcessor
 * 
 * Crawls company websites to extract content for further processing.
 * - Processes companies in batches
 * - Uses concurrent browser contexts
 * - Stores raw HTML and extracted text in company_web_pages
 * - Follows links up to a maximum depth and page count
 */
export class CompanyWebCrawlerProcessor extends BaseProcessor {
  // Configuration
  private readonly MAX_DEPTH = 2;
  private readonly MAX_PAGES = 5;
  private readonly MAX_URLS_PER_DOMAIN = 15;
  private readonly CONCURRENCY = 3; // Reduced concurrency to avoid overloading
  private readonly USER_AGENT = "Mozilla/5.0 (compatible; AnaxBot/1.0; +https://anax.ai/bot)";
  private readonly PAGE_TIMEOUT = 45000; // Increased timeout for slow sites
  private readonly MAX_RETRIES = 3; // Increased retries
  private readonly RETRY_DELAY = 3000; // Delay between retries in ms
  
  // Runtime state
  private browser: playwright.Browser | null = null;
  private activePromises: Promise<unknown>[] = [];
  private semaphore: Semaphore;
  private stats = {
    companiesProcessed: 0,
    pagesScraped: 0,
    errors: 0
  };

  constructor(options: ProcessorOptionsExt = {}) {
    super('CompanyWebCrawler', options);
    this.semaphore = new Semaphore(options.concurrency || this.CONCURRENCY);
  }

  /**
   * Get unprocessed companies that need their websites crawled
   */
  async getUnprocessedEntities(): Promise<CompanyData[]> {
    // Use the standard helper method from BaseProcessor
    const companies = await this.getCompaniesForWebCrawling(
      this.options.limit,
      this.options.singleId
    );
    
    return companies.map(company => ({
      id: Number(company.company_id),
      company_id: Number(company.company_id),
      company_name: String(company.company_name),
      company_website: String(company.company_website),
      processing_state: company.processing_state as CompanyProcessingState
    }));
  }

  getProcessingStateCondition(): string {
    return `processing_state = 'contact_extracted'`;
  }

  /**
   * Initialize browser and process a company
   */
  async processEntity(entity: CompanyData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Starting to crawl website for company: ${entity.company_name} (${entity.company_website})`);
      
      // Check if this is a re-crawl of a previously processed company (singleId mode)
      const isReCrawl = this.options.singleId && entity.processing_state === 'website_scraped';
      
      if (isReCrawl) {
        this.log('info', `Re-crawling previously processed company ${entity.company_id}`);
        // Clear existing pages for re-crawl
        await this.clearExistingPages(entity.company_id);
      }
      
      // Initialize browser if needed
      if (!this.browser) {
        this.browser = await playwright.chromium.launch({
          headless: true,
          args: [
            '--disable-dev-shm-usage',
            '--no-sandbox',
            '--disable-gpu',
            '--disable-extensions'
          ]
        });
      }
      
      // Normalize the website URL
      const normalizedUrl = this.normalizeUrl(entity.company_website);
      if (!normalizedUrl) {
        return { success: false, error: 'Invalid website URL' };
      }
      
      // Crawl the website
      const crawlResult = await this.crawlWebsite(entity.company_id, normalizedUrl);
      // console.log('crawlResult', crawlResult);
      // Update statistics
      this.stats.companiesProcessed++;
      this.stats.pagesScraped += crawlResult.pageCount;
      
      this.log('info', `Successfully crawled ${crawlResult.pageCount} pages for ${entity.company_name}`);
      
      return { success: true };
    } catch (error) {
      this.stats.errors++;
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `Error crawling company ${entity.company_id}: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Update company status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      // Check if any pages were actually saved
      const checkSql = `
        SELECT COUNT(*) as page_count
        FROM company_web_pages
        WHERE company_id = $1
      `;
      
      const result = await this.query(checkSql, [entityId]);
      const pageCount = parseInt(String(result[0]?.page_count) || '0', 10);
      
      // Check if this is a manual processing (singleId mode)
      const isManualProcessing = this.options.singleId === entityId;
      
      if (success && pageCount > 0) {
        // Update the companies table to mark as processed
        this.log('info', `Company ${entityId} marked as processed with ${pageCount} pages saved${isManualProcessing ? ' (manual processing)' : ''}`);
      } else if (success && pageCount === 0) {
        // Success but no pages - don't mark as processed
        const updateSql = `
          UPDATE companies
          SET updated_at = $1
          WHERE company_id = $2
        `;
        
        await this.query(updateSql, [new Date(), entityId]);
        this.log('warn', `Not marking company ${entityId} as processed: no pages were saved${isManualProcessing ? ' (manual processing)' : ''}`);
      } else {
        // Failed processing - update timestamp only
        const updateSql = `
          UPDATE companies
          SET updated_at = $1
          WHERE company_id = $2
        `;
        
        await this.query(updateSql, [new Date(), entityId]);
        const errorMsg = error ? `: ${error}` : '';
        this.log('error', `Company ${entityId} processing failed${isManualProcessing ? ' (manual processing)' : ''}${errorMsg}, updated timestamp only`);
      }
    } catch (updateError) {
      this.log('error', `Error updating status for company ${entityId}: ${updateError}`);
    }
  }

  /**
   * Crawl a website using Playwright with retry capability
   */
  private async crawlWebsite(companyId: number, startUrl: string): Promise<{ pageCount: number }> {
    const seen = new Set<string>();
    const queue: Array<[string, string | null, number]> = [[startUrl, null, 0]]; // [url, parent, depth]
    const runId = uuidv4();
    let domain;
    
    try {
      domain = new URL(startUrl).hostname;
    } catch (error) {
      this.log('error', `Invalid URL: ${startUrl}`);
      return { pageCount: 0 };
    }
    
    await this.semaphore.acquire();
    
    try {
      const context = await this.browser!.newContext({
        userAgent: this.USER_AGENT,
        ignoreHTTPSErrors: true,
        viewport: { width: 1280, height: 800 }
      });
      
      const page = await context.newPage();
      await page.setDefaultTimeout(this.PAGE_TIMEOUT);
      
      while (queue.length > 0 && seen.size < this.MAX_PAGES) {
        const [url, parent, depth] = queue.shift()!;
        
        if (seen.has(url) || depth > this.MAX_DEPTH) {
          continue;
        }
        
        seen.add(url); // Mark as seen even if we fail to crawl
        
        // Try to crawl with retries
        let success = false;
        let html = '';
        let extractedText = '';
        
        for (let attempt = 0; attempt <= this.MAX_RETRIES; attempt++) {
          try {
            this.log('debug', `Crawling ${url} (depth: ${depth}, attempt: ${attempt + 1})`);
            
            // Set a longer timeout for navigation
            await page.goto(url, { 
              waitUntil: 'domcontentloaded',
              timeout: this.PAGE_TIMEOUT
            });
            
            // Wait a bit for dynamic content, longer for heavier pages
            await page.waitForTimeout(2000);
            
            // Try to ensure content is loaded
            try {
              // Wait for body to be available
              await page.waitForSelector('body', { timeout: 5000 });
            } catch (selectorError) {
              this.log('debug', `Could not find body element on ${url}: ${selectorError}`);
              // Continue anyway
            }
            
            html = await page.content();
            
            if (html.length > 500) { // Only consider pages with meaningful content
              extractedText = this.extractText(html);
              
              // Extra check for valid content
              if (extractedText.length > 100) {
                success = true;
                break;
              } else {
                this.log('debug', `Extracted text too short for ${url} (${extractedText.length} chars), retrying...`);
              }
            } else {
              this.log('debug', `Empty page content for ${url} (${html.length} chars), retrying...`);
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.log('warn', `Error crawling ${url} (attempt ${attempt + 1}/${this.MAX_RETRIES + 1}): ${errorMessage}`);
            
            if (attempt === this.MAX_RETRIES) {
              // Failed all attempts
              continue;
            }
            
            // Wait before retry with exponential backoff
            const backoffDelay = this.RETRY_DELAY * Math.pow(2, attempt);
            this.log('debug', `Retrying in ${backoffDelay}ms...`);
            await new Promise(resolve => setTimeout(resolve, backoffDelay));
          }
        }
        
        if (success && html) {
          // Store page data
          try {
            await this.storePage({
              id: uuidv4(),
              company_id: companyId,
              url,
              parent_url: parent,
              crawl_depth: depth,
              crawl_run_id: runId,
              html_gzip: await gzip(html),
              extracted_text: extractedText
            });
            
            // Don't continue crawling if we've reached max depth
            if (depth >= this.MAX_DEPTH) {
              continue;
            }
            
            // Extract links and add to queue
            const links = this.extractLinks(html, url, domain);
            
            for (const link of links) {
              if (!seen.has(link)) {
                queue.push([link, url, depth + 1]);
              }
            }
          } catch (error) {
            this.log('error', `Failed to store page ${url}: ${error}`);
          }
        }
      }
      
      await context.close();
      
      return { pageCount: seen.size };
    } finally {
      this.semaphore.release();
    }
  }

  /**
   * Extract text content from HTML
   */
  private extractText(html: string): string {
    try {
      const $ = cheerio.load(html);
      
      // Remove script and style elements
      $('script, style, meta, link, noscript').remove();
      
      // Get text content
      return $('body').text().replace(/\s+/g, ' ').trim();
    } catch (error) {
      this.log('warn', `Error extracting text: ${error}`);
      return '';
    }
  }

  /**
   * Extract links from HTML that belong to the same domain
   */
  private extractLinks(html: string, baseUrl: string, domain: string): string[] {
    try {
      const $ = cheerio.load(html);
      const links: string[] = [];
      const baseUrlObj = new URL(baseUrl);
      const base = `${baseUrlObj.protocol}//${domain}`;
      
      $('a[href]').each((_, el) => {
        let href = $(el).attr('href') || '';
        
        // Skip anchors and mailto links
        if (href.startsWith('#') || href.startsWith('mailto:')) {
          return;
        }
        
        // Resolve relative URLs
        if (!href.startsWith('http')) {
          try {
            href = new URL(href, base).toString();
          } catch {
            // Skip invalid URLs
            return;
          }
        }
        
        try {
          const hrefUrl = new URL(href);
          
          // Only include links from the same domain
          if (hrefUrl.hostname === domain) {
            // Normalize URL
            let normalizedUrl = `${hrefUrl.protocol}//${hrefUrl.hostname}${hrefUrl.pathname}`;
            
            // Include query params but not hash
            if (hrefUrl.search) {
              normalizedUrl += hrefUrl.search;
            }
            
            links.push(normalizedUrl);
          }
        } catch {
          // Skip invalid URLs
        }
      });
      
      return links;
    } catch (err) {
      this.log('warn', `Error extracting links: ${err instanceof Error ? err.message : String(err)}`);
      return [];
    }
  }

  /**
   * Store a crawled page in the database
   */
  private async storePage(page: CrawledPage): Promise<void> {
    const sql = `
      INSERT INTO company_web_pages
        (id, company_id, url, parent_url, crawl_depth, crawl_run_id,
         html_gzip, extracted_text, last_scraped_at, created_at, extracted)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW(), FALSE)
      ON CONFLICT DO NOTHING
    `;
    
    await this.query(sql, [
      page.id,
      page.company_id,
      page.url,
      page.parent_url,
      page.crawl_depth,
      page.crawl_run_id,
      page.html_gzip,
      page.extracted_text
    ]);
  }

  /**
   * Normalize a URL string
   */
  private normalizeUrl(url: string | null): string | null {
    if (!url) {
      return null;
    }
    
    url = url.trim().replace(/\s/g, '');
    
    if (url.includes(',')) {
      url = url.replace(/,/g, '.');
    }
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    if (url.startsWith('http://')) {
      url = url.replace('http://', 'https://');
    }
    
    try {
      const parsed = new URL(url);
      return parsed.hostname.includes('.') ? url : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Clean up resources when done
   */
  async cleanup(): Promise<void> {
    // Wait for any pending promises
    if (this.activePromises.length > 0) {
      await Promise.all(this.activePromises);
    }
    
    // Close the browser
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
    
    // Log final statistics
    this.log('info', `Crawler statistics: ${JSON.stringify(this.stats)}`);
  }

  /**
   * Scrape a website and extract links
   */
  private async scrapePage(url: string, domain: string, pageDepth: number, visitedUrls: Set<string>): Promise<void> {
    if (visitedUrls.has(url) || visitedUrls.size >= this.MAX_URLS_PER_DOMAIN) {
      return;
    }
    
    // Add to visited set before scraping to prevent concurrent requests to the same URL
    visitedUrls.add(url);
    
    try {
      this.log('debug', `Scraping page: ${url} (depth: ${pageDepth})`);
      
      // Create a new context for each page to avoid cookie/cache issues
      const context = await this.browser!.newContext({
        userAgent: this.USER_AGENT,
        viewport: { width: 1280, height: 720 }
      });
      
      const page = await context.newPage();
      await page.setDefaultTimeout(this.PAGE_TIMEOUT);
      
      // Navigate to the URL
      const response = await page.goto(url, { waitUntil: 'domcontentloaded' });
      
      if (!response || !response.ok()) {
        this.log('debug', `Failed to load page: ${url} - Status: ${response?.status() || 'unknown'}`);
        await context.close();
        return;
      }
      
      // Wait for content to load
      await page.waitForLoadState('domcontentloaded');
      
      // Get page content
      const content = await page.content();
      
      // Get page title
      const title = await page.title();
      
      // Get page description
      const description = await page.$eval('head > meta[name="description"]', (el) => el.getAttribute('content')).catch(() => null);
      
      // Extract metadata
      const metadata = {
        title: title || '',
        description: description || '',
        url,
        content_hash: createHash('md5').update(content).digest('hex'),
        scraped_at: new Date().toISOString()
      };
      
      // Save page data
      await this.storePage({
        id: uuidv4(),
        company_id: Number(metadata.url.split('/')[2].split('.')[0]), // Extract from URL properly
        url: metadata.url,
        parent_url: null,
        crawl_depth: pageDepth,
        crawl_run_id: uuidv4(),
        html_gzip: await gzip(content),
        extracted_text: this.extractText(content)
      });
      
      // Extract links only if we haven't reached max depth
      if (pageDepth < this.MAX_DEPTH) {
        const links = this.extractLinks(content, url, domain);
        const newLinks = links.filter(link => !visitedUrls.has(link));
        
        // Add links to crawl queue
        for (const link of newLinks) {
          // Launch the crawler for this URL
          this.activePromises.push(
            this.semaphore.acquire().then(async () => {
              try {
                await this.scrapePage(link, domain, pageDepth + 1, visitedUrls);
              } finally {
                this.semaphore.release();
              }
            })
          );
        }
      }
      
      // Close browser context to free resources
      await context.close();
      
      // Update stats
      this.stats.pagesScraped++;
      
    } catch (err) {
      // Log error but don't fail the entire crawl
      this.log('warn', `Error scraping page ${url}: ${err instanceof Error ? err.message : String(err)}`);
    }
  }

  /**
   * Clear existing web pages for a company
   * This is used when re-crawling a previously processed company
   */
  private async clearExistingPages(companyId: number): Promise<void> {
    // First count existing pages
    const countSql = `
      SELECT COUNT(*) as page_count
      FROM company_web_pages
      WHERE company_id = $1
    `;
    
    try {
      // Get count before deletion
      const countResult = await this.query(countSql, [companyId]);
      const pageCount = parseInt(String(countResult[0]?.page_count) || '0', 10);
      
      if (pageCount === 0) {
        this.log('info', `No existing web pages found for company ${companyId}`);
        return;
      }
      
      // Perform deletion
      const deleteSql = `
        DELETE FROM company_web_pages
        WHERE company_id = $1
      `;
      
      await this.query(deleteSql, [companyId]);
      
      this.log('info', `Cleared ${pageCount} existing web pages for company ${companyId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `Error clearing existing web pages for company ${companyId}: ${errorMessage}`);
    }
  }
}

/**
 * Semaphore for limiting concurrency
 */
class Semaphore {
  private count: number;
  private queue: Array<() => void> = [];

  constructor(count: number) {
    this.count = count;
  }

  async acquire(): Promise<void> {
    if (this.count > 0) {
      this.count--;
      return Promise.resolve();
    }

    return new Promise<void>(resolve => {
      this.queue.push(resolve);
    });
  }

  release(): void {
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      if (next) next();
    } else {
      this.count++;
    }
  }
} 