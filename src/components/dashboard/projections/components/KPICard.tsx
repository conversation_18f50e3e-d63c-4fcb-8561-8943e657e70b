import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface KPICardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  explanation?: string;
}

export const KPICard = ({ title, value, icon: Icon, explanation }: KPICardProps) => (
  <Card className="flex-1">
    <CardContent className="pt-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-lg font-semibold text-gray-900">{title}</p>
          <h3 className="text-2xl font-bold mt-2">{value}</h3>
          {explanation && (
            <p className="text-xs text-gray-500 mt-2 bg-gray-100 px-2 py-1 rounded-md">
              {explanation}
            </p>
          )}
        </div>
        <Icon className="h-8 w-8 text-gray-400" />
      </div>
    </CardContent>
  </Card>
); 