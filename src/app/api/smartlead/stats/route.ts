import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    // Get counts of contacts by smartlead_status
    const { rows: statusCounts } = await pool.query(`
      SELECT 
        smartlead_status AS status,
        COUNT(*) AS count
      FROM contacts
      WHERE smartlead_lead_id IS NOT NULL
      AND email IS NOT NULL
      AND email_generated = true
      GROUP BY smartlead_status
      ORDER BY CASE 
        WHEN smartlead_status = 'SENT' THEN 1
        WHEN smartlead_status = 'DELIVERED' THEN 2
        WHEN smartlead_status = 'OPENED' THEN 3
        WHEN smartlead_status = 'REPLIED' THEN 4
        WHEN smartlead_status = 'BOUNCED' THEN 5
        WHEN smartlead_status = 'UNSUBSCRIBED' THEN 6
        ELSE 99
      END
    `);

    // Get count of contacts that can be sent (not yet in Smartlead)
    const { rows: pendingCount } = await pool.query(`
      SELECT COUNT(*) AS count
      FROM contacts
      WHERE smartlead_lead_id IS NULL 
        AND email IS NOT NULL
        AND email_generated = true
    `);

    // Get counts per day for the last 30 days
    const { rows: dailyCounts } = await pool.query(`
      SELECT 
        DATE_TRUNC('day', last_email_sent_at) AS date,
        COUNT(*) AS count
      FROM contacts
      WHERE last_email_sent_at IS NOT NULL
        AND email IS NOT NULL   
        AND email_generated = true
        AND last_email_sent_at > NOW() - INTERVAL '30 days'
      GROUP BY DATE_TRUNC('day', last_email_sent_at)
      ORDER BY date
    `);

    // Get recent activity - latest status changes
    const { rows: recentActivity } = await pool.query(`
      SELECT 
        c.contact_id,
        c.first_name,
        c.last_name,
        c.email,
        c.smartlead_status,
        c.last_email_sent_at,
        co.company_name
      FROM contacts c
      JOIN companies co USING (company_id)
      WHERE c.smartlead_lead_id IS NOT NULL
        AND c.email IS NOT NULL
        AND c.email_generated = true
      ORDER BY c.last_email_sent_at DESC
      LIMIT 10
    `);

    return NextResponse.json({
      statusCounts,
      pendingCount: pendingCount[0]?.count || 0,
      dailyCounts,
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching Smartlead stats:', error);
    return NextResponse.json(
      { error: `Error fetching stats: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 