import psycopg2
from psycopg2.extras import execute_batch
from openai import OpenAI
import time
from typing import List, <PERSON>ple
import os
from tqdm import tqdm
from dotenv import load_dotenv

# Load environment variables from .env.local
env_path = '/opt/anax/dash/.env.local'
if not load_dotenv(env_path):
    raise Exception(f"Could not load environment file at {env_path}")

# Verify required environment variables
required_vars = ['OPENAI_API_KEY', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST']
missing_vars = [var for var in required_vars if not os.getenv(var)]
if missing_vars:
    raise Exception(f"Missing required environment variables: {', '.join(missing_vars)}")

class EmbeddingCreator:
    def __init__(self):
        # Initialize OpenAI client
        self.client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Database connection
        self.conn = psycopg2.connect(
            dbname=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT', '5432')
        )
        self.conn.autocommit = True

    def get_unembedded_articles(self) -> List[Tuple[int, str, str, str, str]]:
        """Fetch articles that don't have embeddings yet"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT 
                    id, 
                    COALESCE(news_title, '') as news_title, 
                    news_text, 
                    COALESCE(location, '') as location, 
                    COALESCE(news_date::text, '') as news_date
                FROM deal_news 
                WHERE content_embedding IS NULL 
                AND news_text IS NOT NULL
                AND news_text != ''
                LIMIT 100
            """)
            return cur.fetchall()

    def create_embedding(self, text: str) -> List[float]:
        """Create embedding using OpenAI API"""
        try:
            response = self.client.embeddings.create(
                model="text-embedding-3-small",  # or text-embedding-ada-002
                input=text,
                encoding_format="float"
            )
            return response.data[0].embedding
        except Exception as e:
            print(f"Error creating embedding: {e}")
            return None

    def update_embeddings(self, id: int, embedding: List[float]) -> None:
        """Update the database with the new embedding"""
        with self.conn.cursor() as cur:
            cur.execute(
                "UPDATE deal_news SET content_embedding = %s WHERE id = %s",
                (embedding, id)
            )

    def process_batch(self):
        """Process a batch of articles"""
        articles = self.get_unembedded_articles()
        
        if not articles:
            print("No articles to process")
            return 0

        for id, title, text, location, date in tqdm(articles, desc="Processing articles"):
            # Build combined text, including optional fields only if they exist
            combined_parts = []
            
            if title:
                combined_parts.append(f"Title: {title}")
            if date:
                combined_parts.append(f"Date: {date}")
            if location:
                combined_parts.append(f"Location: {location}")
                
            combined_parts.append(f"Content: {text}")
            
            combined_text = "\n".join(combined_parts)
            
            # Debug output - show first article's full text, then just lengths for others
            if id == articles[0][0]:  # First article
                print("\n=== Sample Article Text ===")
                print(combined_text)
                print("\n=== End Sample ===\n")
            else:
                print(f"\nArticle {id} - Text length: {len(combined_text)} chars")
            
            # Create embedding
            embedding = self.create_embedding(combined_text)
            
            if embedding:
                # Update database
                self.update_embeddings(id, embedding)
                
                # Rate limiting - 3 requests per second for OpenAI API
                time.sleep(0.34)
            else:
                print(f"Skipping article {id} due to embedding error")

        return len(articles)

    def close(self):
        """Close database connection"""
        self.conn.close()

def main():
    creator = EmbeddingCreator()
    try:
        total_processed = 0
        while True:
            processed = creator.process_batch()
            if processed == 0:
                break
            total_processed += processed
            print(f"Processed {total_processed} articles so far")
    finally:
        creator.close()

if __name__ == "__main__":
    main()