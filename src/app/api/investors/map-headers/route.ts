import { NextRequest, NextResponse } from 'next/server'
import { LLMFactory } from '@/lib/llm'
import { HEADER_MAPPING_SYSTEM_PROMPT, HEADER_MAPPING_USER_TEMPLATE } from '@/lib/prompts'
import { pool } from '@/lib/db'

interface HeaderMappingRequest {
  headers: string[]
  context?: string
}

interface HeaderMappingResponse {
  success: boolean
  company_mappings?: Record<string, string[]>
  contact_mappings?: Record<string, string[]>
  unmapped_headers?: string[]
  suggestions?: {
    missing_recommended_fields: string[]
    data_quality_notes: string[]
  }
  error?: string
}

export async function POST(request: NextRequest): Promise<NextResponse<HeaderMappingResponse>> {
  try {
    const body: HeaderMappingRequest = await request.json()
    const { headers, context } = body

    if (!headers || !Array.isArray(headers) || headers.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Headers array is required and cannot be empty'
      }, { status: 400 })
    }

    // Initialize LLM provider
    const llmProvider = LLMFactory.createProvider('openai', {
      log: (level: string, message: string) => {
        console.log(`[${level.toUpperCase()}] Header Mapping: ${message}`)
      },
    })

    // Fetch the companies and contacts table columns
    const fieldsResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3030'}/api/investors/get-database-fields`)
    const fieldsData = await fieldsResponse.json()
    
    if (!fieldsData.success) {
      throw new Error('Failed to fetch database fields')
    }

    // Prepare the prompt with dynamic fields
    const systemPrompt = HEADER_MAPPING_SYSTEM_PROMPT
      .replace('{companies_table_fields}', fieldsData.companies.map((field: string) => `- ${field}`).join('\n'))
      .replace('{contacts_table_fields}', fieldsData.contacts.map((field: string) => `- ${field}`).join('\n'))

    // Extract sample data from context if provided
    let sampleData = 'No sample data provided'
    let cleanContext = context || ''
    
    if (context && context.includes('Sample data:')) {
      const sampleMatch = context.match(/Sample data: (.+)$/)
      if (sampleMatch) {
        try {
          const parsedSample = JSON.parse(sampleMatch[1])
          sampleData = JSON.stringify(parsedSample, null, 2)
          cleanContext = context.replace(/Sample data: .+$/, '').trim()
        } catch (e) {
          sampleData = sampleMatch[1]
        }
      }
    }

    const userPrompt = HEADER_MAPPING_USER_TEMPLATE
      .replace('{headers}', JSON.stringify(headers, null, 2))
      .replace('{sample_data}', sampleData)

    // Add additional context if provided
    const finalPrompt = cleanContext 
      ? `${userPrompt}\n\n### Additional Context:\n${cleanContext}`
      : userPrompt

    console.log('Sending header mapping request to LLM...')
    console.log('Headers to map:', headers)

    // Call LLM
    const response = await llmProvider.callLLM([
      { role: 'system', content: systemPrompt },
      { role: 'user', content: finalPrompt }
    ], {
      model: 'gpt-4o-mini',
      temperature: 0.5, // Low temperature for consistent mapping
      maxTokens: 2000
    })

    console.log('LLM Response:', response.content)

    // Parse the JSON response
    let mappingResult
    try {
      // Extract JSON from response if it's wrapped in markdown
      const jsonMatch = response.content.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      const jsonContent = jsonMatch ? jsonMatch[1] : response.content
      
      mappingResult = JSON.parse(jsonContent)
    } catch (parseError) {
      console.error('Failed to parse LLM response as JSON:', parseError)
      return NextResponse.json({
        success: false,
        error: 'Failed to parse mapping response from AI'
      }, { status: 500 })
    }

    // Validate the response structure
    if (!mappingResult.company_mappings && !mappingResult.contact_mappings) {
      return NextResponse.json({
        success: false,
        error: 'Invalid mapping response structure'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      company_mappings: mappingResult.company_mappings || {},
      contact_mappings: mappingResult.contact_mappings || {},
      unmapped_headers: mappingResult.unmapped_headers || [],
      suggestions: mappingResult.suggestions || {
        missing_recommended_fields: [],
        data_quality_notes: []
      }
    })

  } catch (error) {
    console.error('Header mapping error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
}

export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    message: 'Header mapping endpoint. Use POST with headers array.',
    example: {
      headers: ['Company Name', 'Website', 'First Name', 'Last Name', 'Email'],
      context: 'Optional additional context about the data'
    }
  })
} 