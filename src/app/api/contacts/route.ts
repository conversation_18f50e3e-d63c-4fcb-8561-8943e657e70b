import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const source = searchParams.get('source') || ''
    const sort = searchParams.get('sort') || 'updated_at'
    const direction = searchParams.get('direction') || 'desc'
    const extractedOnly = searchParams.get('extracted_only') === 'true'
    const emailGenerated = searchParams.get('email_generated') === 'true'
    const smartleadLeadId = searchParams.get('smartlead_lead_id')
    const campaignId = searchParams.get('campaign_id')
    const offset = (page - 1) * limit

    // Validate sort field to prevent SQL injection
    const validSortFields = [
      'contact_id', 'first_name', 'last_name', 'full_name', 
      'title', 'job_title', 'company_name', 'updated_at', 'source', 'email_status', 
      'extracted', 'searched', 'email_generated', 'smartlead_lead_id'
    ];
    const sortField = validSortFields.includes(sort) ? sort : 'updated_at';
    
    // Validate direction
    const sortDirection = direction.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Map frontend sort fields to actual DB columns where needed
    let orderByField = sortField;
    if (sortField === 'job_title') orderByField = 'co.title';
    else if (sortField === 'updated_at') orderByField = 'co.updated_at';
    else if (sortField === 'full_name') orderByField = 'co.full_name';
    else if (sortField === 'company_name') orderByField = 'c.company_name';
    else if (sortField === 'source') orderByField = 'co.source';
    else if (sortField === 'email_status') orderByField = 'co.email_status';
    else if (sortField === 'extracted') orderByField = 'ced.created_at';
    else if (sortField === 'searched') orderByField = 'csd.created_at';
    else if (sortField === 'email_generated') orderByField = 'co.email_generated';
    else if (sortField === 'smartlead_lead_id') orderByField = 'co.smartlead_lead_id';

    // Use COALESCE to handle NULL values in sorting
    const orderByClause = sortField === 'email_status' 
      ? `COALESCE(${orderByField}, '') ${sortDirection}, co.full_name ASC`
      : sortField === 'extracted'
      ? `CASE WHEN ced.created_at IS NULL THEN 0 ELSE 1 END ${sortDirection}, ced.created_at ${sortDirection}, co.full_name ASC`
      : sortField === 'searched'
      ? `CASE WHEN csd.created_at IS NULL THEN 0 ELSE 1 END ${sortDirection}, csd.created_at ${sortDirection}, co.full_name ASC`
      : sortField === 'email_generated'
      ? `CASE WHEN co.email_generated IS NULL THEN 0 ELSE 1 END ${sortDirection}, co.email_generated ${sortDirection}, co.searched ${sortDirection}, co.extracted ${sortDirection}, co.full_name ASC`
      : sortField === 'smartlead_lead_id'
      ? `CASE WHEN co.smartlead_lead_id IS NULL THEN 0 ELSE 1 END ${sortDirection}, co.smartlead_lead_id ${sortDirection}, co.email_generated ${sortDirection}, co.searched ${sortDirection}, co.extracted ${sortDirection}, co.full_name ASC`
      : `${orderByField} ${sortDirection}`;

    const whereClauses = [
      `(
        LOWER(co.first_name) LIKE LOWER($3) OR
        LOWER(co.last_name) LIKE LOWER($3) OR
        LOWER(co.full_name) LIKE LOWER($3) OR
        LOWER(c.company_name) LIKE LOWER($3) OR
        LOWER(co.title) LIKE LOWER($3)
      )`
    ];
    const params:any[] = [limit, offset, `%${search}%`];

    if (source) {
      whereClauses.push(`co.source = $${params.length + 1}`);
      params.push(source);
    }
    
    // Add filter for extracted contacts
    if (extractedOnly) {
      whereClauses.push(`ced.contact_id IS NOT NULL`);
    }

    // Add filter for email generated contacts
    if (emailGenerated) {
      whereClauses.push(`co.email_generated = true`);
    }

    // Add filter for contacts with smartlead_lead_id
    if (smartleadLeadId === 'not_null') {
      whereClauses.push(`co.smartlead_lead_id IS NOT NULL`);
    }

    if (campaignId) {
      whereClauses.push(`co.contact_id IN (
        SELECT tp.contact_id
        FROM thread_participants tp
        JOIN messages m ON tp.thread_id = m.thread_id
        WHERE m.smartlead_campaign_id = $${params.length + 1}
      )`);
      params.push(campaignId);
    }

    const query = `
      SELECT 
        co.contact_id::integer AS contact_id,
        co.first_name,
        co.last_name,
        co.full_name,
        co.title as job_title,
        co.headline,
        co.seniority,
        co.email,
        co.personal_email,
        co.email_status,
        co.linkedin_url,
        co.phone_number,
        co.contact_city,
        co.contact_state,
        co.contact_country,
        co.region,
        co.capital_type,
        co.contact_category,
        co.investment_criteria_country,
        co.investment_criteria_state,
        co.investment_criteria_city,
        co.investment_criteria_property_type,
        co.investment_criteria_asset_type,
        co.investment_criteria_loan_type,
        co.investment_criteria_deal_size,
        co.notes,
        co.source,
        co.updated_at,
        c.company_name,
        c.company_city,
        c.company_state,
        c.company_website,
        c.industry,
        co.smartlead_lead_id,
        co.smartlead_status,
        CASE WHEN ced.contact_id IS NOT NULL THEN true ELSE false END as extracted,
        ced.created_at as extraction_date,
        CASE WHEN csd.contact_id IS NOT NULL THEN true ELSE false END as searched,
        csd.created_at as searched_date,
        co.email_generated as email_generated,
        co.email_validated_date,
        COUNT(*) OVER() as total_count
      FROM contacts co
      LEFT JOIN companies c ON co.company_id = c.company_id
      LEFT JOIN contact_extracted_data ced ON co.contact_id = ced.contact_id
      LEFT JOIN contact_searched_data csd ON co.contact_id = csd.contact_id
      WHERE ${whereClauses.join(' AND ')}
      ORDER BY ${orderByClause}
      LIMIT $1 OFFSET $2
    `
    
    console.log('Executing query with sort:', orderByField, sortDirection);
    const result = await pool.query(query, params)
    
    const totalCount = result.rows[0]?.total_count || 0
    console.log('API response constructed with:', {
      contactsCount: result.rows.length,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page
    })
    return NextResponse.json({
      contacts: result.rows,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      totalCount
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contacts' }, 
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const {
      first_name,
      last_name,
      email,
      personal_email,
      title,
      phone_number,
      company_name,
      company_website,
      industry,
      company_address,
      company_city,
      company_state,
      company_country,
      contact_city,
      contact_state,
      contact_country,
      region,
      linkedin_url,
      company_id: providedCompanyId,
      // Additional fields to store in extra_attrs
      capital_type,
      contact_category,
      investment_criteria_country,
      investment_criteria_state,
      investment_criteria_city,
      investment_criteria_property_type,
      investment_criteria_asset_type,
      investment_criteria_loan_type,
      investment_criteria_deal_size,
      notes
    } = body

    let company_id = providedCompanyId;

    // If no company_id provided, create or update company
    if (!company_id) {
      const companyResult = await pool.query(
        `INSERT INTO companies (
          company_name, 
          company_website, 
          industry, 
          company_address, 
          company_city, 
          company_state, 
          company_country
        ) 
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (company_name) DO UPDATE 
        SET 
          company_website = COALESCE(EXCLUDED.company_website, companies.company_website),
          industry = COALESCE(EXCLUDED.industry, companies.industry),
          company_address = COALESCE(EXCLUDED.company_address, companies.company_address),
          company_city = COALESCE(EXCLUDED.company_city, companies.company_city),
          company_state = COALESCE(EXCLUDED.company_state, companies.company_state),
          company_country = COALESCE(EXCLUDED.company_country, companies.company_country)
        RETURNING company_id`,
        [company_name, company_website, industry, company_address, company_city, company_state, company_country]
      )
      company_id = companyResult.rows[0].company_id;
    }

    // Prepare extra attributes for any additional fields not covered by specific columns
    const extraAttrs: Record<string, any> = {};
    
    // Add any additional non-standard fields to extra_attrs if needed
    // Note: Most fields are now stored in dedicated columns

    // Insert the contact with all the new dedicated columns
    const contactResult = await pool.query(
      `INSERT INTO contacts (
        first_name, 
        last_name,
        full_name,
        title,
        email, 
        personal_email,
        linkedin_url,
        phone_number,
        company_id,
        contact_city,
        contact_state,
        contact_country,
        region,
        capital_type,
        contact_category,
        investment_criteria_country,
        investment_criteria_state,
        investment_criteria_city,
        investment_criteria_property_type,
        investment_criteria_asset_type,
        investment_criteria_loan_type,
        investment_criteria_deal_size,
        notes,
        extra_attrs
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24)
      RETURNING contact_id`,
      [
        first_name,
        last_name,
        `${first_name} ${last_name}`,
        title,
        email,
        personal_email,
        linkedin_url,
        phone_number,
        company_id,
        contact_city,
        contact_state,
        contact_country,
        region,
        capital_type,
        contact_category,
        investment_criteria_country,
        investment_criteria_state,
        investment_criteria_city,
        investment_criteria_property_type,
        investment_criteria_asset_type,
        investment_criteria_loan_type,
        investment_criteria_deal_size,
        notes,
        JSON.stringify(extraAttrs)
      ]
    )

    return NextResponse.json(contactResult.rows[0])
  } catch (error) {
    console.error('Error adding contact:', error)
    return NextResponse.json(
      { error: 'Failed to add contact' },
      { status: 500 }
    )
  }
} 