import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';



/**
 * GET handler for fetching date-specific statistics
 * Supports retrieving stats for either the current day or a specific date
 */
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const date = searchParams.get('date') || 'today';
  
  // Build the SQL date filter condition
  let dateFilter = "DATE(created_at) = CURRENT_DATE";
  if (date !== 'today') {
    // If a specific date is provided, use that instead
    dateFilter = `DATE(created_at) = '${date}'`;
  }
  
  let client;
  try {
    client = await pool.connect();
    
    // Query to get statistics for the specified date
    const result = await client.query(`
      -- Get articles counts by source for the specified date
      WITH date_stats AS (
        SELECT
          news_source,
          COUNT(*) as date_count
        FROM deal_news
        WHERE ${dateFilter}
        GROUP BY news_source
      ),
      -- Join with overall source stats
      source_stats AS (
        SELECT
          news_source,
          COUNT(*) as total_count,
          COALESCE((
            SELECT date_count 
            FROM date_stats ts 
            WHERE ts.news_source = dn.news_source
          ), 0) as date_count
        FROM deal_news dn
        GROUP BY news_source
      ),
      -- Get hourly distribution for the specified date
      hourly_stats AS (
        SELECT
          EXTRACT(HOUR FROM created_at) as hour,
          COUNT(*) as count
        FROM deal_news
        WHERE ${dateFilter}
        GROUP BY EXTRACT(HOUR FROM created_at)
        ORDER BY hour
      )
      SELECT
        -- Total articles for the date
        (SELECT COUNT(*) FROM deal_news WHERE ${dateFilter}) as date_total,
        
        -- Source breakdown with both total and date-specific counts
        (SELECT json_agg(json_build_object(
          'news_source', ss.news_source,
          'total_count', ss.total_count,
          'date_count', ss.date_count
        )) FROM source_stats ss) as source_stats,
        
        -- Hourly distribution for the date
        (SELECT json_agg(json_build_object(
          'hour', hs.hour,
          'count', hs.count
        )) FROM hourly_stats hs) as hourly_stats
    `);
    
    // Format and return the response
    return NextResponse.json({
      dateTotal: parseInt(result.rows[0].date_total || '0'),
      sourceStats: result.rows[0].source_stats || [],
      hourlyStats: result.rows[0].hourly_stats || [],
      date: date === 'today' ? new Date().toISOString().split('T')[0] : date
    });
    
  } catch (error: any) {
    console.error('Error fetching stats:', error);
    return NextResponse.json(
      { error: 'Database error', details: error.message },
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
} 