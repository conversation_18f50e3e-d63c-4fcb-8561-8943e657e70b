"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Contact } from '../types';

interface ExecutiveSummarySectionProps {
  contact?: Contact;
}

const ExecutiveSummarySection: React.FC<ExecutiveSummarySectionProps> = ({ contact }) => {
  if (!contact?.executive_summary) return null;
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Executive Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm leading-relaxed whitespace-pre-line">{contact.executive_summary}</p>
      </CardContent>
    </Card>
  );
};

export default ExecutiveSummarySection; 