#!/bin/bash

# Set path to project
SCRIPT_DIR="/opt/anax/dash/scripts"
LOG_DIR="/opt/anax/dash/logs"
VENV_DIR="/opt/anax/dash/scripts/venv"
LOCK_FILE="/tmp/deal_news_embeddings.lock"
ENV_FILE="/opt/anax/dash/.env.local"

# Create logs directory if it doesn't exist
mkdir -p $LOG_DIR

# Check if script is already running
if [ -f "$LOCK_FILE" ]; then
    echo "Script is already running. Exiting."
    exit 1
fi

# Create lock file
touch "$LOCK_FILE"

# Function to clean up lock file
cleanup() {
    rm -f "$LOCK_FILE"
}

# Set up trap to clean up lock file on script exit
trap cleanup EXIT

# Check if virtual environment exists
if [ ! -d "$VENV_DIR" ]; then
    echo "Virtual environment not found. Creating..."
    python3 -m venv $VENV_DIR
    source $VENV_DIR/bin/activate
    pip install openai
else
    source $VENV_DIR/bin/activate
fi

# Check if environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Environment file not found at $ENV_FILE"
    exit 1
fi

# Load environment variables
source $ENV_FILE

# Verify OPENAI_API_KEY is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "OPENAI_API_KEY is not set in $ENV_FILE"
    exit 1
fi

# Log start time
echo "=== Deal News Embedding Creation Started at $(date) ===" >> "$LOG_DIR/embedding_creation.log"

# Run the Python script
python $SCRIPT_DIR/deal-news-embeddings.py

# Log completion
echo "=== Deal News Embedding Creation Completed at $(date) ===" >> "$LOG_DIR/embedding_creation.log"
echo "----------------------------------------" >> "$LOG_DIR/embedding_creation.log" 