import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
    const event = await req.json();
    const { 
      lead_id, 
      event_name, // SENT, DELIVERED, OPENED, REP<PERSON>IED, BO<PERSON>CED, UNSUBSCRIBED
      timestamp,
      subject, 
      body_html 
    } = event;
    
    console.log(`Received Smartlead webhook: ${event_name} for lead ${lead_id}`);
    
    // Convert Unix timestamp (milliseconds) to Date if needed
    const eventDate = timestamp ? new Date(timestamp) : new Date();
    
    // Find the contact associated with this lead_id
    const { rows } = await pool.query(
      `SELECT contact_id, email FROM contacts WHERE smartlead_lead_id = $1`,
      [lead_id]
    );
    
    if (!rows.length) {
      console.error(`Lead ID ${lead_id} not found in contacts table`);
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 });
    }
    
    const contactId = rows[0].contact_id;
    const contactEmail = rows[0].email;
    
    // Update the contact's Smartlead status
    await pool.query(
      `UPDATE contacts SET smartlead_status = $1 WHERE contact_id = $2`,
      [event_name, contactId]
    );
    
    // If this is a reply, we need to create a new message in the thread
    if (event_name === 'REPLIED') {
      // Find the most recent thread for this contact
      const threadResult = await pool.query(`
        SELECT thread_id FROM thread_participants
        WHERE contact_id = $1 
        ORDER BY thread_id DESC 
        LIMIT 1
      `, [contactId]);
      
      if (!threadResult.rows.length) {
        console.error(`No thread found for contact ${contactId}`);
        return NextResponse.json({ error: 'Thread not found' }, { status: 404 });
      }
      
      const threadId = threadResult.rows[0].thread_id;
      
      // Create the inbound message record
      await pool.query(`
        INSERT INTO messages(
          thread_id, 
          from_email, 
          to_email, 
          subject,
          direction, 
          role, 
          body, 
          metadata,
          created_at
        )
        VALUES ($1, $2, '<EMAIL>', $3, 'inbound', 'contact', $4, $5, $6)
      `, [
        threadId, 
        contactEmail, 
        subject || 'Re: (No subject)', 
        body_html || '(No content)', 
        JSON.stringify(event),
        eventDate
      ]);
      
      console.log(`Created new inbound message in thread ${threadId} for contact ${contactId}`);
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `Processed ${event_name} event for lead ${lead_id}` 
    });
  } catch (error) {
    console.error('Error processing Smartlead webhook:', error);
    return NextResponse.json(
      { error: `Internal server error: ${(error as Error).message}` }, 
      { status: 500 }
    );
  }
} 