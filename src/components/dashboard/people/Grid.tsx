"use client"

import React, { useState, useEffect } from 'react'
import {
  DataSheetGrid,
  textColumn,
  keyColumn,
} from 'react-datasheet-grid'
import 'react-datasheet-grid/dist/style.css'
import { Search } from 'lucide-react'
import { Input } from "@/components/ui/input"
import debounce from 'lodash/debounce'

interface Person {
  person_id: number
  first_name: string
  last_name: string
  email: string
  phone_number: string
  job_title: string
  capital_type: string
  contact_source: string
  company_name: string
}

export const Grid = () => {
  const [data, setData] = useState<Person[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize] = useState(50)
  const [containerHeight, setContainerHeight] = useState(0)

  useEffect(() => {
    const updateHeight = () => {
      const navHeight = 64
      const searchBarHeight = 72
      const windowHeight = window.innerHeight
      const availableHeight = windowHeight - navHeight - searchBarHeight
      setContainerHeight(availableHeight)
    }

    updateHeight()
    window.addEventListener('resize', updateHeight)
    return () => window.removeEventListener('resize', updateHeight)
  }, [])

  const fetchData = async (search: string, currentPage: number) => {
    setLoading(true)
    try {
      const response = await fetch(
        `/api/people?page=${currentPage}&limit=${pageSize}&search=${encodeURIComponent(search)}`
      )
      const result = await response.json()
      
      if (Array.isArray(result.persons)) {
        setData(result.persons)
        setTotalPages(result.totalPages || 1)
      }
    } catch (err) {
      console.error('Error fetching people:', err)
      setData([])
    } finally {
      setLoading(false)
    }
  }

  const debouncedFetch = debounce((search: string) => {
    setPage(1)
    fetchData(search, 1)
  }, 300)

  useEffect(() => {
    fetchData(searchTerm, page)
  }, [page])

  useEffect(() => {
    if (searchTerm !== '') {
      debouncedFetch(searchTerm)
    }
  }, [searchTerm])

  const columns = [
    {
      ...keyColumn('person_id', textColumn),
      title: 'ID',
      width: 80,
    },
    {
      ...keyColumn('first_name', textColumn),
      title: 'First Name',
      width: 150,
    },
    {
      ...keyColumn('last_name', textColumn),
      title: 'Last Name',
      width: 150,
    },
    {
      ...keyColumn('email', textColumn),
      title: 'Email',
      width: 250,
    },
    {
      ...keyColumn('job_title', textColumn),
      title: 'Job Title',
      width: 200,
    },
    {
      ...keyColumn('company_name', textColumn),
      title: 'Company',
      width: 200,
    },
    {
      ...keyColumn('capital_type', textColumn),
      title: 'Capital Type',
      width: 150,
    },
    {
      ...keyColumn('status', textColumn),
      title: 'Status',
      width: 120,
    }
  ]

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between py-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search people..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <button 
            className="hover:text-gray-700 disabled:text-gray-300"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1 || loading}
          >
            Previous
          </button>
          <span>
            Page {page} of {totalPages}
          </span>
          <button 
            className="hover:text-gray-700 disabled:text-gray-300"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages || loading}
          >
            Next
          </button>
        </div>
      </div>

      <div 
        className="flex-1 border rounded-md bg-white overflow-hidden"
        style={{ height: `${containerHeight}px` }}
      >
        <DataSheetGrid
          value={data}
          onChange={setData}
          columns={columns}
          rowHeight={40}
          headerHeight={40}
          height={containerHeight}
          lockRows
          className="w-full"
          loading={loading}
          rowClassName={(row, rowIndex) => 
            `relative before:content-[${((page - 1) * pageSize) + rowIndex + 1}] before:absolute before:left-[-30px] before:text-gray-500 before:text-sm`
          }
        />
      </div>
    </div>
  )
}