import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Enhanced processing stats API with business-friendly presentation
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source') || 'all';
    const dateRange = searchParams.get('dateRange') || '7d';

    const client = await pool.connect();

    try {
      const days = getDaysFromRange(dateRange);
      const filters = buildFilters(source);

      // Get executive summary
      const executiveSummary = await getExecutiveSummary(client, filters, days);
      
      // Get stage performance metrics
      const stagePerformance = await getStagePerformance(client, filters, days);
      
      // Get productivity metrics
      const productivity = await getProductivityMetrics(client, filters, days);
      
      // Get quality indicators
      const quality = await getQualityIndicators(client, filters, days);
      
      // Get bottleneck analysis
      const bottlenecks = await getBottleneckAnalysis(client, filters, days);

      return NextResponse.json({
        success: true,
        data: {
          executive_summary: executiveSummary,
          stage_performance: stagePerformance,
          productivity,
          quality,
          bottlenecks
        },
        metadata: {
          generated_at: new Date().toISOString(),
          filters: { source, dateRange },
          business_context: getBusinessContext(dateRange)
        }
      });

    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Enhanced stats error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

function getDaysFromRange(range: string): number {
  switch (range) {
    case '1d': return 1;
    case '7d': return 7;
    case '30d': return 30;
    case '90d': return 90;
    default: return 7;
  }
}

function buildFilters(source: string) {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (source !== 'all') {
    conditions.push(`c.source = $${paramIndex}`);
    params.push(source);
    paramIndex++;
  }

  return {
    whereClause: conditions.length > 0 ? 'AND ' + conditions.join(' AND ') : '',
    params
  };
}

async function getExecutiveSummary(client: any, filters: any, days: number) {
  const summaryQuery = `
    WITH pipeline_summary AS (
      SELECT 
        COUNT(*) as total_leads,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as validated_leads,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as outreach_completed,
        COUNT(CASE WHEN processing_error_count > 0 THEN 1 END) as problematic_leads,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as new_leads_24h,
        COUNT(CASE WHEN email_sending_date >= NOW() - INTERVAL '24 hours' THEN 1 END) as outreach_sent_24h,
        ROUND(AVG(EXTRACT(EPOCH FROM (COALESCE(email_sending_date, NOW()) - created_at)) / 86400), 2) as avg_pipeline_days
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}
    )
    SELECT 
      *,
      ROUND(validated_leads * 100.0 / NULLIF(total_leads, 0), 2) as validation_rate,
      ROUND(outreach_completed * 100.0 / NULLIF(total_leads, 0), 2) as completion_rate,
      ROUND(problematic_leads * 100.0 / NULLIF(total_leads, 0), 2) as error_rate
    FROM pipeline_summary
  `;

  const result = await client.query(summaryQuery, filters.params);
  const summary = result.rows[0];

  return {
    total_leads: parseInt(summary.total_leads || 0),
    validated_leads: parseInt(summary.validated_leads || 0),
    outreach_completed: parseInt(summary.outreach_completed || 0),
    problematic_leads: parseInt(summary.problematic_leads || 0),
    rates: {
      validation_rate: parseFloat(summary.validation_rate || 0),
      completion_rate: parseFloat(summary.completion_rate || 0),
      error_rate: parseFloat(summary.error_rate || 0)
    },
    velocity: {
      new_leads_24h: parseInt(summary.new_leads_24h || 0),
      outreach_sent_24h: parseInt(summary.outreach_sent_24h || 0),
      avg_pipeline_days: parseFloat(summary.avg_pipeline_days || 0)
    },
    health_score: calculateHealthScore(summary),
    status: getOverallStatus(summary)
  };
}

function calculateHealthScore(summary: any): number {
  const completionRate = parseFloat(summary.completion_rate || 0);
  const errorRate = parseFloat(summary.error_rate || 0);
  const pipelineSpeed = parseFloat(summary.avg_pipeline_days || 0);
  
  let score = 0;
  
  // Completion rate (40% weight)
  if (completionRate >= 80) score += 40;
  else if (completionRate >= 60) score += 30;
  else if (completionRate >= 40) score += 20;
  else if (completionRate >= 20) score += 10;
  
  // Error rate (30% weight) - lower is better
  if (errorRate <= 2) score += 30;
  else if (errorRate <= 5) score += 25;
  else if (errorRate <= 10) score += 15;
  else if (errorRate <= 20) score += 5;
  
  // Pipeline speed (30% weight) - lower is better
  if (pipelineSpeed <= 1) score += 30;
  else if (pipelineSpeed <= 3) score += 25;
  else if (pipelineSpeed <= 7) score += 15;
  else if (pipelineSpeed <= 14) score += 5;
  
  return Math.min(100, score);
}

function getOverallStatus(summary: any): 'excellent' | 'good' | 'warning' | 'critical' {
  const completionRate = parseFloat(summary.completion_rate || 0);
  const errorRate = parseFloat(summary.error_rate || 0);
  
  if (completionRate >= 80 && errorRate <= 5) return 'excellent';
  if (completionRate >= 60 && errorRate <= 10) return 'good';
  if (completionRate >= 30 && errorRate <= 20) return 'warning';
  return 'critical';
}

async function getStagePerformance(client: any, filters: any, days: number) {
  const stageQueries = [
    {
      name: 'Email Validation',
      key: 'email_verification',
      eligible_condition: 'email IS NOT NULL AND email != \'\'',
      status_column: 'email_verification_status',
      date_column: 'email_verification_date'
    },
    {
      name: 'Lead Research',
      key: 'osint',
      eligible_condition: 'email_verification_status = \'completed\'',
      status_column: 'osint_status',
      date_column: 'osint_date'
    },
    {
      name: 'Lead Scoring',
      key: 'classification',
      eligible_condition: 'overview_extraction_status = \'completed\'',
      status_column: 'classification_status',
      date_column: 'classification_date'
    },
    {
      name: 'Content Creation',
      key: 'email_generation',
      eligible_condition: 'classification_status = \'completed\'',
      status_column: 'email_generation_status',
      date_column: 'email_generation_date'
    },
    {
      name: 'Outreach Delivery',
      key: 'email_sending',
      eligible_condition: 'email_generation_status = \'completed\'',
      status_column: 'email_sending_status',
      date_column: 'email_sending_date'
    }
  ];

  const stagePerformance = [];

  for (const stage of stageQueries) {
    const stageQuery = `
      SELECT 
        COUNT(CASE WHEN ${stage.eligible_condition} THEN 1 END) as eligible,
        COUNT(CASE WHEN ${stage.status_column} = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN ${stage.status_column} = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN ${stage.status_column} = 'error' THEN 1 END) as errors,
        COUNT(CASE WHEN ${stage.status_column} = 'running' THEN 1 END) as running,
        COUNT(CASE WHEN ${stage.status_column} = 'pending' AND ${stage.eligible_condition} THEN 1 END) as pending,
        COUNT(CASE WHEN ${stage.date_column} >= NOW() - INTERVAL '24 hours' THEN 1 END) as completed_24h,
        ROUND(AVG(CASE WHEN ${stage.status_column} = 'completed' 
          THEN EXTRACT(EPOCH FROM (${stage.date_column} - created_at)) / 3600 END), 2) as avg_processing_hours
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}
    `;

    const result = await client.query(stageQuery, filters.params);
    const metrics = result.rows[0];

    const eligible = parseInt(metrics.eligible || 0);
    const completed = parseInt(metrics.completed || 0);
    const failed = parseInt(metrics.failed || 0);
    const errors = parseInt(metrics.errors || 0);

    stagePerformance.push({
      stage_name: stage.name,
      stage_key: stage.key,
      metrics: {
        eligible,
        completed,
        failed,
        errors,
        running: parseInt(metrics.running || 0),
        pending: parseInt(metrics.pending || 0),
        completed_24h: parseInt(metrics.completed_24h || 0),
        avg_processing_hours: parseFloat(metrics.avg_processing_hours || 0)
      },
      rates: {
        success_rate: eligible > 0 ? parseFloat(((completed / eligible) * 100).toFixed(2)) : 0,
        failure_rate: eligible > 0 ? parseFloat(((failed / eligible) * 100).toFixed(2)) : 0,
        error_rate: eligible > 0 ? parseFloat(((errors / eligible) * 100).toFixed(2)) : 0
      },
      status: getStageStatus(completed, failed, errors, eligible),
      recommendations: getStageRecommendations(stage.key, completed, failed, errors, eligible)
    });
  }

  return stagePerformance;
}

function getStageStatus(completed: number, failed: number, errors: number, eligible: number): 'excellent' | 'good' | 'warning' | 'critical' {
  if (eligible === 0) return 'good';
  
  const successRate = (completed / eligible) * 100;
  const errorRate = ((failed + errors) / eligible) * 100;
  
  if (successRate >= 95 && errorRate <= 2) return 'excellent';
  if (successRate >= 85 && errorRate <= 5) return 'good';
  if (successRate >= 70 && errorRate <= 10) return 'warning';
  return 'critical';
}

function getStageRecommendations(stageKey: string, completed: number, failed: number, errors: number, eligible: number): string[] {
  const recommendations: string[] = [];
  
  if (eligible === 0) return ['No eligible leads for this stage'];
  
  const successRate = (completed / eligible) * 100;
  const errorRate = ((failed + errors) / eligible) * 100;
  
  if (successRate < 80) {
    recommendations.push(`Improve success rate (currently ${successRate.toFixed(1)}%)`);
  }
  
  if (errorRate > 5) {
    recommendations.push(`Reduce error rate (currently ${errorRate.toFixed(1)}%)`);
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Performance is within acceptable ranges');
  }
  
  return recommendations;
}

async function getProductivityMetrics(client: any, filters: any, days: number) {
  const productivityQuery = `
    WITH hourly_productivity AS (
      SELECT
        EXTRACT(HOUR FROM email_verification_date) as hour,
        COUNT(*) as validations_completed
      FROM contacts c
      WHERE email_verification_date >= NOW() - INTERVAL '${days} days'
      AND email_verification_status = 'completed'
      ${filters.whereClause}
      GROUP BY EXTRACT(HOUR FROM email_verification_date)
    ),
    daily_productivity AS (
      SELECT
        DATE(email_sending_date) as date,
        COUNT(*) as outreach_sent
      FROM contacts c
      WHERE email_sending_date >= CURRENT_DATE - INTERVAL '${days} days'
      AND email_sending_status = 'completed'
      ${filters.whereClause}
      GROUP BY DATE(email_sending_date)
      ORDER BY DATE(email_sending_date)
    )
    SELECT
      (SELECT COUNT(*) FROM hourly_productivity) as active_hours,
      (SELECT ROUND(AVG(validations_completed), 2) FROM hourly_productivity) as avg_validations_per_hour,
      (SELECT MAX(validations_completed) FROM hourly_productivity) as peak_validations_per_hour,
      (SELECT COUNT(*) FROM daily_productivity) as active_days,
      (SELECT ROUND(AVG(outreach_sent), 2) FROM daily_productivity) as avg_outreach_per_day,
      (SELECT MAX(outreach_sent) FROM daily_productivity) as peak_outreach_per_day
  `;

  const result = await client.query(productivityQuery, [...filters.params, ...filters.params]);
  const metrics = result.rows[0];

  return {
    hourly_metrics: {
      active_hours: parseInt(metrics.active_hours || 0),
      avg_validations_per_hour: parseFloat(metrics.avg_validations_per_hour || 0),
      peak_validations_per_hour: parseInt(metrics.peak_validations_per_hour || 0)
    },
    daily_metrics: {
      active_days: parseInt(metrics.active_days || 0),
      avg_outreach_per_day: parseFloat(metrics.avg_outreach_per_day || 0),
      peak_outreach_per_day: parseInt(metrics.peak_outreach_per_day || 0)
    },
    efficiency_score: calculateEfficiencyScore(metrics)
  };
}

function calculateEfficiencyScore(metrics: any): number {
  const avgValidationsPerHour = parseFloat(metrics.avg_validations_per_hour || 0);
  const avgOutreachPerDay = parseFloat(metrics.avg_outreach_per_day || 0);

  let score = 0;

  // Validation efficiency (50% weight)
  if (avgValidationsPerHour >= 50) score += 50;
  else if (avgValidationsPerHour >= 30) score += 40;
  else if (avgValidationsPerHour >= 20) score += 30;
  else if (avgValidationsPerHour >= 10) score += 20;

  // Outreach efficiency (50% weight)
  if (avgOutreachPerDay >= 100) score += 50;
  else if (avgOutreachPerDay >= 50) score += 40;
  else if (avgOutreachPerDay >= 25) score += 30;
  else if (avgOutreachPerDay >= 10) score += 20;

  return Math.min(100, score);
}

async function getQualityIndicators(client: any, filters: any, days: number) {
  const qualityQuery = `
    WITH quality_metrics AS (
      SELECT
        COUNT(*) as total_processed,
        COUNT(CASE WHEN processing_error_count = 0 THEN 1 END) as error_free_leads,
        COUNT(CASE WHEN processing_error_count > 0 AND processing_error_count <= 2 THEN 1 END) as low_error_leads,
        COUNT(CASE WHEN processing_error_count > 2 THEN 1 END) as high_error_leads,
        ROUND(AVG(processing_error_count), 2) as avg_error_count,
        COUNT(CASE WHEN email_verification_status = 'completed' AND
                        osint_status = 'completed' AND
                        classification_status = 'completed' AND
                        email_generation_status = 'completed' AND
                        email_sending_status = 'completed' THEN 1 END) as fully_processed
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}
    )
    SELECT
      *,
      ROUND(error_free_leads * 100.0 / NULLIF(total_processed, 0), 2) as error_free_rate,
      ROUND(fully_processed * 100.0 / NULLIF(total_processed, 0), 2) as full_completion_rate
    FROM quality_metrics
  `;

  const result = await client.query(qualityQuery, filters.params);
  const quality = result.rows[0];

  return {
    error_distribution: {
      error_free: parseInt(quality.error_free_leads || 0),
      low_errors: parseInt(quality.low_error_leads || 0),
      high_errors: parseInt(quality.high_error_leads || 0)
    },
    quality_rates: {
      error_free_rate: parseFloat(quality.error_free_rate || 0),
      full_completion_rate: parseFloat(quality.full_completion_rate || 0),
      avg_error_count: parseFloat(quality.avg_error_count || 0)
    },
    quality_score: calculateQualityScore(quality),
    quality_status: getQualityStatus(quality)
  };
}

function calculateQualityScore(quality: any): number {
  const errorFreeRate = parseFloat(quality.error_free_rate || 0);
  const completionRate = parseFloat(quality.full_completion_rate || 0);

  // Quality score based on error-free rate (60%) and completion rate (40%)
  return Math.round((errorFreeRate * 0.6) + (completionRate * 0.4));
}

function getQualityStatus(quality: any): 'excellent' | 'good' | 'warning' | 'critical' {
  const errorFreeRate = parseFloat(quality.error_free_rate || 0);
  const avgErrorCount = parseFloat(quality.avg_error_count || 0);

  if (errorFreeRate >= 95 && avgErrorCount <= 0.1) return 'excellent';
  if (errorFreeRate >= 85 && avgErrorCount <= 0.3) return 'good';
  if (errorFreeRate >= 70 && avgErrorCount <= 0.5) return 'warning';
  return 'critical';
}

async function getBottleneckAnalysis(client: any, filters: any, days: number) {
  const bottleneckQuery = `
    WITH stage_backlogs AS (
      SELECT
        'Email Validation' as stage,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' AND email_verification_status = 'pending' THEN 1 END) as backlog,
        COUNT(CASE WHEN email_verification_status = 'running' THEN 1 END) as in_progress,
        ROUND(AVG(CASE WHEN email_verification_status = 'pending'
          THEN EXTRACT(EPOCH FROM (NOW() - created_at)) / 3600 END), 2) as avg_wait_hours
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}

      UNION ALL

      SELECT
        'Lead Research' as stage,
        COUNT(CASE WHEN email_verification_status = 'completed' AND osint_status = 'pending' THEN 1 END) as backlog,
        COUNT(CASE WHEN osint_status = 'running' THEN 1 END) as in_progress,
        ROUND(AVG(CASE WHEN osint_status = 'pending' AND email_verification_status = 'completed'
          THEN EXTRACT(EPOCH FROM (NOW() - email_verification_date)) / 3600 END), 2) as avg_wait_hours
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}

      UNION ALL

      SELECT
        'Content Creation' as stage,
        COUNT(CASE WHEN classification_status = 'completed' AND email_generation_status = 'pending' THEN 1 END) as backlog,
        COUNT(CASE WHEN email_generation_status = 'running' THEN 1 END) as in_progress,
        ROUND(AVG(CASE WHEN email_generation_status = 'pending' AND classification_status = 'completed'
          THEN EXTRACT(EPOCH FROM (NOW() - classification_date)) / 3600 END), 2) as avg_wait_hours
      FROM contacts c
      WHERE c.created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}
    )
    SELECT
      stage,
      backlog,
      in_progress,
      avg_wait_hours,
      (backlog + in_progress) as total_workload
    FROM stage_backlogs
    WHERE backlog > 0 OR in_progress > 0
    ORDER BY total_workload DESC
  `;

  const result = await client.query(bottleneckQuery, [...filters.params, ...filters.params, ...filters.params]);

  return result.rows.map((row: any) => ({
    stage: row.stage,
    backlog: parseInt(row.backlog || 0),
    in_progress: parseInt(row.in_progress || 0),
    total_workload: parseInt(row.total_workload || 0),
    avg_wait_hours: parseFloat(row.avg_wait_hours || 0),
    severity: getBottleneckSeverity(row),
    recommendations: getBottleneckRecommendations(row)
  }));
}

function getBottleneckSeverity(row: any): 'critical' | 'high' | 'medium' | 'low' {
  const totalWorkload = parseInt(row.total_workload || 0);
  const avgWaitHours = parseFloat(row.avg_wait_hours || 0);

  if (totalWorkload > 500 || avgWaitHours > 48) return 'critical';
  if (totalWorkload > 200 || avgWaitHours > 24) return 'high';
  if (totalWorkload > 50 || avgWaitHours > 12) return 'medium';
  return 'low';
}

function getBottleneckRecommendations(row: any): string[] {
  const recommendations: string[] = [];
  const backlog = parseInt(row.backlog || 0);
  const avgWaitHours = parseFloat(row.avg_wait_hours || 0);

  if (backlog > 100) {
    recommendations.push(`High backlog detected (${backlog} items) - consider increasing processing capacity`);
  }

  if (avgWaitHours > 24) {
    recommendations.push(`Long wait times (${avgWaitHours.toFixed(1)} hours) - prioritize this stage`);
  }

  if (backlog > 50) {
    recommendations.push('Consider parallel processing or additional resources');
  }

  if (recommendations.length === 0) {
    recommendations.push('Stage is operating within normal parameters');
  }

  return recommendations;
}

function getBusinessContext(dateRange: string): string {
  const contexts = {
    '1d': 'Real-time operational dashboard',
    '7d': 'Weekly performance review',
    '30d': 'Monthly business metrics',
    '90d': 'Quarterly strategic analysis'
  };
  return contexts[dateRange as keyof typeof contexts] || 'Performance analysis';
}
