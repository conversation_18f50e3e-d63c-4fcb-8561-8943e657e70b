"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, forwardRef, useImperativeH<PERSON>le } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Define the ref type
export interface MappingTablesViewRef {
  hasUnsavedChanges: () => boolean;
}

const MappingTablesView = forwardRef<MappingTablesViewRef>((props, ref) => {
  const [mappingTables, setMappingTables] = useState<string[]>([])
  const [selectedTable, setSelectedTable] = useState<string>("")
  const [pendingTableSelection, setPendingTableSelection] = useState<string | null>(null)
  const [tableData, setTableData] = useState<any[]>([])
  const [columns, setColumns] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [originalData, setOriginalData] = useState<any[]>([])
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false)

  const isDataChanged = useCallback(() => {
    return JSON.stringify(tableData) !== JSON.stringify(originalData)
  }, [tableData, originalData])

  // Expose methods to parent component through the ref
  useImperativeHandle(ref, () => ({
    hasUnsavedChanges: isDataChanged
  }));

  // Fetch list of mapping tables
  useEffect(() => {
    const fetchMappingTables = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/mapping-tables')
        const data = await response.json()
        setMappingTables(data.tables)
        if (data.tables.length > 0) {
          setSelectedTable(data.tables[0])
        }
      } catch (error) {
        console.error('Error fetching mapping tables:', error)
        toast.error("Failed to load mapping tables")
      } finally {
        setLoading(false)
      }
    }

    fetchMappingTables()
  }, [])

  // Fetch data for selected table
  useEffect(() => {
    if (!selectedTable) return

    const fetchTableData = async () => {
      setLoading(true)
      try {
        const response = await fetch(`/api/mapping-tables/${selectedTable}`)
        const data = await response.json()
        if (data.rows && data.rows.length > 0) {
          setTableData(data.rows)
          setOriginalData(JSON.parse(JSON.stringify(data.rows))) // Deep copy
          setColumns(Object.keys(data.rows[0]))
        } else {
          setTableData([])
          setOriginalData([])
          setColumns([])
        }
      } catch (error) {
        console.error(`Error fetching data for ${selectedTable}:`, error)
        toast.error(`Failed to load data for ${selectedTable}`)
      } finally {
        setLoading(false)
      }
    }

    fetchTableData()
  }, [selectedTable])

  const handleRadioChange = (value: string) => {
    // If there are unsaved changes, show confirmation dialog
    if (isDataChanged()) {
      setPendingTableSelection(value)
      setShowUnsavedChangesDialog(true)
    } else {
      // No unsaved changes, proceed with table change
      setSelectedTable(value)
    }
  }

  const handleConfirmTableChange = () => {
    if (pendingTableSelection) {
      setSelectedTable(pendingTableSelection)
      setPendingTableSelection(null)
    }
    setShowUnsavedChangesDialog(false)
  }

  const handleCancelTableChange = () => {
    setPendingTableSelection(null)
    setShowUnsavedChangesDialog(false)
  }

  const handleCellChange = (rowIndex: number, columnName: string, value: string) => {
    const updatedData = [...tableData]
    updatedData[rowIndex][columnName] = value
    setTableData(updatedData)
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const response = await fetch(`/api/mapping-tables/${selectedTable}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: tableData }),
      })
      
      if (response.ok) {
        setOriginalData(JSON.parse(JSON.stringify(tableData))) // Update original data
        toast.success("Table data saved successfully")
      } else {
        throw new Error('Failed to save data')
      }
    } catch (error) {
      console.error('Error saving table data:', error)
      toast.error("Failed to save table data")
    } finally {
      setSaving(false)
    }
  }

  // Add an effect to warn about unsaved changes when navigating away
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (isDataChanged()) {
        e.preventDefault()
        e.returnValue = ''
        return ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [isDataChanged])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Mapping Tables Editor</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="md:col-span-1 space-y-4">
              <h3 className="text-sm font-medium">Select a mapping table:</h3>
              {loading && mappingTables.length === 0 ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                </div>
              ) : (
                <RadioGroup value={selectedTable} onValueChange={handleRadioChange}>
                  {mappingTables.map((table) => (
                    <div key={table} className="flex items-center space-x-2">
                      <RadioGroupItem value={table} id={table} />
                      <Label htmlFor={table}>{table.replace(/_/g, ' ')}</Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>
            
            <div className="md:col-span-3">
              {loading ? (
                <div className="flex items-center justify-center h-48">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : selectedTable ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">{selectedTable.replace(/_/g, ' ')}</h3>
                    <Button 
                      onClick={handleSave} 
                      disabled={saving || !isDataChanged()}
                    >
                      {saving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : "Save Changes"}
                    </Button>
                  </div>
                  
                  <div className="border rounded-md overflow-auto max-h-[600px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {columns.map((column) => (
                            <TableHead key={column}>
                              {column.replace(/_/g, ' ')}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {tableData.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {columns.map((column) => (
                              <TableCell key={`${rowIndex}-${column}`}>
                                <Input
                                  value={row[column] || ''}
                                  onChange={(e) => handleCellChange(rowIndex, column, e.target.value)}
                                />
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-48 text-gray-500">
                  Select a mapping table to edit
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showUnsavedChangesDialog} onOpenChange={setShowUnsavedChangesDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes to the current table. Would you like to discard these changes?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelTableChange}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmTableChange}>Discard Changes</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
})

MappingTablesView.displayName = 'MappingTablesView'

export default MappingTablesView 