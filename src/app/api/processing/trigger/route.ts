import { NextRequest, NextResponse } from 'next/server'
import { processorScheduler } from '../../../../lib/scheduler/ProcessorScheduler'
import { ProcessingStage } from '../../../../types/processing'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, stage, options = {} } = body

    switch (action) {
      case 'execute_manual': {
        const { limit, singleId, filters } = options
        const batchSize = 500
        
        if (!stage) {
          return NextResponse.json(
            { success: false, error: 'Stage is required for manual execution' },
            { status: 400 }
          )
        }

        const result = await processorScheduler.executeManualJob(stage as ProcessingStage, {
          limit,
          singleId,
          filters,
          batchSize
        })

        if (!result) {
          return NextResponse.json(
            { success: false, error: 'Failed to execute job' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            stage,
            result,
            appliedFilters: filters,
            batchSize
          }
        })
      }

      case 'toggle_scheduled_job': {
        const { jobId, enabled } = options
        
        if (!jobId || typeof enabled !== 'boolean') {
          return NextResponse.json(
            { success: false, error: 'jobId and enabled status are required' },
            { status: 400 }
          )
        }

        processorScheduler.toggleJob(jobId, enabled)

        return NextResponse.json({
          success: true,
          data: {
            jobId,
            enabled
          }
        })
      }

      case 'start_scheduler': {
        processorScheduler.start()
        
        return NextResponse.json({
          success: true,
          data: { message: 'Scheduler started' }
        })
      }

      case 'stop_scheduler': {
        processorScheduler.stop()
        
        return NextResponse.json({
          success: true,
          data: { message: 'Scheduler stopped' }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Processing trigger error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'jobs': {
        const jobs = processorScheduler.getJobs()
        
        return NextResponse.json({
          success: true,
          data: { jobs }
        })
      }

      case 'running_jobs': {
        const runningJobs = processorScheduler.getRunningJobs()
        
        return NextResponse.json({
          success: true,
          data: { runningJobs }
        })
      }

      case 'job_status': {
        const jobId = searchParams.get('jobId')
        
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'jobId is required' },
            { status: 400 }
          )
        }

        const job = processorScheduler.getJobStatus(jobId)
        
        return NextResponse.json({
          success: true,
          data: { job }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Processing trigger GET error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 