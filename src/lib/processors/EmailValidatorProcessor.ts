import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import axios from 'axios'

interface ContactData extends EntityData {
  contact_id: number
  first_name: string
  last_name: string
  email: string
  email_verification_status?: string
}

// BulkEmailChecker API configuration
const BULK_EMAIL_CHECKER_API_URL = "https://api.bulkemailchecker.com/real-time/"
const BULK_EMAIL_CHECKER_API_KEY = process.env.BULK_EMAIL_CHECKER_API_KEY

export class EmailValidatorProcessor extends BaseProcessor {
  // Store email status results to pass to updateEntityStatus
  private emailStatusResults: Map<number, string> = new Map()

  constructor(options: ProcessorOptions = {}) {
    super('EmailValidator', options)
  }

  async getUnprocessedEntities(): Promise<EntityData[]> {
    const contacts = await this.getContactsForEmailValidation(
      this.options.limit,
      this.options.singleId
    )
    
    return contacts.map(contact => ({
      id: contact.contact_id as number,
      ...contact
    }))
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const contact = entity as ContactData
    
    try {
      this.log('info', `Validating email for contact ${contact.contact_id}: ${contact.email}`)
      
      // Set status to running
      await this.updateContactEmailVerificationStatus(contact.contact_id, 'running')
      
      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(contact.email)) {
        // Store email status for updateEntityStatus
        this.emailStatusResults.set(contact.contact_id, 'Invalid')
        return { success: false, error: 'Invalid email format' }
      }

      // Call the email validation service
      const emailStatus = await this.validateEmailWithService(contact.email)
      
      // Store email status for updateEntityStatus
      this.emailStatusResults.set(contact.contact_id, emailStatus)
      
      this.log('info', `Email validation result for ${contact.email}: ${emailStatus}`)
      return { success: emailStatus === 'Verified' }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // Store email status for updateEntityStatus
      this.emailStatusResults.set(contact.contact_id, 'Unknown')
      
      this.log('error', `Error validating email for contact ${contact.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // Get the stored email status result
    const emailStatus = this.emailStatusResults.get(entityId) || 'Unknown'
    
    if (success) {
      // Email validation passed
      await this.updateContactEmailVerificationStatus(entityId, 'completed')
    } else {
      // Email validation failed
      await this.updateContactEmailVerificationStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
    
    // ALWAYS update email_status regardless of success/failure
    await this.updateContactEmailStatus(entityId, emailStatus)
    
    // Clean up stored result
    this.emailStatusResults.delete(entityId)
  }

  /**
   * Validate email using BulkEmailChecker API
   * @param email Email address to validate
   * @returns Email status string ('Verified', 'Invalid', 'Unknown', 'Failed')
   */
  private async validateEmailWithService(email: string): Promise<string> {
    try {
      this.log('info', `Validating email with BulkEmailChecker API: ${email}`)
      
      // URL encode the email
      const encodedEmail = encodeURIComponent(email)
      
      // Create the API URL
      const url = `${BULK_EMAIL_CHECKER_API_URL}?key=${BULK_EMAIL_CHECKER_API_KEY}&email=${encodedEmail}`
      
      // Make the API request
      const response = await axios.get(url, { timeout: 15000 })
      
      // Check if the request was successful
      if (response.status !== 200) {
        this.log('error', `API request failed with status code ${response.status}: ${response.statusText}`)
        return 'Unknown'
      }
      
      // Parse the response
      const result = response.data
      
      // Map API response to our values
      const statusValue = result.status || 'unknown'
      
      if (statusValue === 'passed') {
        this.log('info', `Verified: ${email}`)
        return 'Verified'
      } else if (statusValue === 'failed') {
        this.log('info', `Invalid: ${email}`)
        return 'Invalid'
      } else {  // status === 'unknown' or any other status
        this.log('info', `Unknown: ${email} (API status: ${statusValue})`)
        return 'Unknown'
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error validating email with API: ${errorMessage}`)
      return 'Unknown'
    }
  }
} 