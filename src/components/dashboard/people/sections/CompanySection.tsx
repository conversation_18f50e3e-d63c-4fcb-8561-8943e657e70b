"use client"

import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Link, ExternalLink, Building2, BarChart } from 'lucide-react';
import { Contact } from '../types';

interface CompanySectionProps {
  contact: Contact;
}

const CompanySection: React.FC<CompanySectionProps> = ({ contact }) => {
  if (!contact.company_name && !contact.company_website && !contact.industry) return null;
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Company</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 text-sm">
        {contact.company_name && (
          <div className="flex items-center">
            <Building2 className="h-4 w-4 mr-3 text-gray-400" />
            <span className="font-medium">{contact.company_name}</span>
          </div>
        )}
        {contact.industry && (
          <div className="flex items-center">
            <BarChart className="h-4 w-4 mr-3 text-gray-400" />
            <span>Industry: {contact.industry}</span>
          </div>
        )}
        {contact.company_website && (
          <div className="flex items-center">
            <Link className="h-4 w-4 mr-3 text-gray-400" />
            <a href={contact.company_website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline flex items-center">
              {contact.company_website}
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CompanySection; 