// Base processor and interfaces
export { BaseProcessor } from './BaseProcessor'
export type { 
  ProcessorOptions, 
  ProcessorResult,
  EntityData 
} from './BaseProcessor'

// Specific processors
export { EmailValidatorProcessor } from './EmailValidatorProcessor'
export { ContactClassificationProcessor } from './ContactClassificationProcessor'
export { ContactOverviewProcessor } from './ContactOverviewProcessor'
export { CompanyOverviewProcessor } from './CompanyOverviewProcessor'
export { ContactSearchProcessor } from './ContactSearchProcessor'
export { EmailGenerationProcessor } from './EmailGenerationProcessor'
export { CompanyWebCrawlerProcessor } from './CompanyWebCrawlerProcessor'

// Scheduler
export { ProcessorScheduler, processorScheduler } from '../scheduler/ProcessorScheduler'

// Processing types
export type {
  ContactProcessingState,
  CompanyProcessingState,
  ProcessingStage,
  ProcessingJob,
  ProcessingStats
} from '../../types/processing' 