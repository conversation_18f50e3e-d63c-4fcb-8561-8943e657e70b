'use client'

import { useEffect, useRef } from 'react'
import mermaid from 'mermaid'

interface MermaidProps {
  chart: string
}

const Mermaid = ({ chart }: MermaidProps) => {
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const renderMermaid = async () => {
      if (elementRef.current) {
        try {
          const { svg } = await mermaid.render('mermaid-svg', chart)
          elementRef.current.innerHTML = svg
        } catch (error) {
          console.error('Mermaid render error:', error)
          elementRef.current.innerHTML = `<div class="text-red-500">Error rendering diagram</div>`
        }
      }
    }

    renderMermaid()
  }, [chart])

  return <div ref={elementRef} className="overflow-auto" />
}

export default Mermaid 