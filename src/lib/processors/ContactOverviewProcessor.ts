import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import { ContactProcessingState } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { CONTACT_OVERVIEW_SYSTEM_PROMPT, CONTACT_OVERVIEW_USER_TEMPLATE_FUNCTION } from '../prompts'

interface ContactData extends EntityData {
  contact_id: number
  full_name: string
  first_name?: string
  last_name?: string
  title?: string
  email: string
  linkedin_url?: string
  company_id?: number
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  processing_state: ContactProcessingState
  extracted?: boolean
  extra_attrs?: any
  osint_research?: string
}

interface ExtractedData {
  executive_summary: string
  career_timeline: string[]
  notable_activities: Array<{
    title: string
    snippet: string
    url?: string
    date?: string
  }>
  personal_tidbits: string[]
  conversation_hooks: string[]
  outreach_draft: string
  sources: string[]
}

export class ContactOverviewProcessor extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    super('ContactOverview', options)
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Create LLM provider with Perplexity as primary and OpenAI as fallback
    this.llmProvider = LLMFactory.createWithFallback(
      'perplexity',
      'openai',
      loggerAdapter,
      process.env.PERPLEXITY_API_KEY,
      process.env.OPENAI_API_KEY,
      { 
        temperature: 0.2,
        maxTokens: 8000,
        model: 'r1-1776'  // Using latest Perplexity model
      }
    );
  }

  async getUnprocessedEntities(): Promise<EntityData[]> {
    const contacts = await this.getContactsForOverviewExtraction(
      this.options.limit,
      this.options.singleId
    )
    
    return contacts.map(contact => ({
      id: contact.contact_id as number,
      ...contact
    }))
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const contact = entity as ContactData
    
    try {
      this.log('info', `Starting profile extraction for contact ${contact.contact_id}: ${contact.full_name}`)
      // Extract OSINT research
      const osintResearch = await this.getContactOSINTResearch(contact.contact_id)
      
      // Perform profile extraction
      const extractionResult = await this.performProfileExtraction(contact, osintResearch)
      
      if (extractionResult.success) {
        this.log('info', `Profile extraction completed for ${contact.full_name}`)
        return { success: true }
      } else {
        return { success: false, error: extractionResult.error }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in profile extraction for contact ${contact.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // Profile extraction completed successfully
      await this.updateContactOverviewExtractionStatus(entityId, 'completed')
    } else {
      // Profile extraction failed
      await this.updateContactOverviewExtractionStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Perform comprehensive profile extraction using our LLM provider
   */
  private async performProfileExtraction(
    contact: ContactData, 
    osintResearch: Record<string, unknown> | null
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Starting profile extraction for contact ${contact.contact_id}`)
      
      // Build the extraction prompt with OSINT data
      const prompt = await this.buildExtractionPrompt(contact, osintResearch)
      
      // Call LLM provider
      this.log('debug', `Calling LLM API for profile extraction for contact ${contact.contact_id}`)
      const apiResponse = await this.callLLMAPI(prompt)
      
      if (!apiResponse.success) {
        return { success: false, error: apiResponse.error }
      }
      
      // Parse the response to extract profile data
      const extractedData = this.parseExtractionResponse(apiResponse.content!)
      if (!extractedData) {
        return { success: false, error: 'Failed to parse extraction response' }
      }
      
      // Save the extracted data to database
      const saveResult = await this.saveExtractedData(contact.contact_id, extractedData, apiResponse.tokens!)
      
      if (saveResult.success) {
        this.log('info', `Profile extraction completed and saved for contact ${contact.contact_id}`)
        return { success: true }
      } else {
        return { success: false, error: saveResult.error }
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in profile extraction: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Build comprehensive extraction prompt with OSINT research data
   */
  private async buildExtractionPrompt(
    contact: ContactData, 
    osintResearch: Record<string, unknown> | null
  ): Promise<{ system: string; user: string }> {
    const systemPrompt = CONTACT_OVERVIEW_SYSTEM_PROMPT
    
    const userPrompt = CONTACT_OVERVIEW_USER_TEMPLATE_FUNCTION(contact, osintResearch)

    return { system: systemPrompt, user: userPrompt }
  }

  /**
   * Call LLM API for profile extraction
   */
  private async callLLMAPI(
    prompt: { system: string; user: string }
  ): Promise<{ success: boolean; content?: string; tokens?: number; error?: string }> {
    try {
      const messages: LLMMessage[] = [
        { role: 'system', content: prompt.system },
        { role: 'user', content: prompt.user }
      ];

      // Use the LLM provider with fallback support
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.2,
        maxTokens: 8000
      });

      if (!response.content) {
        return { success: false, error: `No content in API response from ${response.provider}` };
      }

      const tokens = response.usage?.totalTokens || 0;
      this.log('info', `API call successful using ${response.provider} (${response.model}). Tokens used: ${tokens}`);
      
      return { 
        success: true, 
        content: response.content, 
        tokens: tokens 
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log('error', `LLM API call failed: ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Parse extraction response matching Python script logic
   */
  private parseExtractionResponse(content: string): ExtractedData | null {
    try {
      this.log('debug', `Parsing LLM response content (length: ${content.length})`)
      
      // First, try to extract JSON from markdown code blocks
      let jsonContent = content
      
      // Remove thinking tags if present
      jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '')
      
      // Try to extract from markdown code blocks first
      const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownJsonMatch) {
        jsonContent = markdownJsonMatch[1].trim()
        this.log('debug', `Extracted JSON from markdown code block`)
      } else {
        // Fallback: try to find JSON object in the content
        const jsonMatch = jsonContent.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          jsonContent = jsonMatch[0]
          this.log('debug', `Extracted JSON using fallback regex`)
        } else {
          this.log('warn', `No JSON found in response content`)
          return null
        }
      }
      
      // Clean up the JSON content
      jsonContent = jsonContent.trim()
      
      this.log('debug', `Attempting to parse JSON: ${jsonContent.substring(0, 200)}...`)
      
      const parsed = JSON.parse(jsonContent)
      
      // Validate required fields
      if (parsed.executive_summary || parsed.career_timeline || parsed.conversation_hooks) {
        this.log('info', `Successfully parsed extraction response with ${Object.keys(parsed).length} fields`)
        return {
          executive_summary: parsed.executive_summary || '',
          career_timeline: Array.isArray(parsed.career_timeline) ? parsed.career_timeline : [],
          notable_activities: Array.isArray(parsed.notable_activities) ? parsed.notable_activities : [],
          personal_tidbits: Array.isArray(parsed.personal_tidbits) ? parsed.personal_tidbits : [],
          conversation_hooks: Array.isArray(parsed.conversation_hooks) ? parsed.conversation_hooks : [],
          outreach_draft: parsed.outreach_draft || '',
          sources: Array.isArray(parsed.sources) ? parsed.sources : []
        }
      } else {
        this.log('warn', `Parsed JSON but missing required fields (executive_summary, career_timeline, conversation_hooks)`)
        return null
      }

    } catch (error) {
      this.log('error', `Failed to parse JSON response: ${error}`)
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 500 ? content.substring(0, 500) + '...' : content
      this.log('debug', `Response content preview: ${contentPreview}`)
      
      return null
    }
  }

  /**
   * Save extracted data to contact_extracted_data table (matching Python script)
   */
  private async saveExtractedData(
    contactId: number, 
    extractedData: ExtractedData,
    tokens: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Upsert to contact_extracted_data table exactly like Python script
      const upsertSql = `
        INSERT INTO contact_extracted_data (
          contact_id, executive_summary, career_timeline, notable_activities,
          personal_tidbits, conversation_hooks, outreach_draft, sources, created_at, updated_at
        )
        VALUES ($1, $2, $3::jsonb, $4::jsonb, $5::jsonb, $6::jsonb, $7::jsonb, $8::jsonb, NOW(), NOW())
        ON CONFLICT (contact_id) DO UPDATE SET
          executive_summary = EXCLUDED.executive_summary,
          career_timeline = EXCLUDED.career_timeline,
          notable_activities = EXCLUDED.notable_activities,
          personal_tidbits = EXCLUDED.personal_tidbits,
          conversation_hooks = EXCLUDED.conversation_hooks,
          outreach_draft = EXCLUDED.outreach_draft,
          sources = EXCLUDED.sources,
          updated_at = NOW()
      `

      // Mark contact as extracted (matching Python script)
      const markExtractedSql = `UPDATE contacts SET extracted = TRUE WHERE contact_id = $1`

      await this.query(upsertSql, [
        contactId,
        extractedData.executive_summary,
        JSON.stringify(extractedData.career_timeline),
        JSON.stringify(extractedData.notable_activities),
        JSON.stringify(extractedData.personal_tidbits),
        JSON.stringify(extractedData.conversation_hooks),
        JSON.stringify(extractedData.outreach_draft),
        JSON.stringify(extractedData.sources)
      ])

      await this.query(markExtractedSql, [contactId])

      this.log('info', `✓ Contact ${contactId} profile data saved successfully`)
      return { success: true }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Failed to save extracted data: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get extraction statistics
   */
  async getExtractionStats(): Promise<{
    total: number
    extracted: number
    pending: number
    searched: number
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN extracted = true THEN 1 END) as extracted,
        COUNT(CASE WHEN extracted IS NULL OR extracted = false THEN 1 END) as pending,
        COUNT(CASE WHEN searched = true THEN 1 END) as searched
      FROM contacts
      WHERE searched = true
    `
    
    const result = await this.query(sql)
    const data = result[0] || {}
    
    return {
      total: parseInt(data.total as string) || 0,
      extracted: parseInt(data.extracted as string) || 0,
      pending: parseInt(data.pending as string) || 0,
      searched: parseInt(data.searched as string) || 0
    }
  }
} 