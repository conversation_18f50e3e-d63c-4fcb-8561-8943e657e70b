import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { DollarSign, Briefcase, TrendingUp } from 'lucide-react';
import { useProjectionsData } from '@/hooks/useProjectionsData';

const KPICard = ({ title, value, icon: Icon, trend }) => (
  <Card className="flex-1">
    <CardContent className="pt-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <h3 className="text-2xl font-bold mt-2">{value}</h3>
          {trend && (
            <p className={`text-sm mt-1 ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {trend >= 0 ? '↑' : '↓'} {Math.abs(trend)}%
            </p>
          )}
        </div>
        <Icon className="h-8 w-8 text-gray-400" />
      </div>
    </CardContent>
  </Card>
);

const FunnelMetricsCard = ({ metrics }) => (
  <Card>
    <CardHeader>
      <CardTitle>Sales Funnel Metrics</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {metrics.map((stage) => (
          <div key={stage.stage} className="border-b pb-4">
            <h4 className="font-medium">{stage.stage}</h4>
            <div className="grid grid-cols-3 gap-4 mt-2">
              <div>
                <p className="text-sm text-gray-500">Starting</p>
                <p className="font-medium">{Math.round(stage.starting)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Converted</p>
                <p className="font-medium">{Math.round(stage.converted)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Time (Days)</p>
                <p className="font-medium">{stage.timeInDays}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

export const Overview = () => {
  const { metrics } = useProjectionsData();
  const finalStage = metrics[metrics.length - 1];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <KPICard 
          title="Monthly Revenue" 
          value={`$${Math.round(finalStage?.revenue || 0).toLocaleString()}`}
          icon={DollarSign}
          trend={15}
        />
        <KPICard 
          title="Deals Closed" 
          value={Math.round(finalStage?.converted || 0)}
          icon={Briefcase}
          trend={10}
        />
        <KPICard 
          title="Total Pipeline Time" 
          value={`${Math.round(metrics.reduce((acc, m) => acc + m.timeInDays, 0))} days`}
          icon={TrendingUp}
          trend={-30}
        />
      </div>

      <FunnelMetricsCard metrics={metrics} />
    </div>
  );
}; 