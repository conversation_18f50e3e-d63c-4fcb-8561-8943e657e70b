import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

// Helper function to add delay between requests
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * POST: Bulk delete multiple leads from a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    const body = await req.json();
    const { leadIds } = body;
    
    if (!leadIds || !Array.isArray(leadIds) || leadIds.length === 0) {
      return NextResponse.json(
        { error: 'Lead IDs array is required' },
        { status: 400 }
      );
    }
    
    console.log(`Bulk deleting ${leadIds.length} leads from campaign ${campaignId}`);
    
    // First, get all campaign leads to map campaign_lead_map_id to actual lead.id
    let allLeads: any[] = [];
    let offset = 0;
    const limit = 100; // Smartlead API maximum limit
    let hasMore = true;
    
    // Fetch all leads with pagination
    while (hasMore) {
      const leadsResponse = await fetch(`${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}&limit=${limit}&offset=${offset}`);
      
      if (!leadsResponse.ok) {
        const errorText = await leadsResponse.text();
        console.error(`Error fetching leads for campaign ${campaignId}: ${errorText}`);
        return NextResponse.json(
          { error: `Failed to fetch leads: ${leadsResponse.status}` },
          { status: leadsResponse.status }
        );
      }

      const leadsData = await leadsResponse.json();
      
      if (leadsData.data && leadsData.data.length > 0) {
        allLeads.push(...leadsData.data);
        offset += limit;
        hasMore = leadsData.data.length === limit; // Continue if we got a full page
      } else {
        hasMore = false;
      }
    }
    
    // Create a mapping from campaign_lead_map_id to actual lead.id
    const leadMapping = new Map();
    allLeads.forEach((item: any) => {
      if (item.campaign_lead_map_id && item.lead?.id) {
        leadMapping.set(item.campaign_lead_map_id, item.lead.id);
      }
    });
    
    const results = [];
    const errors = [];
    
    // Process each lead deletion with a small delay to avoid rate limiting
    for (const campaignLeadMapId of leadIds) {
      try {
        const actualLeadId = leadMapping.get(campaignLeadMapId);
        
        if (!actualLeadId) {
          errors.push({
            leadId: campaignLeadMapId,
            error: `Lead not found in campaign or missing lead data`
          });
          console.error(`Lead with campaign_lead_map_id ${campaignLeadMapId} not found`);
          continue;
        }
        
        console.log(`Deleting lead ${actualLeadId} (campaign_lead_map_id: ${campaignLeadMapId})`);
        
        const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads/${actualLeadId}?api_key=${SMARTLEAD_API_KEY}`;
        
        const response = await fetch(apiUrl, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          results.push({
            leadId: campaignLeadMapId,
            actualLeadId,
            success: true,
            data
          });
          console.log(`Successfully deleted lead ${actualLeadId} (campaign_lead_map_id: ${campaignLeadMapId})`);
        } else {
          const errorText = await response.text();
          errors.push({
            leadId: campaignLeadMapId,
            actualLeadId,
            error: `Failed to delete: ${response.status} - ${errorText}`
          });
          console.error(`Failed to delete lead ${actualLeadId}: ${errorText}`);
        }
        
        // Add small delay between requests to avoid rate limiting
        await sleep(100);
      } catch (error) {
        errors.push({
          leadId: campaignLeadMapId,
          error: `Error deleting lead: ${(error as Error).message}`
        });
        console.error(`Error deleting lead ${campaignLeadMapId}:`, error);
      }
    }
    
    const successCount = results.length;
    const errorCount = errors.length;
    
    return NextResponse.json({
      success: errorCount === 0,
      message: `Bulk delete completed. ${successCount} successful, ${errorCount} failed.`,
      successCount,
      errorCount,
      totalRequested: leadIds.length,
      results,
      errors
    });
  } catch (error) {
    console.error(`Error in bulk delete operation:`, error);
    return NextResponse.json(
      { error: `Error in bulk delete: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 