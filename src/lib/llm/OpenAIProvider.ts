import OpenAI from 'openai';
import { <PERSON><PERSON><PERSON><PERSON>ider, LLMMessage, LLMRequestOptions, LLMResponse, LoggerInterface } from './BaseLLMProvider';
import pLimit from 'p-limit';

/**
 * OpenAI (GPT) implementation of the LLM provider
 */
export class OpenAIProvider extends BaseLLMProvider {
  private client: OpenAI;
  private readonly defaultGptModel = 'gpt-4.1';
  private concurrencyLimit = pLimit(5);

  constructor(logger: LoggerInterface, apiKey?: string, defaultOptions: LLMRequestOptions = {}) {
    super('OpenAI', logger, {
      model: defaultOptions.model || 'gpt-4.1',
      ...defaultOptions
    });

    // Use API key from env if not provided
    const openaiApiKey = apiKey || process.env.OPENAI_API_KEY;
    
    if (!openaiApiKey) {
      throw new Error('OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass as parameter');
    }

    this.client = new OpenAI({
      apiKey: openai<PERSON><PERSON><PERSON><PERSON>
    });
  }

  /**
   * Call OpenAI's chat completion API
   */
  public async callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse> {
    try {
      const mergedOptions = {
        ...this.defaultOptions,
        ...options
      };

      const model = mergedOptions.model || this.defaultGptModel;
      this.logger.log('info', `Calling OpenAI with model ${model}`);
      
      // Convert our generic messages to OpenAI's format
      const openaiMessages = messages.map(message => ({
        role: message.role,
        content: message.content
      }));

      // Call OpenAI with rate limiting and concurrency limiting
      const response = await this.concurrencyLimit(() =>
        this.rateLimitedCall(async () => {
          return await this.client.chat.completions.create({
            model,
            messages: openaiMessages,
            temperature: mergedOptions.temperature,
            max_tokens: mergedOptions.maxTokens
          });
        }, 1000)
      );

      // Extract the content from the response
      const content = response.choices[0]?.message?.content || '';
      
      if (!content) {
        throw new Error('Empty response from OpenAI');
      }

      // Return in our standardized format
      return {
        content,
        usage: {
          promptTokens: response.usage?.prompt_tokens,
          completionTokens: response.usage?.completion_tokens,
          totalTokens: response.usage?.total_tokens
        },
        provider: this.name,
        model: response.model || model
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
} 