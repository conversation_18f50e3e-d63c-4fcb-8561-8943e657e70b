import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { emails } = await request.json()

    if (!emails || !Array.isArray(emails)) {
      return NextResponse.json(
        { error: 'Invalid emails array' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      const existsMap: Record<string, boolean> = {}
      
      if (emails.length > 0) {
        // Create placeholders for the IN clause
        const placeholders = emails.map((_, index) => `$${index + 1}`).join(', ')
        
        const query = `
          SELECT DISTINCT email 
          FROM contacts 
          WHERE email IN (${placeholders})
          AND email IS NOT NULL
        `
        
        const result = await client.query(query, emails)
        
        // Initialize all emails as not existing
        emails.forEach(email => {
          existsMap[email] = false
        })
        
        // Mark existing emails as true
        result.rows.forEach(row => {
          existsMap[row.email] = true
        })
      }

      return NextResponse.json({
        success: true,
        exists: existsMap
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Email check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check emails',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 