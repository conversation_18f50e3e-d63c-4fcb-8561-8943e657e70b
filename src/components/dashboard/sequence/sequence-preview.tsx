import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel } from "@/components/ui/alert-dialog";
import { Mail, Phone, Calendar, Clock, ArrowRight, Play, Edit2, Users, Briefcase, Sparkles } from 'lucide-react';

const SequencePreview = ({ onBack }) => {
  const sequence = {
    name: "Q4 Investment Opportunities",
    description: "Outreach sequence for potential equity investors in the Madison Ave portfolio",
    audience: "Equity Investors",
    dealType: "Acquisition",
    steps: [
      {
        type: 'email',
        delay: 0,
        template: 'initial_outreach',
        subject: 'Madison Avenue Office Portfolio - Investment Opportunity',
        preview: 'I hope this email finds you well. Given your focus on prime office assets...'
      },
      {
        type: 'email',
        delay: 3,
        template: 'follow_up_1',
        subject: 'RE: Madison Avenue Portfolio - Additional Materials',
        preview: 'Following up on my previous email regarding the Madison Avenue opportunity...'
      },
      {
        type: 'call',
        delay: 5,
        template: 'phone_script_1',
        subject: 'Initial Discussion',
        preview: 'Introduction and high-level overview of the opportunity...'
      }
    ]
  };

  return (
    <div className="h-screen bg-gray-50 overflow-auto p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Button variant="ghost" onClick={onBack} className="mb-2">
            ← Back to Builder
          </Button>
          <h1 className="text-2xl font-bold">Sequence Preview</h1>
          <p className="text-gray-500 mt-1">Preview and activate your sequence</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Edit2 className="h-4 w-4 mr-2" />
            Edit Sequence
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button>
                <Play className="h-4 w-4 mr-2" />
                Activate Sequence
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Activate Sequence</AlertDialogTitle>
                <AlertDialogDescription>
                  This will start the sequence for all selected contacts. Communications will be sent automatically based on the defined schedule.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction>Activate</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          {/* Timeline View */}
          <Card>
            <CardHeader>
              <CardTitle>Sequence Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative">
                {sequence.steps.map((step, index) => (
                  <TimelineStep 
                    key={index}
                    step={step}
                    isLast={index === sequence.steps.length - 1}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* Sequence Details */}
          <Card>
            <CardHeader>
              <CardTitle>Sequence Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500">Target Audience</div>
                  <div className="flex items-center mt-1">
                    <Users className="h-4 w-4 mr-2 text-gray-400" />
                    {sequence.audience}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Deal Type</div>
                  <div className="flex items-center mt-1">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-400" />
                    {sequence.dealType}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Total Duration</div>
                  <div className="flex items-center mt-1">
                    <Clock className="h-4 w-4 mr-2 text-gray-400" />
                    5 days
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Steps</div>
                  <div className="flex items-center mt-1">
                    <ArrowRight className="h-4 w-4 mr-2 text-gray-400" />
                    {sequence.steps.length} steps
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Recommendations */}
          <Card className="bg-gradient-to-br from-blue-50 to-white border-blue-100">
            <CardHeader>
              <CardTitle>AI Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <RecommendationItem 
                  text="Consider adding a follow-up call after the second email"
                  type="improvement"
                />
                <RecommendationItem 
                  text="Email open rates peak at 10 AM EST for this audience"
                  type="timing"
                />
                <RecommendationItem 
                  text="Personalization opportunities detected in templates"
                  type="content"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const TimelineStep = ({ step, isLast }) => {
  const icons = {
    email: <Mail className="h-4 w-4" />,
    call: <Phone className="h-4 w-4" />,
    meeting: <Calendar className="h-4 w-4" />
  };

  return (
    <div className="flex items-start mb-8">
      <div className="flex flex-col items-center">
        <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
          {icons[step.type]}
        </div>
        {!isLast && <div className="h-16 w-px bg-gray-200 my-2" />}
      </div>
      <div className="ml-4 flex-1">
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <div className="font-medium">{step.subject}</div>
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              {step.delay === 0 ? 'Immediate' : `Day ${step.delay}`}
            </Badge>
          </div>
          <p className="text-gray-600 text-sm">{step.preview}</p>
          <div className="mt-3 flex justify-end">
            <Button variant="outline" size="sm">Preview Template</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const RecommendationItem = ({ text, type }) => {
  const colors = {
    improvement: 'text-purple-600 bg-purple-100',
    timing: 'text-blue-600 bg-blue-100',
    content: 'text-green-600 bg-green-100'
  };

  return (
    <div className="flex items-start space-x-3">
      <div className={`h-6 w-6 rounded-full ${colors[type]} flex items-center justify-center flex-shrink-0`}>
        <Sparkles className="h-3 w-3" />
      </div>
      <p className="text-sm text-gray-600">{text}</p>
    </div>
  );
};

export default SequencePreview;