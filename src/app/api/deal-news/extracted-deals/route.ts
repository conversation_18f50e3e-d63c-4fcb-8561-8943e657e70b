import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';



export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const pageSize = parseInt(searchParams.get('pageSize') || '20');
  const search = searchParams.get('search') || '';
  const sort = searchParams.get('sort') || 'date_desc';
  const source = searchParams.get('source') || '';
  
  const offset = (page - 1) * pageSize;
  
  try {
    const client = await pool.connect();
    try {
      // Build the WHERE clause for searching
      let whereClause = '';
      let searchParams: any[] = [];
      let paramCount = 0;
      
      if (search) {
        paramCount++;
        whereClause = `
          WHERE (
            dnd.deal_type ILIKE $${paramCount} OR
            dnd.deal_status ILIKE $${paramCount} OR
            dnd.property_type ILIKE $${paramCount} OR
            dnd.property_address ILIKE $${paramCount} OR
            EXISTS (
              SELECT 1 FROM deal_news_companies dnc
              WHERE dnc.deal_id = dnd.id AND
                dnc.company_name ILIKE $${paramCount}
            )
          )
        `;
        searchParams.push(`%${search}%`);
      }
      
      // Add source filter if provided
      if (source) {
        paramCount++;
        whereClause = whereClause 
          ? `${whereClause} AND dn.news_source = $${paramCount}`
          : `WHERE dn.news_source = $${paramCount}`;
        searchParams.push(source);
      }
      
      // Determine the ORDER BY clause based on sort parameter
      let orderByClause = '';
      switch (sort) {
        case 'date_asc':
          orderByClause = 'ORDER BY dnd.created_at ASC NULLS LAST, dnd.id DESC';
          break;
        case 'date_desc':
          orderByClause = 'ORDER BY dnd.created_at DESC NULLS LAST, dnd.id DESC';
          break;
        case 'value_asc':
          orderByClause = 'ORDER BY dnd.deal_value ASC NULLS LAST, dnd.id DESC';
          break;
        case 'value_desc':
          orderByClause = 'ORDER BY dnd.deal_value DESC NULLS LAST, dnd.id DESC';
          break;
        default:
          orderByClause = 'ORDER BY dnd.deal_date DESC NULLS LAST, dnd.id DESC';
      }
      
      // Count total deals matching search criteria
      const countQuery = `
        SELECT COUNT(*) as total
        FROM deal_news_deals dnd
        LEFT JOIN deal_news dn ON dnd.news_id = dn.id
        ${whereClause}
      `;
      
      const countResult = await client.query(countQuery, searchParams);
      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / pageSize);
      
      // Fetch deals with company information
      const dealsQuery = `
        SELECT 
          dnd.id,
          dnd.news_id,
          dnd.deal_type,
          dnd.deal_status,
          dnd.deal_date,
          dnd.deal_value,
          dnd.currency,
          dnd.cap_rate,
          dnd.ltv,
          dnd.confidence_score,
          dnd.property_type,
          dnd.property_size,
          dnd.property_size_unit,
          dnd.property_address,
          dnd.created_at,
          dn.news_title,
          dn.news_date,
          dn.news_source,
          (
            SELECT json_agg(json_build_object(
              'id', dnc.id,
              'company_name', dnc.company_name,
              'role', dnc.role
            ))
            FROM deal_news_companies dnc
            WHERE dnc.deal_id = dnd.id
          ) as companies
        FROM deal_news_deals dnd
        LEFT JOIN deal_news dn ON dnd.news_id = dn.id
        ${whereClause}
        ${orderByClause}
        LIMIT $${searchParams.length + 1} OFFSET $${searchParams.length + 2}
      `;
      
      const dealsResult = await client.query(
        dealsQuery, 
        [...searchParams, pageSize, offset]
      );
      
      // Get source statistics
      const sourceStatsQuery = `
        SELECT 
          dn.news_source,
          COUNT(DISTINCT dnd.id) as deal_count,
          COUNT(DISTINCT CASE WHEN DATE(dnd.created_at AT TIME ZONE 'UTC') >= DATE(NOW()) THEN dnd.id END) as today_count
        FROM 
          deal_news_deals dnd
        JOIN 
          deal_news dn ON dnd.news_id = dn.id
        GROUP BY 
          dn.news_source
        ORDER BY 
          deal_count DESC
      `;
      
      const sourceStatsResult = await client.query(sourceStatsQuery);
      
      // Get daily statistics for last 7 days
      const dailyStatsQuery = `
        SELECT 
          dn.news_source,
          DATE(dnd.created_at AT TIME ZONE 'UTC') as date,
          COUNT(dnd.id) as count
        FROM 
          deal_news_deals dnd
        JOIN 
          deal_news dn ON dnd.news_id = dn.id
        WHERE 
          DATE(dnd.created_at AT TIME ZONE 'UTC') >= DATE(NOW()) - INTERVAL '7 days'
        GROUP BY 
          dn.news_source, DATE(dnd.created_at AT TIME ZONE 'UTC')
        ORDER BY 
          date ASC, count DESC
      `;
      
      const dailyStatsResult = await client.query(dailyStatsQuery);
      return NextResponse.json({
        deals: dealsResult.rows,
        total,
        page,
        totalPages,
        pageSize,
        sources: sourceStatsResult.rows,
        dailyStats: dailyStatsResult.rows
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching deals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch deals' },
      { status: 500 }
    );
  }
} 