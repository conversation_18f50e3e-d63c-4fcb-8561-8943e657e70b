import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import pLimit from 'p-limit'; // <-- Make sure to install p-limit
import { PoolClient } from 'pg';
import { OpenAIProvider } from '@/lib/llm/OpenAIProvider';
import { LoggerInterface, LogLevel } from '@/lib/llm/BaseLLMProvider';

export const dynamic = 'force-dynamic';

// Keep track of processing state
let isProcessing = false;
let currentProgress = {
  processed: 0,
  total: 0,
  logs: [] as string[],
  errors: [] as { id: number; error: string }[],
  results: [] as any[],
  lastUpdate: new Date()
};

/**
 * Helper to parse date strings in a few common formats.
 */
function parseDate(dateStr: string): string | null {
  if (!dateStr || dateStr.toLowerCase() === 'null') return null;

  try {
    // Try different date formats
    const possibleFormats = [
      // Standard ISO format
      /^\d{4}-\d{2}-\d{2}/,
      // US format MM/DD/YYYY
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})/,
      // Text format like "January 1, 2024"
      /^[A-Za-z]+ \d{1,2},? \d{4}/
    ];

    for (const format of possibleFormats) {
      if (format.test(dateStr)) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
      }
    }

    // If no format matches, try direct parsing
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }

    console.log('Could not parse date:', dateStr);
    return null;
  } catch (e) {
    console.error('Date parsing error:', e, 'for date:', dateStr);
    return null;
  }
}

/**
 * Simple logger that includes timestamps in the log message.
 */
function logWithTimestamp(message: string) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  return `[${timestamp}] ${message}`;
}

// Create a logger for OpenAI provider
const llmLogger: LoggerInterface = {
  log: (level: LogLevel, message: string) => {
    logWithTimestamp(`[OpenAI] [${level.toUpperCase()}] ${message}`);
  }
};

/**
 * Main endpoint to start or check processing.
 */
export async function POST(request: Request) {
  console.log('\n\n=== NEW REQUEST ===');
  logWithTimestamp('Received new request');

  try {
    const body = await request.json();
    logWithTimestamp(`Request body: ${JSON.stringify(body)}`);

    const { testMode = false, limit = 3, action = 'start' } = body;

    // If this is just a status check, return current progress
    if (action === 'status') {
      logWithTimestamp(
        `Status check - isProcessing: ${isProcessing}, processed: ${currentProgress.processed}/${currentProgress.total}`
      );
      return NextResponse.json({
        isProcessing,
        ...currentProgress,
        success: true
      });
    }

    // Prevent double-starting
    if (isProcessing) {
      logWithTimestamp('Rejected: Already processing');
      return NextResponse.json({
        success: false,
        error: 'Processing already in progress',
        isProcessing,
        ...currentProgress
      });
    }

    logWithTimestamp(`Starting new processing job - testMode: ${testMode}, limit: ${limit}`);

    // Reset progress
    isProcessing = true;
    currentProgress = {
      processed: 0,
      total: 0,
      logs: [logWithTimestamp('Starting processing...')],
      errors: [],
      results: [],
      lastUpdate: new Date()
    };

    // Start background processing
    processItems(testMode, limit).catch((error) => {
      console.error('Processing error:', error);
      logWithTimestamp(`Fatal error in background process: ${error.message}`);
      logWithTimestamp(error.stack || 'No stack trace available');
      isProcessing = false;
      currentProgress.logs.push(logWithTimestamp(`Fatal error: ${error.message}`));
      currentProgress.errors.push({ id: 0, error: error.message });
    });

    return NextResponse.json({
      success: true,
      message: 'Processing started',
      isProcessing,
      ...currentProgress
    });
  } catch (error: any) {
    console.error('Request handling error:', error);
    logWithTimestamp(`Error handling request: ${error.message}`);
    logWithTimestamp(error.stack || 'No stack trace available');
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * Processes items from the database in chunks of up to 10 at a time.
 * Each item is processed in parallel with concurrency = 10.
 */
async function processItems(testMode: boolean, maxItems: number) {
  logWithTimestamp('Starting processItems function');
  
  // Initialize OpenAI provider
  const openaiProvider = new OpenAIProvider(llmLogger);
  logWithTimestamp('OpenAI provider initialized');

  let client: PoolClient | undefined;
  try {
    logWithTimestamp('Connecting to database...');
    client = await pool.connect();
    logWithTimestamp('Database connected');

    // Count how many items we need to process
    logWithTimestamp('Querying for total count...');
    const { rows: countRows } = await client.query(`
      SELECT COUNT(*) as total
      FROM deal_news 
      WHERE raw_html IS NOT NULL 
        AND news_text IS NULL
        AND processed = false;
    `);

    // If in testMode, we only process up to maxItems
    const grandTotal = parseInt(countRows[0].total, 10);
    currentProgress.total = testMode ? Math.min(maxItems, grandTotal) : grandTotal;
    logWithTimestamp(`Found ${grandTotal} total items, but will process up to ${currentProgress.total}`);

    const concurrencyLimit = pLimit(3); // Reduce concurrent API calls

    // Keep going until we've processed the total
    while (currentProgress.processed < currentProgress.total) {
      // If we only need a certain number more, let's just fetch that many
      const remaining = currentProgress.total - currentProgress.processed;
      const batchSize = Math.min(10, remaining);

      logWithTimestamp(`\nFetching next ${batchSize} item(s) from DB...`);
      const { rows } = await client.query(`
        SELECT id, raw_html 
        FROM deal_news 
        WHERE raw_html IS NOT NULL
          AND news_text IS NULL
          AND processed = false
        ORDER BY id DESC
        LIMIT ${batchSize};
      `);

      if (rows.length === 0) {
        logWithTimestamp('No more items to process.');
        break;
      }

      // Process each row in parallel with concurrency = 10 (bounded by p-limit)
      const processingTasks = rows.map((row) =>
        concurrencyLimit(() => {
          if (!client) throw new Error('Database client is not initialized');
          return processSingleItem(row, testMode, openaiProvider, client);
        })
      );

      // Wait until all items in this batch are done
      const batchResults = await Promise.all(processingTasks);

      // Accumulate results & errors
      for (const result of batchResults) {
        if (result.success) {
          currentProgress.processed++;
          currentProgress.results.push(result.details);
        } else {
          currentProgress.errors.push({ id: result.id, error: result.error });
        }

        // If in test mode and we have reached the limit, break out
        if (testMode && currentProgress.processed >= maxItems) {
          logWithTimestamp('Test mode limit reached. Stopping early.');
          break;
        }
      }

      currentProgress.lastUpdate = new Date();

      // If in test mode & processed >= limit, break the outer loop
      if (testMode && currentProgress.processed >= maxItems) {
        break;
      }
    }
  } catch (error: any) {
    console.error('Processing error:', error);
    logWithTimestamp(`Processing error: ${error.message}`);
    logWithTimestamp(error.stack || 'No stack trace available');
    throw error;
  } finally {
    if (client) {
      logWithTimestamp('Releasing database connection');
      client.release();
    }
    isProcessing = false;
    logWithTimestamp('Processing complete');
    currentProgress.logs.push(logWithTimestamp('Processing complete'));
  }
}

/**
 * Process a single row: clean HTML, call OpenAI, parse results, update DB.
 */
async function processSingleItem(
  row: any,
  testMode: boolean,
  openaiProvider: OpenAIProvider,
  client: PoolClient
) {
  // Add delay at start of processing
  await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
  
  // Default result object
  const resultObj = { id: row.id, success: false, error: '', details: {} };

  try {
    logWithTimestamp(`\n--- Processing item ${row.id} ---`);
    currentProgress.logs.push(logWithTimestamp(`Processing item ${row.id}...`));

    // 1) Clean HTML
    logWithTimestamp(`Cleaning HTML for item ${row.id}`);
    const cleanText = row.raw_html
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Add safety limit for API input (approximately 100k characters to stay well under token limit)
    const MAX_TEXT_LENGTH = 128000;
    const truncatedText = cleanText.length > MAX_TEXT_LENGTH 
      ? cleanText.substring(0, MAX_TEXT_LENGTH) + '... [truncated due to length]'
      : cleanText;
    
    logWithTimestamp(`HTML cleaned, original length: ${cleanText.length} characters${
      cleanText.length > MAX_TEXT_LENGTH ? `, truncated to ${truncatedText.length} characters` : ''
    }`);

    // 2) Use GPT to extract
    logWithTimestamp(`Calling OpenAI API for item ${row.id}`);
    const llmResponse = await openaiProvider.callLLM([
      {
        role: 'system',
        content:
          'You extract fields from the text without summarizing or altering content.'
      },
      {
        role: 'user',
        content: `Extract the following fields from the raw text of a news article:
1. Title: The title of the article.
2. Date: The publication date of the article (if available, otherwise return null).
3. Body: The full body text of the article.

Raw Text:
${truncatedText}

Output format:
Title: <title>
Date: <date>
Body: <body>`
      }
    ], {
      model: 'gpt-4.1-mini',
    });
    logWithTimestamp(`Received OpenAI response for item ${row.id}`);

    // 3) Parse GPT output
    const content = llmResponse.content;
    if (!content) {
      throw new Error('No content in OpenAI response');
    }
    logWithTimestamp(`Parsing OpenAI output for item ${row.id}`);

    // Expecting lines in the format:
    // Title: ...
    // Date: ...
    // Body: ...
    const [titlePart = "", rest = ""] = content.split('\nDate: ');
    const [dateStr = "", bodyPart = ""] = rest.split('\nBody: ');

    const title = titlePart.replace('Title: ', '').trim();
    const parsedDate = parseDate(dateStr.trim());
    const body = bodyPart.trim();

    logWithTimestamp(
      `Parsed content - Title length: ${title.length}, Date: ${parsedDate}, Body length: ${body.length}`
    );

    // 4) Update DB (if not test mode)
    if (!testMode) {
      logWithTimestamp(`Updating database for item ${row.id}`);
      await client.query(
        `
        UPDATE deal_news
        SET news_text = $1,
            news_title = $2,
            news_date = $3,
            processed = true
        WHERE id = $4
      `,
        [body, title, parsedDate, row.id]
      );
      logWithTimestamp(`Database updated for item ${row.id}`);
    } else {
      logWithTimestamp(`Test mode - skipping DB update for item ${row.id}`);
    }

    // 5) Build success details
    resultObj.success = true;
    resultObj.details = {
      id: row.id,
      originalHtml: row.raw_html.substring(0, 500) + '...',
      cleanedText: cleanText.substring(0, 500) + '...',
      extractedContent: { title, date: parsedDate, body }
    };

    logWithTimestamp(`Completed processing item ${row.id}`);
    return resultObj;
  } catch (error: any) {
    console.error(`Error processing item ${row.id}:`, error);
    const msg = `Error processing item ${row.id}: ${error.message}`;
    logWithTimestamp(msg);
    logWithTimestamp(error.stack || 'No stack trace available');
    currentProgress.logs.push(logWithTimestamp(msg));
    resultObj.error = error.message;
    return resultObj;
  }
}
