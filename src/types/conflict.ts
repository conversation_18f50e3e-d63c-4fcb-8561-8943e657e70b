export interface FieldConflict {
  existing_value: string | null | any[]
  new_value: string | null | any
  source: string
  created_at: string
  field_type: 'string' | 'number' | 'boolean' | 'date' | 'json' | 'company_selection'
  match_reason?: string
}

export interface ConflictData {
  [fieldName: string]: FieldConflict
}

export type ConflictStatus = 'none' | 'pending' | 'resolved'

export interface ConflictRecord {
  id: number
  type: 'company' | 'contact'
  name: string
  conflicts: ConflictData
  conflict_status: ConflictStatus
  conflict_created_at: string | null
  conflict_resolved_at: string | null
  conflict_source: string | null
}

export interface CompanyConflict extends ConflictRecord {
  type: 'company'
  company_id: number
  company_name: string
  company_website?: string
  industry?: string
}

export interface ContactConflict extends ConflictRecord {
  type: 'contact'
  contact_id: number
  full_name: string
  email?: string
  company_name?: string
  company_id?: number
  match_reason?: string
  linkedin_url?: string
}

export interface ConflictResolution {
  record_id: number
  record_type: 'company' | 'contact'
  field_name: string
  resolution: 'keep_existing' | 'use_new' | 'manual'
  manual_value?: string
}

export interface ConflictPreviewRow {
  index: number
  selected: boolean
  conflict_type: 'none' | 'field_conflict' | 'duplicate_email' | 'duplicate_company' | 'duplicate_contact'
  existing_record_id?: number
  conflicts?: ConflictData
  // All the original CSV fields
  [key: string]: any
}

export interface ConflictUploadResult {
  success: boolean
  stats: {
    companies: {
      total: number
      added: number
      updated_with_conflicts: number
      skipped: number
      error: number
    }
    contacts: {
      total: number
      added: number
      updated_with_conflicts: number
      skipped: number
      error: number
    }
  }
  conflicts: {
    companies: CompanyConflict[]
    contacts: ContactConflict[]
  }
  logs: string[]
  error?: string
} 