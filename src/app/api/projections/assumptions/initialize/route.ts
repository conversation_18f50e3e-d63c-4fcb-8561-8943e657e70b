import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'
import { defaultAssumptions } from '@/config/assumptions'

export async function POST() {
  try {
    // First, delete all existing assumptions
    await pool.query('DELETE FROM projections_assumptions');

    // Then insert all default assumptions
    for (const [category, values] of Object.entries(defaultAssumptions)) {
      for (const [field, value] of Object.entries(values)) {
        const query = `
          INSERT INTO projections_assumptions (category, field, value)
          VALUES ($1, $2, $3)
        `
        const numericValue = typeof value === 'string' ? 0 : value
        await pool.query(query, [category, field, numericValue])
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 