"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarClock, FileSearch } from 'lucide-react';
import { Contact } from '../types';

interface SearchedDataSectionProps {
  contact: Contact;
}

const SearchedDataSection: React.FC<SearchedDataSectionProps> = ({ contact }) => {
  if (!contact.searched && !contact.searched_profile) return null;
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-medium">Search Results</CardTitle>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 flex items-center gap-1">
            <CalendarClock className="h-3.5 w-3.5 mr-1" />
            {contact.searched_date ? new Date(contact.searched_date).toLocaleDateString() : 'No date'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="text-sm">
        {contact.searched_tokens_used && (
          <div className="text-xs text-gray-500 mb-4">
            Tokens used: {contact.searched_tokens_used}
          </div>
        )}
        <div className="prose max-w-none">
          {contact.searched_profile ? (
            <div className="whitespace-pre-line">{contact.searched_profile}</div>
          ) : (
            <div className="text-gray-500 italic">No search profile available</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchedDataSection; 