"use client"

import React, { useState, useRef, useEffect } from 'react'
import ContactsView from './people/ContactsView'
import { Grid } from './people/Grid'
import MappingTablesView, { MappingTablesViewRef } from './people/MappingTablesView'
import List44View from './people/List44View'
import ThreadManagementView from './people/ThreadManagementView'
import ContactOverview from './people/ContactOverview'
import { toast } from "sonner"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface PeopleStuffProps {
  isActive: boolean;
}

export const PeopleStuff: React.FC<PeopleStuffProps> = ({ isActive }) => {
  const [activeTab, setActiveTab] = useState<string>('contacts')
  const [pendingTabChange, setPendingTabChange] = useState<string | null>(null)
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false)
  
  // Create a ref to expose a method that can be called to check for unsaved changes
  const mappingTableRef = useRef<MappingTablesViewRef>(null)
  
  if (!isActive) return null

  const tabs = [
    { id: 'contacts', name: 'Contacts' },
    { id: 'overview', name: 'Contact Overview' },
    { id: 'grid', name: 'Grid' },
    { id: 'list44', name: 'List 44' },
    { id: 'threads', name: 'Thread Management' },
    { id: 'mapping', name: 'Mapping Tables' },
  ]
  
  const handleTabChange = (tabId: string) => {
    if (activeTab === 'mapping' && mappingTableRef.current?.hasUnsavedChanges()) {
      setPendingTabChange(tabId)
      setShowUnsavedChangesDialog(true)
      return
    }
    
    // No unsaved changes, proceed with tab change
    setActiveTab(tabId)
  }
  
  const handleConfirmTabChange = () => {
    if (pendingTabChange) {
      setActiveTab(pendingTabChange)
      setPendingTabChange(null)
    }
    setShowUnsavedChangesDialog(false)
  }
  
  const handleCancelTabChange = () => {
    setPendingTabChange(null)
    setShowUnsavedChangesDialog(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`pb-4 px-4 text-sm font-medium border-b-2 transition-all ${
              activeTab === tab.id 
                ? 'border-blue-600 text-blue-600' 
                : 'border-transparent hover:border-gray-200'
            }`}
            type="button"
          >
            {tab.name}
          </button>
        ))}
      </div>

      {activeTab === 'grid' && <Grid />}
      {activeTab === 'contacts' && <ContactsView />}
      {activeTab === 'overview' && <ContactOverview />}
      {activeTab === 'list44' && <List44View />}
      {activeTab === 'threads' && <ThreadManagementView />}
      {activeTab === 'mapping' && <MappingTablesView ref={mappingTableRef} />}
      
      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showUnsavedChangesDialog} onOpenChange={setShowUnsavedChangesDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes in the Mapping Tables. Would you like to discard these changes?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelTabChange}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmTabChange}>Discard Changes</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}