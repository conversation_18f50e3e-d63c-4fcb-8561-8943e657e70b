"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ArrowLeftCircle,
  ArrowRightCircle,
  Building2,
  CheckCircle,
  Coins,
  Eye,
  FileText,
  LineChart,
  Lock,
  Rocket,
  Scale,
  Shield,
  Users,
  Users2,
  Wallet,
  Zap,
} from "lucide-react"
export function CREPitchPresentation() {
  const [currentSlide, setCurrentSlide] = useState(0)

  // Slides content for Commercial Real Estate Capital Advisory Pitch
  const slides = [
    {
      title: "Title Slide",
      content: (
        <div className="space-y-8">
          <div className="flex items-center space-x-6">
            <div
              className="p-5 rounded-2xl bg-gradient-to-br 
                from-blue-500/10 to-blue-500/5 
                border border-blue-500/20 
                shadow-[0_0_30px_rgba(59,130,246,0.2)]"
            >
              <Building2 className="w-16 h-16 text-blue-400" />
            </div>
            <div>
              <h3
                className="text-3xl font-syne font-light mb-4
                  bg-gradient-to-r from-blue-400 to-blue-300 text-transparent bg-clip-text"
              >
                ANAX Capital
              </h3>
              <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
                Reinventing Commercial Real Estate Capital Advisory with Tech & AI
              </p>
            </div>
          </div>
          <p className="text-lg text-gray-400 max-w-3xl">
            Connecting real estate borrowers and lenders through a data-rich, automated
            platform that streamlines deal flow, reduces friction, and unlocks billions in
            value across the CRE market.
          </p>
        </div>
      ),
    },
    {
      title: "Executive Summary",
      content: (
        <div className="space-y-6">
          <p className="text-xl text-blue-100/90 leading-relaxed">
            <strong className="text-blue-300">Who We Are:</strong> A tech-driven commercial real estate capital advisory
            platform combining AI, big data, and automation to reduce friction in CRE financing.
          </p>
          <p className="text-xl text-blue-100/90 leading-relaxed">
            <strong className="text-blue-300">Market Size:</strong> Over \$600B in annual CRE transactions in the U.S.,
            generating \$6–\$10B in advisory fees.
          </p>
          <p className="text-xl text-blue-100/90 leading-relaxed">
            <strong className="text-blue-300">Our Solution:</strong> An end-to-end digital advisory platform that matches
            sponsors to the right capital sources in seconds—faster closings, more deals.
          </p>
          <p className="text-xl text-blue-100/90 leading-relaxed">
            <strong className="text-blue-300">Expansion:</strong> Leverage unique deal data to launch a direct lending
            arm, capturing origination fees + interest income.
          </p>
        </div>
      ),
    },
    {
      title: "The Problem",
      content: (
        <div className="space-y-6">
          <ul className="list-disc list-inside text-lg text-gray-300">
            <li>
              <strong>Fragmentation:</strong> Thousands of lenders, each with different
              criteria—borrowers struggle to find the right match.
            </li>
            <li>
              <strong>Manual Processes:</strong> Relationship-driven, slow, and reliant on
              endless phone calls, emails, and spreadsheets.
            </li>
            <li>
              <strong>Opaque Pricing:</strong> Borrowers lack transparency into market terms
              and often overpay or miss better opportunities.
            </li>
            <li>
              <strong>Underserved Deals:</strong> Small and mid-market sponsors are overlooked
              by big brokerages focused on mega-transactions.
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Our Vision",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Tech + Human Touch**: We automate away administrative tasks while preserving
            the high-touch relationship management that drives trust in CRE.
          </p>
          <p className="text-lg text-gray-300">
            **Complete Market Coverage**: A “Bloomberg Terminal” for CRE capital—instantly
            updated data on lenders, terms, and property valuations.
          </p>
          <p className="text-lg text-gray-300">
            **AI-Driven Matching**: Sponsor deals automatically paired with best-fit lenders
            based on real-time data, sponsor track records, and risk profiles.
          </p>
          <p className="text-lg text-gray-300">
            **Seamless Experience**: Borrowers manage deals from submission to close via a
            single dashboard; lenders see curated, pre-qualified deals.
          </p>
        </div>
      ),
    },
    {
      title: "Market Opportunity",
      content: (
        <div className="space-y-6">
          <p className="text-xl text-white/80">
            The U.S. CRE debt market is valued at \$4.5–\$5 trillion in outstanding debt, with
            \$600B+ in annual transactions. 
          </p>
          <p className="text-xl text-white/80">
            Fee pool for capital advisory sits around \$6–\$10B per year, just in the U.S.
          </p>
          <p className="text-xl text-white/80">
            Post-2008, private lending exploded, creating more niche capital sources—and more
            complexity ripe for a tech solution.
          </p>
          <p className="text-xl text-white/80">
            Even a 1% capture of the advisory market translates to billions in arranged volume
            and tens of millions in fees.
          </p>
        </div>
      ),
    },
    {
      title: "The Platform",
      content: (
        <div className="space-y-8">
          <p className="text-lg text-gray-300">
            **Data & Intelligence**: We aggregate lender profiles, loan terms, sponsor
            histories, and market news into a single real-time source of truth.
          </p>
          <p className="text-lg text-gray-300">
            **AI-Enhanced Deal Workflow**: Automated due diligence, risk scoring, and
            streamlined documentation.
          </p>
          <p className="text-lg text-gray-300">
            **Advisor Dashboard**: Pipeline management, auto-generated term sheets, integrated
            doc management—minimizing repetitive tasks.
          </p>
          <p className="text-lg text-gray-300">
            **Borrower & Lender Portals**: Submit deals, track status, interact directly, and
            finalize closings online.
          </p>
        </div>
      ),
    },
    {
      title: "Expansion: Direct Lending Strategy",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Why Lend Ourselves?** Our data advantage allows us to identify high-probability,
            high-quality deals early. 
          </p>
          <p className="text-lg text-gray-300">
            **Data-Driven Underwriting**: We leverage our platform’s analytics to price deals
            more accurately than traditional lenders.
          </p>
          <p className="text-lg text-gray-300">
            **Revenue Upside**: Capture origination fees, plus recurring interest or mezzanine
            returns from select deals.
          </p>
          <p className="text-lg text-gray-300">
            **Risk Management**: Start small with mezz or bridge loans, partner with co-lenders
            to spread risk, and gradually scale as we build a track record.
          </p>
        </div>
      ),
    },
    {
      title: "Traction & Milestones",
      content: (
        <div className="space-y-6">
          <ul className="list-disc list-inside text-lg text-gray-300">
            <li>
              <strong>Pilot Deals:</strong> Early closings with \$X million financed, average
              deal size \$Y million.
            </li>
            <li>
              <strong>Growing Lender Network:</strong> Over 50+ lenders onboarded, with new
              inquiries weekly.
            </li>
            <li>
              <strong>Revenue Growth:</strong> Advisory fees increasing at a steady pace; 
              pipeline deals valued at \$Z million.
            </li>
            <li>
              <strong>Beta Test for Direct Lending:</strong> Identified high-quality deals 
              to fund via our first SPV.
            </li>
          </ul>
        </div>
      ),
    },
    {
      title: "Competitive Landscape",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Traditional Brokerages (CBRE, JLL, Eastdil)**: Relationship-driven, limited
            technology, focus on larger institutional deals.
          </p>
          <p className="text-lg text-gray-300">
            **Proptech Platforms (Various)**: Some digital matching solutions exist, but few
            offer robust AI or full-service advisory.
          </p>
          <p className="text-lg text-gray-300">
            **Our Edge**: Deep data integration, automated processes, and an upcoming direct
            lending arm create a powerful, sticky ecosystem.
          </p>
        </div>
      ),
    },
    {
      title: "Business Model & Financials",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Advisory Fees**: 0.5%–2% of deal size, with subscription add-ons for lenders
            wanting premium data.
          </p>
          <p className="text-lg text-gray-300">
            **Direct Lending**: Origination fees + interest spread, potentially securitize or
            sell loans post-closing.
          </p>
          <p className="text-lg text-gray-300">
            **3–5 Year Projections**: Show steady increase in deal flow, culminating in
            profitability driven by higher-margin direct lending.
          </p>
          <p className="text-lg text-gray-300">
            **Key Assumptions**: Market adoption rate, average deal size, default/loss rates
            if lending, speed of expansion to new markets.
          </p>
        </div>
      ),
    },
    {
      title: "Funding Ask & Use of Proceeds",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Raising \$X million** in Seed/Series A to accelerate platform growth and launch
            our direct lending pilot.
          </p>
          <ul className="list-disc list-inside text-lg text-gray-300">
            <li>
              Product Development & AI Enhancements
            </li>
            <li>
              Regulatory & Compliance for Lending Licenses
            </li>
            <li>
              Hiring (Sales, Data, Advisory Experts)
            </li>
            <li>
              Seed Capital for Direct Lending Vehicle
            </li>
          </ul>
          <p className="text-lg text-gray-300">
            **Milestones**: Achieve 100+ deals a month, expand lender network to 200+, finalize
            pilot direct lending deals.
          </p>
        </div>
      ),
    },
    {
      title: "Team",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Founders**: CRE experts, data scientists, and AI engineers combining decades of
            experience.
          </p>
          <p className="text-lg text-gray-300">
            **Key Hires**: CTO with AI/ML background, Head of Lending from top private debt
            fund, plus advisors in real estate law.
          </p>
          <p className="text-lg text-gray-300">
            **Advisors**: Real estate veterans, industry bankers, and leading proptech
            innovators.
          </p>
        </div>
      ),
    },
    {
      title: "Closing & Q&A",
      content: (
        <div className="space-y-6">
          <p className="text-lg text-gray-300">
            **Join Us** in revolutionizing CRE finance. By combining data, AI, and an
            end-to-end digital experience, we aim to capture a significant slice of this
            \$600B+ market.
          </p>
          <p className="text-lg text-gray-300">
            **Thank You** for your time. We look forward to discussing how ANAX Capital can
            reshape commercial real estate funding.
          </p>
        </div>
      ),
    },
  ]

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  return (
    <Card className="bg-gradient-to-br from-gray-800/50 via-gray-900/50 to-black/50
      backdrop-blur-xl border border-blue-500/10
      shadow-[0_0_50px_rgba(0,0,0,0.3)]
      rounded-2xl overflow-hidden">
      <CardContent className="p-12">
        <div className="mb-12">
          <h2 className="text-4xl font-syne font-light mb-4
            bg-clip-text text-transparent bg-gradient-to-r 
            from-blue-300 via-blue-400 to-indigo-400
            [text-shadow:_0_0_30px_rgba(59,130,246,0.2)]">
            {slides[currentSlide].title}
          </h2>
          <div className="h-1 w-32 bg-gradient-to-r from-blue-500 via-blue-400 to-indigo-500 
            rounded-full shadow-[0_0_15px_rgba(59,130,246,0.3)]"></div>
        </div>

        <div className="min-h-[500px] mb-12 text-gray-100">
          {slides[currentSlide].content}
        </div>

        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={prevSlide}
            disabled={currentSlide === 0}
            className="flex items-center space-x-3 px-6 py-3 text-lg
              bg-gradient-to-r from-blue-500/10 to-transparent
              border border-blue-500/20 hover:border-blue-400/40
              text-blue-300 hover:text-blue-200
              transition-all duration-300 hover:scale-105
              disabled:opacity-50 disabled:hover:scale-100
              disabled:cursor-not-allowed"
          >
            <ArrowLeftCircle className="w-5 h-5" />
            <span>Previous</span>
          </Button>

          <div className="text-base text-blue-300/80 font-medium">
            Slide {currentSlide + 1} of {slides.length}
          </div>

          <Button
            variant="outline"
            onClick={nextSlide}
            disabled={currentSlide === slides.length - 1}
            className="flex items-center space-x-3 px-6 py-3 text-lg
              bg-gradient-to-r from-blue-500/10 to-transparent
              border border-blue-500/20 hover:border-blue-400/40
              text-blue-300 hover:text-blue-200
              transition-all duration-300 hover:scale-105
              disabled:opacity-50 disabled:hover:scale-100
              disabled:cursor-not-allowed"
          >
            <span>Next</span>
            <ArrowRightCircle className="w-5 h-5" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}