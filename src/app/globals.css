@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
 
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

/* Rich Text Editor content styling */
.rich-text-content {
  line-height: 1.5;
}

.rich-text-content p {
  margin-bottom: 1em;
}

.rich-text-content ul, 
.rich-text-content ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
  padding-left: 0.5em;
  list-style-position: outside;
}

/* Handle nested lists properly */
.rich-text-content ul ul,
.rich-text-content ol ol,
.rich-text-content ul ol,
.rich-text-content ol ul {
  margin-top: 0.5em;
  margin-bottom: 0;
}

.rich-text-content ul > li,
.rich-text-content ol > li {
  margin-bottom: 0.5em;
  position: relative;
  list-style-position: outside;
  display: list-item;
}

/* Fix for list-style-type: none that hides bullets */
.rich-text-content ul li[style*="list-style-type: none"] {
  list-style-type: disc !important;
}

.rich-text-content ul ul > li {
  list-style-type: circle;
}

.rich-text-content ul ul ul > li {
  list-style-type: square;
}

/* Fix for nested p tags in list items */
.rich-text-content li > p {
  margin: 0;
  display: inline;
}

/* Fix for bullet characters */
.rich-text-content li::marker,
.rich-text-content li::before {
  content: "";
  display: inline-block;
}

.rich-text-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.rich-text-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}

.rich-text-content table td,
.rich-text-content table th {
  border: 1px solid #e2e8f0;
  padding: 0.5em;
}

.rich-text-content table th {
  background-color: #f8fafc;
  font-weight: 600;
}

/* Remove unwanted dir attributes and styles */
.rich-text-content [dir="ltr"] {
  direction: inherit;
}

/* TinyMCE UI Improvements */
.tox-tinymce {
  border-radius: 0.375rem;
  border-color: #e2e8f0 !important;
}

.tox .tox-toolbar__group {
  border-color: #f1f5f9 !important;
}

.tox .tox-tbtn {
  border-radius: 0.25rem !important;
}

.tox .tox-tbtn:hover {
  background-color: #f1f5f9 !important;
}

/* Animation for highlighting messages */
@keyframes pulse-highlight {
  0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
  100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
}

.highlight-message {
  animation: pulse-highlight 2s infinite;
  border: 2px solid #3b82f6 !important;
}