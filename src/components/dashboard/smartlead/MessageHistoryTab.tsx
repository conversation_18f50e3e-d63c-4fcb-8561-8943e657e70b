"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>f<PERSON><PERSON><PERSON>, ArrowLeft, Mail, User, Calendar, Eye, MousePointer } from 'lucide-react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

interface Message {
  id: string;
  type: string;
  subject?: string;
  body?: string;
  html_body?: string;
  sent_at?: string;
  status?: string;
  from?: string;
  to?: string;
  opens?: number;
  clicks?: number;
  reply?: boolean;
  sequence_number?: number;
  email_sequence_id?: string;
}

interface Campaign {
  id: string;
  name: string;
  status?: string;
}

interface Lead {
  campaign_lead_map_id: string;
  lead_category_id?: string;
  status: string;
  created_at: string;
  lead: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    phone_number?: string;
    company_name?: string;
    website?: string;
    location?: string;
    custom_fields?: any;
  };
  contact_info?: {
    contact_id: number;
    company_id: number;
    contact_first_name: string;
    contact_last_name: string;
    contact_title: string;
    company_name: string;
    company_website: string;
    industry: string;
  };
}

const MessageHistoryTab: React.FC = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('');
  const [selectedLeadId, setSelectedLeadId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [loadingLeads, setLoadingLeads] = useState(false);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);
  const [messageTab, setMessageTab] = useState<string>('all');




  useEffect(() => {
    fetchCampaigns();
    
    // Check if campaign and lead were selected from leads tab
    const storedCampaignId = localStorage.getItem('viewMessageCampaignId');
    const storedLeadId = localStorage.getItem('viewMessageLeadId');
    
    if (storedCampaignId && storedLeadId) {
      setSelectedCampaignId(storedCampaignId);
      setSelectedLeadId(storedLeadId);
      
      // Clear after use
      localStorage.removeItem('viewMessageCampaignId');
      localStorage.removeItem('viewMessageLeadId');
    }
  }, []);

  useEffect(() => {
    if (selectedCampaignId) {
      fetchLeads(selectedCampaignId);
    } else {
      setLeads([]);
      setSelectedLeadId('');
      setMessages([]);
    }
  }, [selectedCampaignId]);

  useEffect(() => {
    if (selectedCampaignId && selectedLeadId) {
      fetchMessageHistory(selectedCampaignId, selectedLeadId);
    } else {
      setMessages([]);
    }
  }, [selectedCampaignId, selectedLeadId]);



  const fetchCampaigns = async () => {
    setLoadingCampaigns(true);
    try {
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error(`Failed to fetch campaigns: ${response.status}`);
      }
      
      const data = await response.json();
      if (data && data.campaigns) {
        setCampaigns(data.campaigns);
        
        // If no campaign is selected yet but we have campaigns, select the first one
        if (data.campaigns.length > 0 && !selectedCampaignId) {
          setSelectedCampaignId(data.campaigns[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error(`Failed to fetch campaigns: ${(error as Error).message}`);
    } finally {
      setLoadingCampaigns(false);
    }
  };

  const fetchLeads = async (campaignId: string) => {
    setLoadingLeads(true);
    try {
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/leads`);
      if (!response.ok) {
        throw new Error(`Failed to fetch leads: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Leads fetched for campaign ${campaignId}:`, data);
      
      if (data && Array.isArray(data)) {
        setLeads(data);
        
        // If no lead is selected yet but we have leads, select the first one
        if (data.length > 0 && !selectedLeadId) {
          setSelectedLeadId(data[0].campaign_lead_map_id);
        } else if (data.length === 0) {
          // Clear selected lead if no leads exist for this campaign
          setSelectedLeadId('');
          setMessages([]);
        }
      } else {
        setLeads([]);
        setSelectedLeadId('');
        setMessages([]);
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast.error(`Failed to fetch leads: ${(error as Error).message}`);
      setLeads([]);
      setSelectedLeadId('');
      setMessages([]);
    } finally {
      setLoadingLeads(false);
    }
  };

  const fetchMessageHistory = async (campaignId: string, campaignLeadMapId: string) => {
    setLoading(true);
    try {
      // Find the actual lead ID from the campaign_lead_map_id
      let selectedLead = leads.find(l => l.campaign_lead_map_id === campaignLeadMapId);
      
      // If lead not found in current leads array, fetch leads first
      if (!selectedLead) {
        console.log('Lead not found in current leads array, fetching leads...');
        const leadsResponse = await fetch(`/api/smartlead/campaigns/${campaignId}/leads`);
        if (leadsResponse.ok) {
          const leadsData = await leadsResponse.json();
          if (leadsData && Array.isArray(leadsData)) {
            selectedLead = leadsData.find(l => l.campaign_lead_map_id === campaignLeadMapId);
          }
        }
      }
      
      if (!selectedLead) {
        throw new Error(`Lead with campaign_lead_map_id ${campaignLeadMapId} not found`);
      }
      
      const actualLeadId = selectedLead.lead.id;
      console.log(`Fetching message history for actual lead ID ${actualLeadId} (campaign_lead_map_id: ${campaignLeadMapId})`);
      
      const response = await fetch(`/api/smartlead/campaigns/${campaignId}/leads/${actualLeadId}/message-history`);
      if (!response.ok) {
        throw new Error(`Failed to fetch message history: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Message history for lead ${actualLeadId}:`, data);
      console.log(`Frontend - Data type: ${typeof data}, Array: ${Array.isArray(data)}, Length: ${Array.isArray(data) ? data.length : 'N/A'}`);
      
      // Handle different possible response formats
      let messagesArray = [];
      if (Array.isArray(data)) {
        messagesArray = data;
      } else if (data && typeof data === 'object') {
        // Check if data has a history property (Smartlead API format)
        if (Array.isArray(data.history)) {
          messagesArray = data.history;
        } else if (Array.isArray(data.messages)) {
          messagesArray = data.messages;
        } else if (Array.isArray(data.data)) {
          messagesArray = data.data;
        } else if (Array.isArray(data.results)) {
          messagesArray = data.results;
        } else {
          // If it's an object but not an array, wrap it in an array
          messagesArray = [data];
        }
      }
      
      console.log(`Final messages array length: ${messagesArray.length}`);
      
      // Transform the messages to match our interface
      const transformedMessages = messagesArray.map((msg: any, index: number) => {
        // Determine status based on engagement
        let status = 'sent';
        if (msg.open_count > 0) {
          status = 'opened';
        }
        if (msg.click_count > 0) {
          status = 'clicked';
        }
        
        const transformedMessage = {
          id: msg.stats_id || msg.message_id || `msg-${index}`,
          type: msg.type === 'SENT' ? 'outbound' : 'inbound',
          subject: msg.subject,
          body: msg.email_body ? stripHtmlTags(msg.email_body) : undefined,
          html_body: msg.email_body,
          sent_at: msg.time,
          status: status,
          from: msg.from,
          to: msg.to,
          opens: msg.open_count,
          clicks: msg.click_count,
          reply: msg.type !== 'SENT', // Inbound messages are replies
          sequence_number: msg.email_seq_number ? parseInt(msg.email_seq_number) : undefined
        };
        
        return transformedMessage;
      });
      
      setMessages(transformedMessages);
    } catch (error) {
      console.error('Error fetching message history:', error);
      toast.error(`Failed to fetch message history: ${(error as Error).message}`);
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const getLeadDisplayName = (lead: Lead) => {
    const { first_name, last_name, email } = lead.lead;
    if (first_name || last_name) {
      return `${first_name || ''} ${last_name || ''}`.trim();
    }
    return email;
  };

  const getLeadEmail = (campaignLeadMapId: string) => {
    const lead = leads.find(l => l.campaign_lead_map_id === campaignLeadMapId);
    return lead?.lead?.email || 'Unknown Email';
  };

  const getCampaignName = (campaignId: string) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign ? campaign.name : 'Unknown Campaign';
  };

  const getMessageStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'opened':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'clicked':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'replied':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'bounced':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'sent':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'outbound':
        return <Mail className="h-4 w-4 text-blue-600" />;
      case 'inbound':
        return <Mail className="h-4 w-4 text-green-600" />;
      default:
        return <Mail className="h-4 w-4 text-gray-600" />;
    }
  };

  const stripHtmlTags = (html: string) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || "";
  };

  const getMessagePreview = (message: Message) => {
    if (message.html_body) {
      const textContent = stripHtmlTags(message.html_body);
      return textContent.length > 150 ? textContent.substring(0, 150) + '...' : textContent;
    } else if (message.body) {
      return message.body.length > 150 ? message.body.substring(0, 150) + '...' : message.body;
    }
    return 'No content available';
  };

  const filteredMessages = messages.filter(message => {
    if (messageTab === 'all') return true;
    if (messageTab === 'sent' && message.type === 'outbound') return true;
    if (messageTab === 'received' && message.type === 'inbound') return true;
    return false;
  });



  const selectedLead = leads.find(l => l.campaign_lead_map_id === selectedLeadId);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Message History
            </CardTitle>
            <div className="flex space-x-2">
              <Select 
                value={selectedCampaignId} 
                onValueChange={setSelectedCampaignId}
                disabled={loadingCampaigns}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder={loadingCampaigns ? "Loading campaigns..." : "Select a campaign"} />
                </SelectTrigger>
                <SelectContent>
                  {campaigns.map((campaign) => (
                    <SelectItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select 
                value={selectedLeadId} 
                onValueChange={setSelectedLeadId}
                disabled={loadingLeads || leads.length === 0 || !selectedCampaignId}
              >
                <SelectTrigger className="w-[250px]">
                  <SelectValue placeholder={
                    !selectedCampaignId ? "Select campaign first" :
                    loadingLeads ? "Loading leads..." : 
                    leads.length === 0 ? "No leads available" : 
                    "Select a lead"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {leads.map((lead) => (
                    <SelectItem key={lead.campaign_lead_map_id} value={lead.campaign_lead_map_id}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>{getLeadDisplayName(lead)}</span>
                        {lead.contact_info && (
                          <Badge variant="secondary" className="text-xs">DB</Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => {
                  if (selectedCampaignId && selectedLeadId) {
                    fetchMessageHistory(selectedCampaignId, selectedLeadId);
                  }
                }}
                disabled={loading || !selectedCampaignId || !selectedLeadId}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
              

            </div>
          </div>
          
          {/* Lead Information */}
          {selectedLead && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Contact:</span>
                  <p className="text-gray-900">{getLeadDisplayName(selectedLead)}</p>
                  <p className="text-gray-600">{selectedLead.lead.email}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Company:</span>
                  <p className="text-gray-900">{selectedLead.lead.company_name || 'N/A'}</p>
                  {selectedLead.lead.location && (
                    <p className="text-gray-600">{selectedLead.lead.location}</p>
                  )}
                </div>
                <div>
                  <span className="font-medium text-gray-600">Status:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getMessageStatusColor(selectedLead.status)}>
                      {selectedLead.status}
                    </Badge>
                    {selectedLead.contact_info && (
                      <Badge variant="secondary">
                        Connected to DB
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {selectedCampaignId && selectedLeadId ? (
            <>

              
              <div className="mb-4">
                <Tabs defaultValue="all" value={messageTab} onValueChange={setMessageTab} className="w-full">
                  <TabsList className="grid grid-cols-3 w-[300px]">
                    <TabsTrigger value="all">All Messages ({filteredMessages.length})</TabsTrigger>
                    <TabsTrigger value="sent">Sent ({messages.filter(m => m.type === 'outbound').length})</TabsTrigger>
                    <TabsTrigger value="received">Received ({messages.filter(m => m.type === 'inbound').length})</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            
              <ScrollArea className="h-[600px] rounded-md border">
                {loading ? (
                  <div className="flex items-center justify-center h-40">
                    <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-600">Loading messages...</span>
                  </div>
                ) : (messages.length === 0 || filteredMessages.length === 0) ? (
                  <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                    <Mail className="h-12 w-12 text-gray-300 mb-2" />
                    <p className="text-lg font-medium">No messages found</p>
                    <p className="text-sm">No message history available for this lead</p>

                  </div>
                ) : (
                  <div className="space-y-4 p-4">
                    {filteredMessages.map((message, index) => (
                      <div 
                        key={message.id || index} 
                        className={`p-4 rounded-lg border transition-all hover:shadow-md ${
                          message.type === 'outbound' 
                            ? 'bg-blue-50 border-blue-200 ml-8' 
                            : 'bg-green-50 border-green-200 mr-8'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            {getMessageTypeIcon(message.type)}
                            <div>
                              <p className="text-sm font-semibold text-gray-900">
                                {message.type === 'outbound' ? 'Sent to:' : 'Received from:'} 
                                <span className="ml-1 text-blue-600">
                                  {message.type === 'outbound' ? message.to : message.from}
                                </span>
                              </p>
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <Calendar className="h-3 w-3" />
                                {formatDate(message.sent_at)}
                                {message.sequence_number && (
                                  <>
                                    <span>•</span>
                                    <span>Sequence #{message.sequence_number}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {message.status && (
                              <Badge className={getMessageStatusColor(message.status)}>
                                {message.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        {message.subject && (
                          <div className="mb-3">
                            <p className="text-sm font-medium text-gray-700">
                              <strong>Subject:</strong> {message.subject}
                            </p>
                          </div>
                        )}
                        
                        <div className="mb-3">
                          <div className="text-sm text-gray-700 bg-white p-3 rounded border">
                            {message.html_body ? (
                              <div 
                                dangerouslySetInnerHTML={{ __html: message.html_body }} 
                                className="prose prose-sm max-w-none"
                              />
                            ) : message.body ? (
                              <p className="whitespace-pre-wrap">{message.body}</p>
                            ) : (
                              <p className="italic text-gray-500">No message content</p>
                            )}
                          </div>
                        </div>
                        
                        {/* Engagement metrics */}
                        {(message.opens !== undefined || message.clicks !== undefined) && (
                          <div className="flex items-center gap-4 text-xs text-gray-600 bg-white p-2 rounded border">
                            {message.opens !== undefined && (
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>Opens: {message.opens}</span>
                              </div>
                            )}
                            {message.clicks !== undefined && (
                              <div className="flex items-center gap-1">
                                <MousePointer className="h-3 w-3" />
                                <span>Clicks: {message.clicks}</span>
                              </div>
                            )}
                            {message.reply && (
                              <Badge variant="outline" className="text-xs">
                                Reply
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <Mail className="h-12 w-12 text-gray-300 mb-2" />
              <p className="text-lg font-medium">Select Campaign and Lead</p>
              <p className="text-sm">Please select both a campaign and a lead to view message history</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MessageHistoryTab; 