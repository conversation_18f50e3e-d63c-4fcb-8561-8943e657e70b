import { NextResponse } from 'next/server';
import { pool } from '../../../../lib/db';
import { PoolClient } from 'pg';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const filters = {
    source: searchParams.get('source') || 'all'
  };

  let client: PoolClient | undefined;
  try {
    // Set a connection timeout
    const connectionPromise = pool.connect();
    const timeoutPromise = new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('Connection timeout')), 10000)
    );
    
    client = await Promise.race([connectionPromise, timeoutPromise]) as PoolClient;

    // Build WHERE clauses based on filters
    const buildContactFilters = () => {
      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      // Source filter
      if (filters.source !== 'all') {
        conditions.push(`c.source = $${paramIndex}`);
        params.push(filters.source);
        paramIndex++;
      }

      return {
        whereClause: conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '',
        params
      };
    };

    const buildCompanyFilters = () => {
      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      // Apply source filter directly to companies
      if (filters.source !== 'all') {
        conditions.push(`c.source = $${paramIndex}`);
        params.push(filters.source);
        paramIndex++;
      }

      return {
        whereClause: conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '',
        params
      };
    };

    // Get contact processing stats using status columns
    const contactFilters = buildContactFilters();
    
    const contactStatsQuery = `
      SELECT 
        -- Total contacts
        COUNT(*) as total_contacts,
        
        -- Email Verification Stats (only contacts with email addresses)
        COUNT(CASE WHEN c.email IS NOT NULL AND c.email != '' AND c.email != ' ' THEN 1 END) as email_verification_total,
        COUNT(CASE WHEN c.email IS NOT NULL AND c.email != '' AND c.email != ' ' AND COALESCE(c.email_verification_status, 'pending') = 'pending' THEN 1 END) as email_verification_pending,
        COUNT(CASE WHEN c.email_verification_status = 'running' THEN 1 END) as email_verification_running,
        COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as email_verification_completed,
        COUNT(CASE WHEN c.email_verification_status = 'failed' THEN 1 END) as email_verification_failed,
        COUNT(CASE WHEN c.email_verification_status = 'error' THEN 1 END) as email_verification_error,
        
        -- OSINT Research Stats (eligible: verified emails)
        COUNT(CASE WHEN c.email_verification_status = 'completed' THEN 1 END) as osint_research_total,
        COUNT(CASE WHEN c.email_verification_status = 'completed' AND COALESCE(c.osint_status, 'pending') = 'pending' THEN 1 END) as osint_research_pending,
        COUNT(CASE WHEN c.osint_status = 'running' THEN 1 END) as osint_research_running,
        COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as osint_research_completed,
        COUNT(CASE WHEN c.osint_status = 'failed' THEN 1 END) as osint_research_failed,
        COUNT(CASE WHEN c.osint_status = 'error' THEN 1 END) as osint_research_error,
        
        -- Overview Extraction Stats (eligible: OSint data Extracted  contacts)
        COUNT(CASE WHEN c.osint_status = 'completed' THEN 1 END) as overview_extraction_total,
        COUNT(CASE WHEN c.osint_status = 'completed' AND COALESCE(c.overview_extraction_status, 'pending') = 'pending' THEN 1 END) as overview_extraction_pending,
        COUNT(CASE WHEN c.overview_extraction_status = 'running' THEN 1 END) as overview_extraction_running,
        COUNT(CASE WHEN c.overview_extraction_status = 'completed' THEN 1 END) as overview_extraction_completed,
        COUNT(CASE WHEN c.overview_extraction_status = 'failed' THEN 1 END) as overview_extraction_failed,
        COUNT(CASE WHEN c.overview_extraction_status = 'error' THEN 1 END) as overview_extraction_error,
        
        -- Classification Stats (eligible: overview_extraction_status = completed)
        COUNT(CASE WHEN c.overview_extraction_status = 'completed' THEN 1 END) as classification_total,
        COUNT(CASE WHEN c.overview_extraction_status = 'completed' AND COALESCE(c.classification_status, 'pending') = 'pending' THEN 1 END) as classification_pending,
        COUNT(CASE WHEN c.classification_status = 'running' THEN 1 END) as classification_running,
        COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as classification_completed,
        COUNT(CASE WHEN c.classification_status = 'failed' THEN 1 END) as classification_failed,
        COUNT(CASE WHEN c.classification_status = 'error' THEN 1 END) as classification_error,
        
        -- Email Generation Stats (eligible: classification completed)
        COUNT(CASE WHEN c.classification_status = 'completed' THEN 1 END) as email_generation_total,
        COUNT(CASE WHEN c.classification_status = 'completed' AND COALESCE(c.email_generation_status, 'pending') = 'pending' THEN 1 END) as email_generation_pending,
        COUNT(CASE WHEN c.email_generation_status = 'running' THEN 1 END) as email_generation_running,
        COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as email_generation_completed,
        COUNT(CASE WHEN c.email_generation_status = 'failed' THEN 1 END) as email_generation_failed,
        COUNT(CASE WHEN c.email_generation_status = 'error' THEN 1 END) as email_generation_error,
        
        -- Email Sending Stats (eligible: email generation completed)
        COUNT(CASE WHEN c.email_generation_status = 'completed' THEN 1 END) as email_sending_total,
        COUNT(CASE WHEN c.email_generation_status = 'completed' AND COALESCE(c.email_sending_status, 'pending') = 'pending' THEN 1 END) as email_sending_pending,
        COUNT(CASE WHEN c.email_sending_status = 'running' THEN 1 END) as email_sending_running,
        COUNT(CASE WHEN c.email_sending_status = 'completed' THEN 1 END) as email_sending_completed,
        COUNT(CASE WHEN c.email_sending_status = 'failed' THEN 1 END) as email_sending_failed,
        COUNT(CASE WHEN c.email_sending_status = 'error' THEN 1 END) as email_sending_error
      FROM contacts c
      ${contactFilters.whereClause}
    `;
    
    const contactStatsResult = await client.query(contactStatsQuery, contactFilters.params);
    
    const contactRow = contactStatsResult.rows[0] || {};
    const contacts = {
      email_verification: {
        total: parseInt(contactRow.email_verification_total || 0),
        pending: parseInt(contactRow.email_verification_pending || 0),
        running: parseInt(contactRow.email_verification_running || 0),
        completed: parseInt(contactRow.email_verification_completed || 0),
        failed: parseInt(contactRow.email_verification_failed || 0),
        error: parseInt(contactRow.email_verification_error || 0)
      },
      osint_research: {
        total: parseInt(contactRow.osint_research_total || 0),
        pending: parseInt(contactRow.osint_research_pending || 0),
        running: parseInt(contactRow.osint_research_running || 0),
        completed: parseInt(contactRow.osint_research_completed || 0),
        failed: parseInt(contactRow.osint_research_failed || 0),
        error: parseInt(contactRow.osint_research_error || 0)
      },
      overview_extraction: {
        total: parseInt(contactRow.overview_extraction_total || 0),
        pending: parseInt(contactRow.overview_extraction_pending || 0),
        running: parseInt(contactRow.overview_extraction_running || 0),
        completed: parseInt(contactRow.overview_extraction_completed || 0),
        failed: parseInt(contactRow.overview_extraction_failed || 0),
        error: parseInt(contactRow.overview_extraction_error || 0)
      },
      classification: {
        total: parseInt(contactRow.classification_total || 0),
        pending: parseInt(contactRow.classification_pending || 0),
        running: parseInt(contactRow.classification_running || 0),
        completed: parseInt(contactRow.classification_completed || 0),
        failed: parseInt(contactRow.classification_failed || 0),
        error: parseInt(contactRow.classification_error || 0)
      },
      email_generation: {
        total: parseInt(contactRow.email_generation_total || 0),
        pending: parseInt(contactRow.email_generation_pending || 0),
        running: parseInt(contactRow.email_generation_running || 0),
        completed: parseInt(contactRow.email_generation_completed || 0),
        failed: parseInt(contactRow.email_generation_failed || 0),
        error: parseInt(contactRow.email_generation_error || 0)
      },
      email_sending: {
        total: parseInt(contactRow.email_sending_total || 0),
        pending: parseInt(contactRow.email_sending_pending || 0),
        running: parseInt(contactRow.email_sending_running || 0),
        completed: parseInt(contactRow.email_sending_completed || 0),
        failed: parseInt(contactRow.email_sending_failed || 0),
        error: parseInt(contactRow.email_sending_error || 0)
      }
    };

    // Get company processing stats using status columns
    const companyFilters = buildCompanyFilters();
    
    const companyStatsQuery = `
      SELECT 
        -- Total companies
        COUNT(*) as total_companies,
        
        -- Website Scraping Stats (eligible: companies with websites)
        COUNT(CASE WHEN c.company_website IS NOT NULL AND c.company_website != '' AND c.company_website != ' ' THEN 1 END) as website_scraping_total,
        COUNT(CASE WHEN c.company_website IS NOT NULL AND c.company_website != '' AND c.company_website != ' ' AND COALESCE(c.website_scraping_status, 'pending') = 'pending' THEN 1 END) as website_scraping_pending,
        COUNT(CASE WHEN c.website_scraping_status = 'running' THEN 1 END) as website_scraping_running,
        COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as website_scraping_completed,
        COUNT(CASE WHEN c.website_scraping_status = 'failed' THEN 1 END) as website_scraping_failed,
        COUNT(CASE WHEN c.website_scraping_status = 'error' THEN 1 END) as website_scraping_error,
        
        -- Company Overview Stats (eligible: website scraping completed)
        COUNT(CASE WHEN c.website_scraping_status = 'completed' THEN 1 END) as company_overview_total,
        COUNT(CASE WHEN c.website_scraping_status = 'completed' AND COALESCE(c.company_overview_status, 'pending') = 'pending' THEN 1 END) as company_overview_pending,
        COUNT(CASE WHEN c.company_overview_status = 'running' THEN 1 END) as company_overview_running,
        COUNT(CASE WHEN c.company_overview_status = 'completed' THEN 1 END) as company_overview_completed,
        COUNT(CASE WHEN c.company_overview_status = 'failed' THEN 1 END) as company_overview_failed,
        COUNT(CASE WHEN c.company_overview_status = 'error' THEN 1 END) as company_overview_error
      FROM companies c
      ${companyFilters.whereClause}
    `;
      
    const companyStatsResult = await client.query(companyStatsQuery, companyFilters.params);
    
    const companyRow = companyStatsResult.rows[0] || {};
    const companies = {
      website_scraping: {
        total: parseInt(companyRow.website_scraping_total || 0),
        pending: parseInt(companyRow.website_scraping_pending || 0),
        running: parseInt(companyRow.website_scraping_running || 0),
        completed: parseInt(companyRow.website_scraping_completed || 0),
        failed: parseInt(companyRow.website_scraping_failed || 0),
        error: parseInt(companyRow.website_scraping_error || 0)
      },
      company_overview: {
        total: parseInt(companyRow.company_overview_total || 0),
        pending: parseInt(companyRow.company_overview_pending || 0),
        running: parseInt(companyRow.company_overview_running || 0),
        completed: parseInt(companyRow.company_overview_completed || 0),
        failed: parseInt(companyRow.company_overview_failed || 0),
        error: parseInt(companyRow.company_overview_error || 0)
      }
    };

    // Get recent errors - build proper WHERE clauses
    const contactErrorsWhereClause = contactFilters.whereClause 
      ? `${contactFilters.whereClause} AND (
        email_verification_error IS NOT NULL OR
        osint_error IS NOT NULL OR
        overview_extraction_error IS NOT NULL OR
        classification_error IS NOT NULL OR
        email_generation_error IS NOT NULL OR
        email_sending_error IS NOT NULL
      )`
      : `WHERE (
        email_verification_error IS NOT NULL OR
        osint_error IS NOT NULL OR
        overview_extraction_error IS NOT NULL OR
        classification_error IS NOT NULL OR
        email_generation_error IS NOT NULL OR
        email_sending_error IS NOT NULL
      )`;

    const companyErrorsWhereClause = companyFilters.whereClause 
      ? `${companyFilters.whereClause} AND (
        website_scraping_error IS NOT NULL OR
        company_overview_error IS NOT NULL
      )`
      : `WHERE (
        website_scraping_error IS NOT NULL OR
        company_overview_error IS NOT NULL
      )`;

    const contactErrorsQuery = `
      SELECT 
        'contact' as entity_type,
        contact_id as entity_id,
        CASE 
          WHEN email_verification_error IS NOT NULL THEN 'email_validation'
          WHEN osint_error IS NOT NULL THEN 'contact_search'
          WHEN overview_extraction_error IS NOT NULL THEN 'contact_overview'
          WHEN classification_error IS NOT NULL THEN 'contact_classification'
          WHEN email_generation_error IS NOT NULL THEN 'email_generation'
          WHEN email_sending_error IS NOT NULL THEN 'email_generation'
        END as stage,
        CASE 
          WHEN email_verification_error IS NOT NULL THEN email_verification_error
          WHEN osint_error IS NOT NULL THEN osint_error
          WHEN overview_extraction_error IS NOT NULL THEN overview_extraction_error
          WHEN classification_error IS NOT NULL THEN classification_error
          WHEN email_generation_error IS NOT NULL THEN email_generation_error
          WHEN email_sending_error IS NOT NULL THEN email_sending_error
        END as error_message,
        updated_at as occurred_at,
        processing_error_count as retry_count
      FROM contacts c
      ${contactErrorsWhereClause}
      ORDER BY updated_at DESC
      LIMIT 50
    `;

    const companyErrorsQuery = `
      SELECT 
        'company' as entity_type,
        company_id as entity_id,
        CASE 
          WHEN website_scraping_error IS NOT NULL THEN 'company_web_crawler'
          WHEN company_overview_error IS NOT NULL THEN 'company_overview'
        END as stage,
        CASE 
          WHEN website_scraping_error IS NOT NULL THEN website_scraping_error
          WHEN company_overview_error IS NOT NULL THEN company_overview_error
        END as error_message,
        updated_at as occurred_at,
        processing_error_count as retry_count
      FROM companies c
      ${companyErrorsWhereClause}
      ORDER BY updated_at DESC
      LIMIT 50
    `;

    const [contactErrorsResult, companyErrorsResult] = await Promise.all([
      client.query(contactErrorsQuery, contactFilters.params),
      client.query(companyErrorsQuery, companyFilters.params)
    ]);

    // Generate metadata
    const getQueryInfo = () => {
      const details = [];
      
      if (filters.source !== 'all') {
        details.push(`Filtered by source: ${filters.source}`);
      }
      
      return details.length > 0 ? details.join(' | ') : 'No filters applied';
    };
    
    // Add human-readable explanations for each metric
    const explanations = {
      contacts: {
        email_verification: "Email Verification Stage (eligible: contacts with email addresses)",
        osint_research: "OSINT Research Stage (eligible: contacts with verified emails)",
        overview_extraction: "Overview Extraction Stage (eligible: contacts with verified emails)",
        classification: "Classification Stage (eligible: contacts with verified emails)",
        email_generation: "Email Generation Stage (eligible: contacts with completed classification)",
        email_sending: "Email Sending Stage (eligible: contacts with generated emails)"
      },
      companies: {
        website_scraping: "Website Scraping Stage (eligible: companies with websites)",
        company_overview: "Company Overview Stage (eligible: companies with scraped websites)"
      }
    };
    
    const metadata = {
      statsSource: 'PostgreSQL Status Columns',
      lastUpdated: new Date().toISOString(),
      queryInfo: getQueryInfo(),
      explanations
    };

    return NextResponse.json({
      success: true,
      data: {
        contacts,
        companies,
        totals: {
          total_contacts: parseInt(contactRow.total_contacts || 0),
          total_companies: parseInt(companyRow.total_companies || 0)
        },
        errors: {
          contact_errors: contactErrorsResult.rows || [],
          company_errors: companyErrorsResult.rows || []
        },
        metadata
      }
    });

  } catch (error) {
    console.error('Processing stats error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
} 