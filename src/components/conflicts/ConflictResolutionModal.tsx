'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, XCircle, AlertTriangle, FileText, User, Building2 } from 'lucide-react'
import { ConflictRecord, ConflictResolution, FieldConflict } from '@/types/conflict'

interface ConflictResolutionModalProps {
  isOpen: boolean
  onClose: () => void
  conflict: ConflictRecord | null
  onResolve: (resolutions: ConflictResolution[]) => Promise<void>
}

interface FieldResolution {
  field_name: string
  resolution: 'keep_existing' | 'use_new' | 'manual'
  manual_value?: string
}

export default function ConflictResolutionModal({
  isOpen,
  onClose,
  conflict,
  onResolve
}: ConflictResolutionModalProps) {
  const [resolutions, setResolutions] = useState<Record<string, FieldResolution>>({})
  const [loading, setLoading] = useState(false)

  const handleResolutionChange = (fieldName: string, resolution: FieldResolution['resolution']) => {
    setResolutions(prev => ({
      ...prev,
      [fieldName]: {
        field_name: fieldName,
        resolution,
        manual_value: prev[fieldName]?.manual_value || ''
      }
    }))
  }

  const handleManualValueChange = (fieldName: string, value: string) => {
    setResolutions(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        manual_value: value
      }
    }))
  }

  const handleResolveAll = async () => {
    if (!conflict) return

    setLoading(true)
    try {
      const conflictResolutions: ConflictResolution[] = Object.values(resolutions).map(res => ({
        record_id: conflict.id,
        record_type: conflict.type,
        field_name: res.field_name,
        resolution: res.resolution,
        manual_value: res.manual_value
      }))

      await onResolve(conflictResolutions)
      onClose()
      setResolutions({})
    } catch (error) {
      console.error('Error resolving conflicts:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
  }

  const getFieldIcon = (fieldName: string) => {
    if (fieldName.includes('email')) return <User className="h-4 w-4" />
    if (fieldName.includes('company') || fieldName.includes('industry')) return <Building2 className="h-4 w-4" />
    return <FileText className="h-4 w-4" />
  }

  const canResolve = conflict && Object.keys(conflict.conflicts).every(
    fieldName => resolutions[fieldName]?.resolution
  )

  if (!conflict) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {conflict.type === 'company' ? <Building2 className="h-5 w-5" /> : <User className="h-5 w-5" />}
            Resolve Conflicts for {conflict.name}
          </DialogTitle>
          <DialogDescription>
            Choose how to resolve each conflicting field. You can keep the existing value, use the new value, or enter a custom value.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <div>
              <p className="font-medium text-yellow-800">
                {Object.keys(conflict.conflicts).length} conflicts found
              </p>
              <div className="flex items-center gap-2 text-sm text-yellow-700 flex-wrap">
                <span>Source: {conflict.conflict_source}</span>
                <span>•</span>
                <span>Created: {new Date(conflict.conflict_created_at || '').toLocaleDateString()}</span>
                {/* Show contact match information */}
                {conflict.type === 'contact' && (conflict as any).match_reason && (
                  <>
                    <span>•</span>
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                      Contact matched by {(conflict as any).match_reason}
                    </Badge>
                  </>
                )}
                {conflict.type === 'contact' && (conflict as any).email && (
                  <>
                    <span>•</span>
                    <span className="text-xs">Email: {(conflict as any).email}</span>
                  </>
                )}
                {conflict.type === 'contact' && (conflict as any).linkedin_url && (
                  <>
                    <span>•</span>
                    <span className="text-xs">LinkedIn: {(conflict as any).linkedin_url}</span>
                  </>
                )}
                
                {/* Show company match information */}
                {conflict.type === 'company' && (conflict as any).match_reason && (
                  <>
                    <span>•</span>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                      Company matched by {(conflict as any).match_reason}
                    </Badge>
                  </>
                )}
                {conflict.type === 'company' && (conflict as any).company_website && (
                  <>
                    <span>•</span>
                    <span className="text-xs">Website: {(conflict as any).company_website}</span>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {Object.entries(conflict.conflicts).map(([fieldName, fieldConflict]: [string, FieldConflict]) => (
              <Card key={fieldName} className="border-l-4 border-l-orange-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {getFieldIcon(fieldName)}
                    {formatFieldName(fieldName)}
                    {/* Show match condition badge for all conflict types */}
                    {fieldConflict.match_reason && (
                      <Badge variant="secondary" className="text-xs bg-blue-50 text-blue-700 border-blue-200 ml-2">
                        Matched by {fieldConflict.match_reason}
                      </Badge>
                    )}
                  </CardTitle>
                  {/* Show conflict source and timing */}
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Badge variant="outline" className="text-xs">
                      {fieldConflict.field_type}
                    </Badge>
                    <span>•</span>
                    <span>Source: {fieldConflict.source}</span>
                    <span>•</span>
                    <span>Created: {new Date(fieldConflict.created_at).toLocaleString()}</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Show conflict detection info */}
                  <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-gray-600" />
                      <span className="font-medium text-gray-800">Conflict Detection Details</span>
                    </div>
                    <div className="text-sm text-gray-700 space-y-1">
                      <p><strong>Field Type:</strong> {fieldConflict.field_type}</p>
                      <p><strong>Detection Method:</strong> {
                        fieldConflict.match_reason ? `Matched by ${fieldConflict.match_reason}` : 
                        fieldName.includes('email') ? 'Email address comparison' :
                        fieldName.includes('linkedin') ? 'LinkedIn URL comparison' :
                        fieldName.includes('name') ? 'Name similarity matching' :
                        fieldName.includes('phone') ? 'Phone number comparison' :
                        'Field value comparison'
                      }</p>
                      <p><strong>Conflict Reason:</strong> {
                        fieldConflict.field_type === 'company_selection' ? 'Multiple companies found matching the same criteria' :
                        'Existing record found with different value for this field'
                      }</p>
                    </div>
                  </div>

                  {/* Show match condition info for company selection conflicts */}
                  {fieldConflict.field_type === 'company_selection' && Array.isArray(fieldConflict.existing_value) && (
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-800">Multiple Company Matches Found</span>
                      </div>
                      <p className="text-sm text-blue-700 mb-3">
                        Multiple companies were found that could match your data. Please select which company to use:
                      </p>
                      <div className="space-y-2">
                        {fieldConflict.existing_value.map((match: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                            <div>
                              <p className="font-medium text-gray-900">{match.company_name}</p>
                              <p className="text-sm text-gray-600">{match.company_website}</p>
                            </div>
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                              Matched by {match.match_reason}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <RadioGroup
                    value={resolutions[fieldName]?.resolution || ''}
                    onValueChange={(value) => handleResolutionChange(fieldName, value as FieldResolution['resolution'])}
                  >
                    {/* Keep Existing Value */}
                    <div className="flex items-start space-x-3 p-3 rounded-lg border border-green-200 bg-green-50">
                      <RadioGroupItem value="keep_existing" id={`${fieldName}-existing`} className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor={`${fieldName}-existing`} className="text-green-800 font-medium cursor-pointer">
                          {fieldConflict.field_type === 'company_selection' ? 'Select from Existing Companies' : 'Keep Existing Value'}
                        </Label>
                        <div className="mt-1 p-2 bg-white rounded border">
                          {fieldConflict.field_type === 'company_selection' && Array.isArray(fieldConflict.existing_value) ? (
                            <div className="space-y-1">
                              {fieldConflict.existing_value.map((match: any, index: number) => (
                                <div key={index} className="text-sm text-gray-900 flex items-center justify-between">
                                  <span>{match.company_name} ({match.company_website})</span>
                                  <Badge variant="outline" className="text-xs">
                                    {match.match_reason}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="space-y-1">
                              <p className="text-sm text-gray-900">
                                {fieldConflict.existing_value || <span className="text-gray-400 italic">Empty</span>}
                              </p>
                              {fieldConflict.match_reason && (
                                <div className="flex items-center gap-1">
                                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                                    Found by {fieldConflict.match_reason} match
                                  </Badge>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <CheckCircle className="h-5 w-5 text-green-600 mt-1" />
                    </div>

                    {/* Use New Value */}
                    <div className="flex items-start space-x-3 p-3 rounded-lg border border-blue-200 bg-blue-50">
                      <RadioGroupItem value="use_new" id={`${fieldName}-new`} className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor={`${fieldName}-new`} className="text-blue-800 font-medium cursor-pointer">
                          {fieldConflict.field_type === 'company_selection' ? 'Create New Company' : 'Use New Value'}
                        </Label>
                        <div className="mt-1 p-2 bg-white rounded border">
                          {fieldConflict.field_type === 'company_selection' && typeof fieldConflict.new_value === 'object' && fieldConflict.new_value ? (
                            <div className="text-sm text-gray-900">
                              <p className="font-medium">{fieldConflict.new_value.company_name}</p>
                              <p className="text-gray-600">{fieldConflict.new_value.company_website}</p>
                            </div>
                          ) : (
                            <p className="text-sm text-gray-900">
                              {fieldConflict.new_value || <span className="text-gray-400 italic">Empty</span>}
                            </p>
                          )}
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-blue-700 bg-blue-200">
                        New
                      </Badge>
                    </div>

                    {/* Manual Entry */}
                    <div className="flex items-start space-x-3 p-3 rounded-lg border border-purple-200 bg-purple-50">
                      <RadioGroupItem value="manual" id={`${fieldName}-manual`} className="mt-1" />
                      <div className="flex-1">
                        <Label htmlFor={`${fieldName}-manual`} className="text-purple-800 font-medium cursor-pointer">
                          Enter Custom Value
                        </Label>
                        <Input
                          className="mt-2"
                          placeholder="Enter your custom value..."
                          value={resolutions[fieldName]?.manual_value || ''}
                          onChange={(e) => handleManualValueChange(fieldName, e.target.value)}
                          disabled={resolutions[fieldName]?.resolution !== 'manual'}
                        />
                      </div>
                      <FileText className="h-5 w-5 text-purple-600 mt-1" />
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="border-t border-gray-200" />

          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {Object.keys(resolutions).length} of {Object.keys(conflict.conflicts).length} conflicts resolved
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose} disabled={loading}>
                Cancel
              </Button>
              <Button 
                onClick={handleResolveAll} 
                disabled={!canResolve || loading}
                className="bg-green-600 hover:bg-green-700"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Resolving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Resolve All Conflicts
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 