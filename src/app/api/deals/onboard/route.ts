// Ensure Node.js runtime so we can use fs and path
export const runtime = 'nodejs'
import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const contactId = formData.get('contactId')?.toString() || ''
    const notes = formData.get('notes')?.toString() || ''
    const attachments = formData.getAll('attachments') as File[]

    // Load prompt from markdown
    const promptFile = path.join(process.cwd(), 'src/app/api/deals/onboard/prompt.md')
    const prompt = fs.readFileSync(promptFile, 'utf8')

    // Build messages payload
    const attachmentNames = attachments.map((file) => file.name).join(', ')
    const messages = [
      { role: 'system', content: prompt },
      { role: 'user', content: `Contact ID: ${contactId}\nNotes: ${notes}\nAttachments: ${attachmentNames}` }
    ]

    // Call OpenAI
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY not set')
    }
    const res = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({ model: 'gpt-4o', messages })
    })
    if (!res.ok) {
      const err = await res.text()
      throw new Error(`OpenAI error: ${err}`)
    }
    const json = await res.json()
    const content = json.choices?.[0]?.message?.content ?? ''
    return NextResponse.json({ output: content })
  } catch (error: any) {
    console.error('Error in /api/deals/onboard:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}