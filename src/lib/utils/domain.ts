/**
 * Extract domain from a URL string
 * @param url - The URL to extract domain from
 * @returns The extracted domain or empty string if invalid
 */
export function extractDomain(url: string): string {
  try {
    if (!url) return "";
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Split the hostname into parts
    const parts = hostname.split(".");

    // Handle cases where the hostname is an IP address or doesn't have enough parts
    if (parts.length < 2 || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
      return hostname;
    }

    const secondLevelTLDs = [
      "co",
      "gov",
      "org",
      "edu",
      "com",
      "net",
      "int",
      "mil",
      "arpa",
    ];

    // Check if the second to last part is a second-level domain (e.g., 'co' in 'co.uk')
    if (parts.length > 2 && secondLevelTLDs.includes(parts[parts.length - 2])) {
      // Return the last three parts (e.g., example.co.uk)
      return parts.slice(-3).join(".");
    }

    // Otherwise, return the last two parts as the domain (e.g., example.com)
    return parts.slice(-2).join(".");
  } catch (error) {
    console.error(`Invalid URL: ${error}`);
    return "";
  }
}

/**
 * Format website URL to include protocol
 * @param website - The website URL to format
 * @returns Formatted URL with https:// protocol
 */
export function formatWebsite(website: string): string {
  if (!website) return ''
  if (!website.startsWith('http://') && !website.startsWith('https://')) {
    return `https://${website}`
  }
  return website
}

// Example usage:
// console.log(extractDomain('sub.example.com'));         // Output: example.com
// console.log(extractDomain('https://www.example.co.uk'));       // Output: example.co.uk
// console.log(extractDomain('https://example.com'));             // Output: example.com
// console.log(extractDomain('https://***************'));         // Output: ***************
// console.log(extractDomain('https://subdomain.example.com.au')); // Output: example.com.au
// console.log(extractDomain('https://localhost:3000'));          // Output: localhost
// console.log(extractDomain('https://invalid-url'));             // Output: null
// console.log(extractDomain(''));                                // Output: null 