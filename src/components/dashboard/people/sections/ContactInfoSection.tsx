"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Tag,
  CalendarClock,
  Bookmark,
  Building2,
  <PERSON><PERSON><PERSON>,
  Link,
  ExternalLink
} from 'lucide-react';
import { Contact } from '../types';

interface ContactInfoSectionProps {
  contact: Contact;
}

const ContactInfoSection: React.FC<ContactInfoSectionProps> = ({ contact }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Contact Information */}
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Contact Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div className="flex items-start">
            <User className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
            <div>
              <span className="font-medium">{`${contact.first_name || ''} ${contact.last_name || ''}`}</span>
              {contact.job_title && (
                <div className="text-gray-500 mt-0.5">{contact.job_title}</div>
              )}
            </div>
          </div>
                
          <div className="flex items-start">
            <Mail className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
            <div>
              {contact.email ? (
                <span>{contact.email}</span>
              ) : (
                <span className="text-gray-500">No email available</span>
              )}
              {contact.email_status && (
                <div className="text-xs mt-0.5 inline-flex items-center">
                  <Badge variant="secondary" className={`text-xs ${contact.email_status === 'valid' ? 'bg-green-100 text-green-800' : ''}`}>
                    {contact.email_status}
                  </Badge>
                </div>
              )}
            </div>
          </div>
                
          <div className="flex items-start">
            <Phone className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
            <div>
              {contact.phone_number ? (
                <span>{contact.phone_number}</span>
              ) : (
                <span className="text-gray-500">No phone number available</span>
              )}
            </div>
          </div>
                
          <div className="flex items-start">
            <MapPin className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
            <div>
              {contact.company_city || contact.company_state || contact.company_country ? (
                <span>
                  {[
                    contact.company_city, 
                    contact.company_state, 
                    contact.company_country
                  ].filter(Boolean).join(', ')}
                </span>
              ) : (
                <span className="text-gray-500">No location available</span>
              )}
            </div>
          </div>
                
          {contact.source && (
            <div className="flex items-start">
              <Briefcase className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>Source: {contact.source}</span>
              </div>
            </div>
          )}
                
          {contact.capital_type && (
            <div className="flex items-start">
              <Tag className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>Capital Type: {contact.capital_type}</span>
              </div>
            </div>
          )}
                
          {contact.last_email_sent_at && (
            <div className="flex items-start">
              <CalendarClock className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>Last Contact: {new Date(contact.last_email_sent_at).toLocaleDateString()}</span>
              </div>
            </div>
          )}
                
          {contact.smartlead_status && (
            <div className="flex items-start">
              <Bookmark className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>Status: {contact.smartlead_status}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Investment Criteria */}
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Investment Criteria</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div>
            <div className="text-gray-500 mb-1">Asset Types</div>
            <div>
              {contact.investment_criteria?.asset_types?.length ? (
                <div className="flex flex-wrap gap-2">
                  {contact.investment_criteria.asset_types.map((type, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700">
                      {type}
                    </Badge>
                  ))}
                </div>
              ) : (
                <span className="text-gray-500">Not specified</span>
              )}
            </div>
          </div>
                
          <div>
            <div className="text-gray-500 mb-1">Deal Size</div>
            <div className="font-medium">
              {contact.investment_criteria?.deal_size?.min || contact.investment_criteria?.deal_size?.max ? (
                <>
                  {contact.investment_criteria.deal_size.min ? `$${contact.investment_criteria.deal_size.min.toLocaleString()}` : '$0'} - 
                  {contact.investment_criteria.deal_size.max ? ` $${contact.investment_criteria.deal_size.max.toLocaleString()}` : ' No maximum'}
                </>
              ) : (
                <span className="text-gray-500">Not specified</span>
              )}
            </div>
          </div>
                
          <div>
            <div className="text-gray-500 mb-1">Markets</div>
            <div>
              {contact.investment_criteria?.markets?.length ? (
                <span>{contact.investment_criteria.markets.join(', ')}</span>
              ) : (
                <span className="text-gray-500">Not specified</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
            
      {/* Company Information */}
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Company Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-sm">
          <div className="flex items-start">
            <Building2 className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
            <div>
              <span className="font-medium">{contact.company_name || 'Not specified'}</span>
            </div>
          </div>
                
          {contact.industry && (
            <div className="flex items-start">
              <BarChart className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>Industry: {contact.industry}</span>
              </div>
            </div>
          )}
                
          {contact.company_website && (
            <div className="flex items-start">
              <Link className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <a 
                  href={contact.company_website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline flex items-center"
                >
                  {contact.company_website}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            </div>
          )}
                
          {contact.company_address && (
            <div className="flex items-start">
              <MapPin className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
              <div>
                <span>{contact.company_address}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactInfoSection; 