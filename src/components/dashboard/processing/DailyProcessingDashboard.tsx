"use client"

import <PERSON><PERSON>, { useState, useEffect, use<PERSON><PERSON>back, useRef } from 'react'
import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { RefreshCw, Calendar, TrendingUp, BarChart3, AlertTriangle, CheckCircle, Clock, Zap, Activity } from 'lucide-react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Area,
  AreaChart,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar
} from 'recharts'

interface StageData {
  stage: string
  completed: number
  failed: number
  pending: number
  running: number
  total: number
  successRate: number
  name: string
  color: string
  order: number
}

interface TimeSeriesData {
  date: string
  completed: number
  failed: number
  pending: number
}

interface ProcessingSpeedMetric {
  stage: string
  avg_processing_time_seconds: number
  median_processing_time_seconds: number
  min_processing_time_seconds: number
  max_processing_time_seconds: number
  total_processed_today: number
  success_rate: number
  avg_time_formatted: string
  median_time_formatted: string
  efficiency_score: number
}

interface BottleneckAnalysis {
  stage: string
  bottleneck_score: number
  pending_count: number
  avg_wait_time_hours: number
  recommendation: string
}

interface EnhancedThroughput {
  contacts_today: number
  avg_per_hour: number
  peak_hour: number
  avg_hourly: number
  emails_verified_today: number
  osint_completed_today: number
  overview_completed_today: number
  classified_today: number
  emails_generated_today: number
  emails_sent_today: number
}

interface DashboardData {
  timeSeriesData: TimeSeriesData[]
  stagePerformance: StageData[]
  throughput: EnhancedThroughput
  processingSpeed: ProcessingSpeedMetric[]
  bottleneckAnalysis: BottleneckAnalysis[]
  metadata: {
    timePeriod: string
    lastUpdated: string
    realTimeEnabled: boolean
    analyticsConfig: any
  }
}

const DailyProcessingDashboard = () => {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(false)
  const [timePeriod, setTimePeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily')
  const [realTimeEnabled, setRealTimeEnabled] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Stage configuration with proper ordering and visual styling
  const stageConfig = [
    { key: 'email_verification', name: 'Email Verification', icon: '📧', color: '#3b82f6', order: 1 },
    { key: 'osint', name: 'OSINT Research', icon: '🔍', color: '#8b5cf6', order: 2 },
    { key: 'overview_extraction', name: 'Overview Extraction', icon: '📄', color: '#06b6d4', order: 3 },
    { key: 'classification', name: 'Classification', icon: '🏷️', color: '#10b981', order: 4 },
    { key: 'email_generation', name: 'Email Generation', icon: '✍️', color: '#f59e0b', order: 5 },
    { key: 'email_sending', name: 'Email Sending', icon: '📤', color: '#ef4444', order: 6 }
  ]

  // Auto-refresh functionality
  useEffect(() => {
    fetchData()

    if (realTimeEnabled) {
      intervalRef.current = setInterval(fetchData, 30000) // Refresh every 30 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [timePeriod, realTimeEnabled])

  const fetchData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/processing/analytics?period=${timePeriod}&realtime=${realTimeEnabled}`)
      const result = await response.json()

      if (result.success) {
        // Process stage performance data and sort by order
        const sortedStageData = result.data.stagePerformance
          .map((stage: any) => {
            const config = stageConfig.find(c => c.key === stage.stage)
            const completed = parseInt(stage.completed) || 0
            const failed = parseInt(stage.failed) || 0
            const pending = parseInt(stage.pending) || 0
            const running = parseInt(stage.running) || 0
            const total = completed + failed + pending + running
            const successRate = total > 0 ? (completed / total) * 100 : 0

            return {
              ...stage,
              completed,
              failed,
              pending,
              running,
              total,
              successRate,
              name: config?.name || stage.stage,
              color: config?.color || '#6b7280',
              order: config?.order || 999
            }
          })
          .sort((a: any, b: any) => a.order - b.order)

        setData({
          timeSeriesData: result.data.timeSeriesData || [],
          stagePerformance: sortedStageData,
          throughput: result.data.throughput || {
            contacts_today: 0,
            avg_per_hour: 0,
            peak_hour: 0,
            avg_hourly: 0,
            emails_verified_today: 0,
            osint_completed_today: 0,
            overview_completed_today: 0,
            classified_today: 0,
            emails_generated_today: 0,
            emails_sent_today: 0
          },
          processingSpeed: result.data.processingSpeed || [],
          bottleneckAnalysis: result.data.bottleneckAnalysis || [],
          metadata: result.data.metadata || {}
        })
        setLastUpdated(new Date())
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }, [timePeriod, realTimeEnabled, stageConfig])

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    if (timePeriod === 'daily') {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    } else if (timePeriod === 'weekly') {
      return `Week ${Math.ceil(date.getDate() / 7)}`
    } else {
      return date.toLocaleDateString('en-US', { month: 'short' })
    }
  }

  const getTotalsByStatus = () => {
    if (!data?.stagePerformance) return { completed: 0, failed: 0, pending: 0, running: 0 }

    return data.stagePerformance.reduce((acc, stage) => ({
      completed: acc.completed + stage.completed,
      failed: acc.failed + stage.failed,
      pending: acc.pending + stage.pending,
      running: acc.running + stage.running
    }), { completed: 0, failed: 0, pending: 0, running: 0 })
  }

  const getBottleneckSeverity = (score: number) => {
    if (score >= 90) return { level: 'critical', color: 'bg-red-500', text: 'Critical' }
    if (score >= 70) return { level: 'high', color: 'bg-orange-500', text: 'High' }
    if (score >= 50) return { level: 'medium', color: 'bg-yellow-500', text: 'Medium' }
    if (score >= 30) return { level: 'low', color: 'bg-blue-500', text: 'Low' }
    return { level: 'none', color: 'bg-green-500', text: 'None' }
  }

  const getEfficiencyColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const totals = getTotalsByStatus()

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Daily Processing Dashboard
                {loading && <RefreshCw className="h-4 w-4 animate-spin" />}
              </CardTitle>

              {/* Real-time indicator */}
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${realTimeEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
                <span className="text-sm text-muted-foreground">
                  {realTimeEnabled ? 'Live' : 'Static'}
                </span>
              </div>

              {lastUpdated && (
                <Badge variant="outline" className="text-xs">
                  Updated {lastUpdated.toLocaleTimeString()}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Real-time toggle */}
              <Button
                variant={realTimeEnabled ? 'default' : 'outline'}
                size="sm"
                onClick={() => setRealTimeEnabled(!realTimeEnabled)}
              >
                <Activity className="h-4 w-4 mr-1" />
                Real-time
              </Button>

              {/* Time Period Selector */}
              <div className="flex gap-2">
                <Button
                  variant={timePeriod === 'daily' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('daily')}
                >
                  Daily (7d)
                </Button>
                <Button
                  variant={timePeriod === 'weekly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('weekly')}
                >
                  Weekly (4w)
                </Button>
                <Button
                  variant={timePeriod === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimePeriod('monthly')}
                >
                  Monthly (12m)
                </Button>
              </div>

              <Button onClick={fetchData} size="sm" variant="outline" disabled={loading}>
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{totals.completed.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{totals.running.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Running</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{totals.pending.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{totals.failed.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </CardContent>
        </Card>
      </div>

      {/* Processing Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Processing Trends Over Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data?.timeSeriesData || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={formatDate}
                fontSize={12}
              />
              <YAxis fontSize={12} />
              <Tooltip 
                labelFormatter={(value) => formatDate(value as string)}
                formatter={(value: number, name: string) => [
                  value.toLocaleString(), 
                  name.charAt(0).toUpperCase() + name.slice(1)
                ]}
              />
              <Area 
                type="monotone" 
                dataKey="completed" 
                stackId="1" 
                stroke="#22c55e" 
                fill="#22c55e" 
                fillOpacity={0.8}
                name="completed" 
              />
              <Area 
                type="monotone" 
                dataKey="pending" 
                stackId="1" 
                stroke="#f59e0b" 
                fill="#f59e0b" 
                fillOpacity={0.8}
                name="pending" 
              />
              <Area 
                type="monotone" 
                dataKey="failed" 
                stackId="1" 
                stroke="#ef4444" 
                fill="#ef4444" 
                fillOpacity={0.8}
                name="failed" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Stage Processing Pipeline */}
      <Card>
        <CardHeader>
          <CardTitle>Processing Pipeline Status</CardTitle>
          <p className="text-sm text-muted-foreground">
            Current status of each processing stage showing the sequential workflow
          </p>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart 
              data={data?.stagePerformance || []} 
              layout="horizontal"
              margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" fontSize={12} />
              <YAxis 
                type="category" 
                dataKey="name"
                width={120}
                fontSize={12}
              />
              <Tooltip 
                formatter={(value: number, name: string) => [
                  value.toLocaleString(), 
                  name.charAt(0).toUpperCase() + name.slice(1)
                ]}
              />
              <Bar dataKey="completed" stackId="a" fill="#22c55e" name="completed" />
              <Bar dataKey="running" stackId="a" fill="#3b82f6" name="running" />
              <Bar dataKey="pending" stackId="a" fill="#f59e0b" name="pending" />
              <Bar dataKey="failed" stackId="a" fill="#ef4444" name="failed" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Stage Details Table */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Stage Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">Stage</th>
                  <th className="text-center p-3">Completed</th>
                  <th className="text-center p-3">Running</th>
                  <th className="text-center p-3">Pending</th>
                  <th className="text-center p-3">Failed</th>
                  <th className="text-center p-3">Total</th>
                  <th className="text-center p-3">Success Rate</th>
                </tr>
              </thead>
              <tbody>
                {data?.stagePerformance.map((stage, index) => {
                  const config = stageConfig.find(c => c.key === stage.stage)
                  return (
                    <tr key={stage.stage} className="border-b hover:bg-gray-50">
                      <td className="p-3">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{config?.icon}</div>
                          <div>
                            <div className="font-medium">{stage.name}</div>
                            <div className="text-xs text-gray-500">Stage {index + 1}</div>
                          </div>
                        </div>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                          {stage.completed.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                          {stage.running.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                          {stage.pending.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3">
                        <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                          {stage.failed.toLocaleString()}
                        </span>
                      </td>
                      <td className="text-center p-3 font-medium">{stage.total.toLocaleString()}</td>
                      <td className="text-center p-3">
                        <span className={`font-medium ${
                          stage.successRate >= 80 ? 'text-green-600' : 
                          stage.successRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {stage.successRate.toFixed(1)}%
                        </span>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Throughput Metrics */}
      {data?.throughput && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Today's Processing Throughput
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data.throughput.contacts_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Contacts Created</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data.throughput.emails_verified_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Emails Verified</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data.throughput.osint_completed_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">OSINT Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-600">
                  {data.throughput.classified_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Classified</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {data.throughput.emails_generated_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Emails Generated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {data.throughput.emails_sent_today.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Emails Sent</div>
              </div>
            </div>

            {/* Processing Rate Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">
                  {data.throughput.avg_per_hour.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Avg/Hour</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600">
                  {data.throughput.peak_hour.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Peak Hour</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {Math.round((data.throughput.emails_sent_today / Math.max(data.throughput.contacts_today, 1)) * 100)}%
                </div>
                <div className="text-sm text-muted-foreground">Completion Rate</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600">
                  {data.throughput.avg_hourly.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Avg Hourly</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processing Speed Analytics */}
      {data?.processingSpeed && data.processingSpeed.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Processing Speed Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3">Stage</th>
                    <th className="text-center p-3">Avg Time</th>
                    <th className="text-center p-3">Median Time</th>
                    <th className="text-center p-3">Processed Today</th>
                    <th className="text-center p-3">Success Rate</th>
                    <th className="text-center p-3">Efficiency Score</th>
                  </tr>
                </thead>
                <tbody>
                  {data.processingSpeed.map((speed, index) => {
                    const config = stageConfig.find(c => c.key === speed.stage)
                    return (
                      <tr key={speed.stage} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">{config?.icon}</div>
                            <div>
                              <div className="font-medium">{config?.name || speed.stage}</div>
                              <div className="text-xs text-gray-500">Stage {index + 1}</div>
                            </div>
                          </div>
                        </td>
                        <td className="text-center p-3 font-mono">
                          {speed.avg_time_formatted}
                        </td>
                        <td className="text-center p-3 font-mono">
                          {speed.median_time_formatted}
                        </td>
                        <td className="text-center p-3">
                          <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                            {speed.total_processed_today.toLocaleString()}
                          </span>
                        </td>
                        <td className="text-center p-3">
                          <span className={`font-medium ${
                            speed.success_rate >= 95 ? 'text-green-600' :
                            speed.success_rate >= 85 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {speed.success_rate.toFixed(1)}%
                          </span>
                        </td>
                        <td className="text-center p-3">
                          <span className={`font-bold ${getEfficiencyColor(speed.efficiency_score)}`}>
                            {speed.efficiency_score}/100
                          </span>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bottleneck Analysis */}
      {data?.bottleneckAnalysis && data.bottleneckAnalysis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Bottleneck Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.bottleneckAnalysis.map((bottleneck) => {
                const config = stageConfig.find(c => c.key === bottleneck.stage)
                const severity = getBottleneckSeverity(bottleneck.bottleneck_score)

                return (
                  <div key={bottleneck.stage} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{config?.icon}</div>
                        <div>
                          <h3 className="font-medium">{config?.name || bottleneck.stage}</h3>
                          <p className="text-sm text-gray-500">
                            {bottleneck.pending_count.toLocaleString()} pending •
                            Avg wait: {bottleneck.avg_wait_time_hours.toFixed(1)}h
                          </p>
                        </div>
                      </div>
                      <Badge className={`${severity.color} text-white`}>
                        {severity.text} ({bottleneck.bottleneck_score})
                      </Badge>
                    </div>

                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <strong>Recommendation:</strong> {bottleneck.recommendation}
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default DailyProcessingDashboard 