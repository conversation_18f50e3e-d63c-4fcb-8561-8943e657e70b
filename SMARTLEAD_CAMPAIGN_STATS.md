# Smartlead Campaign Statistics

This document describes the comprehensive campaign-wise statistics functionality integrated with the Smartlead API.

## Overview

The campaign statistics feature provides detailed analytics and performance metrics for individual Smartlead email campaigns, including:

- Campaign overview and configuration
- Lead status distribution
- Email engagement metrics (sent, delivered, opened, replied, bounced, unsubscribed)
- Performance rates (open rate, reply rate, bounce rate)
- Daily activity trends
- Email sequence information
- Recent activity logs

## API Endpoints

### Campaign Statistics
- **Endpoint**: `/api/smartlead/campaigns/[campaignId]/stats`
- **Method**: GET
- **Description**: Fetches comprehensive statistics for a specific campaign

### Campaign Sequences
- **Endpoint**: `/api/smartlead/campaigns/[campaignId]/sequences`
- **Method**: GET
- **Description**: Fetches email sequence configuration for a campaign

## Features

### 1. Campaign Selector
- Dropdown to select from available campaigns
- Automatic loading of the first campaign if none selected
- Refresh button to reload statistics

### 2. Campaign Overview
- Campaign name and status
- Total leads count
- Emails sent count
- Open rate percentage
- Reply rate percentage

### 3. Performance Metrics
- **Open Rate**: Percentage of emails opened (with trend indicator)
- **Reply Rate**: Percentage of emails that received replies (with trend indicator)
- **Bounce Rate**: Percentage of emails that bounced (with warning indicator)

### 4. Visual Analytics

#### Email Status Distribution (Pie Chart)
- Sent emails
- Delivered emails
- Opened emails
- Replied emails
- Bounced emails
- Unsubscribed contacts

#### Lead Status Distribution (Pie Chart)
- Started leads
- In-progress leads
- Completed leads
- Blocked leads

#### Daily Activity Chart (Area Chart)
- Email sending trends over the last 30 days
- Email opening trends
- Reply trends

### 5. Email Sequence Display
- Shows configured email templates in the campaign
- Subject lines and content previews
- Day delays between emails
- Sequence order

### 6. Campaign Configuration
- Max leads per day
- Minimum time between emails
- Follow-up percentage
- AI ESP matching status
- Plain text sending preference
- Stop lead settings

### 7. Recent Activity Table
- Latest email interactions
- Contact information
- Email status
- Last activity timestamps

## Data Sources

The statistics are compiled from multiple sources:

1. **Smartlead API**: Campaign details, leads, and sequences
2. **Local Database**: Email engagement tracking and contact information
3. **Combined Analytics**: Performance calculations and trend analysis

## API Integration

### Smartlead API Endpoints Used

Based on the [Smartlead API Documentation](https://helpcenter.smartlead.ai/en/articles/125-full-api-documentation):

1. **Get Campaign By Id**
   ```
   GET /campaigns/{campaign_id}?api_key={API_KEY}
   ```

2. **Fetch Campaign Leads**
   ```
   GET /campaigns/{campaign_id}/leads?api_key={API_KEY}&limit={limit}&offset={offset}
   ```

3. **Fetch Campaign Sequence**
   ```
   GET /campaigns/{campaign_id}/sequences?api_key={API_KEY}
   ```

### Database Queries

The system queries the local PostgreSQL database for:
- Email engagement statistics by campaign
- Daily activity aggregations
- Recent contact interactions
- Performance metric calculations

## Usage

### Accessing Campaign Stats

1. Navigate to the Smartlead section in the dashboard
2. Click on the "Campaign Stats" tab
3. Select a campaign from the dropdown
4. View comprehensive analytics and metrics

### Direct URL Access

Campaign statistics can also be accessed directly via:
```
/dashboard/smartlead/campaign-stats
```

## Performance Considerations

- **Pagination**: Large campaigns are fetched with pagination to avoid timeouts
- **Caching**: Statistics are calculated on-demand but could benefit from caching
- **Rate Limiting**: Respects Smartlead API rate limits (10 requests per 2 seconds)

## Error Handling

- Graceful fallbacks for missing data
- Error messages for API failures
- Loading states during data fetching
- Retry mechanisms for failed requests

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live statistics
2. **Export Functionality**: CSV/PDF export of campaign reports
3. **Comparative Analytics**: Compare multiple campaigns
4. **Advanced Filtering**: Date range and status filters
5. **Predictive Analytics**: Forecast campaign performance
6. **A/B Testing**: Compare sequence variations

## Technical Implementation

### Components

- `CampaignStats.tsx`: Main statistics component
- `SmartleadView.tsx`: Updated with new stats tab
- API routes for data fetching

### Dependencies

- Recharts for data visualization
- Lucide React for icons
- Tailwind CSS for styling
- Next.js API routes for backend

### Database Schema

The system relies on the existing `contacts` table with Smartlead-related fields:
- `smartlead_campaign_id`
- `smartlead_lead_id`
- `smartlead_status`
- `last_email_sent_at`

## Configuration

Ensure the following environment variables are set:
```
SMARTLEAD_API_KEY=your_api_key
SMARTLEAD_BASE_URL=https://server.smartlead.ai/api/v1/
```

## Troubleshooting

### Common Issues

1. **No campaigns showing**: Check API key configuration
2. **Missing statistics**: Verify database connection and data sync
3. **Slow loading**: Check network connectivity and API rate limits
4. **Chart rendering issues**: Ensure all required data fields are present

### Debug Mode

Enable debug logging by checking browser console for detailed error messages and API response data. 