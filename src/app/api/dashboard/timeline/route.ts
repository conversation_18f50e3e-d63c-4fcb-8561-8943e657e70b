import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Enhanced timeline API with business-friendly presentation
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '7d';
    const source = searchParams.get('source') || 'all';
    const entityType = searchParams.get('entityType') || 'all';
    const stage = searchParams.get('stage') || 'all';

    const client = await pool.connect();

    try {
      const days = getDaysFromRange(dateRange);
      const filters = buildFilters(source);

      // Get intuitive timeline data
      const timelineData = await getIntuitiveTimelineData(client, filters, days, entityType, stage);
      
      // Get performance trends
      const trends = await getPerformanceTrends(client, filters, days);
      
      // Get daily insights
      const insights = await getDailyInsights(client, filters, days);

      return NextResponse.json({
        success: true,
        data: {
          timeline: timelineData,
          trends,
          insights,
          summary: generateSummary(timelineData, trends)
        },
        metadata: {
          generated_at: new Date().toISOString(),
          filters: { dateRange, source, entityType, stage },
          business_context: getBusinessContext(dateRange)
        }
      });

    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Enhanced timeline error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

function getDaysFromRange(range: string): number {
  switch (range) {
    case '1d': return 1;
    case '7d': return 7;
    case '30d': return 30;
    case '90d': return 90;
    default: return 7;
  }
}

function buildFilters(source: string) {
  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (source !== 'all') {
    conditions.push(`c.source = $${paramIndex}`);
    params.push(source);
    paramIndex++;
  }

  return {
    whereClause: conditions.length > 0 ? 'AND ' + conditions.join(' AND ') : '',
    params
  };
}

async function getIntuitiveTimelineData(client: any, filters: any, days: number, entityType: string, stage: string) {
  // Create date series for consistent timeline
  const dateSeriesQuery = `
    SELECT generate_series(
      CURRENT_DATE - INTERVAL '${days} days',
      CURRENT_DATE,
      '1 day'::interval
    )::date as date
  `;

  const dateResult = await client.query(dateSeriesQuery);
  const dates = dateResult.rows.map(row => row.date);

  // Get contact pipeline progress by stage
  const contactStages = [
    { key: 'email_verification', name: 'Email Validation', date_col: 'email_verification_date', status_col: 'email_verification_status' },
    { key: 'osint', name: 'Lead Research', date_col: 'osint_date', status_col: 'osint_status' },
    { key: 'overview_extraction', name: 'Profile Building', date_col: 'overview_extraction_date', status_col: 'overview_extraction_status' },
    { key: 'classification', name: 'Lead Scoring', date_col: 'classification_date', status_col: 'classification_status' },
    { key: 'email_generation', name: 'Content Creation', date_col: 'email_generation_date', status_col: 'email_generation_status' },
    { key: 'email_sending', name: 'Outreach Delivery', date_col: 'email_sending_date', status_col: 'email_sending_status' }
  ];

  const timeline = [];

  for (const date of dates) {
    const dayData = {
      date: date.toISOString().split('T')[0],
      stages: {},
      daily_totals: {
        leads_processed: 0,
        leads_completed: 0,
        leads_failed: 0,
        conversion_rate: 0
      }
    };

    // Get data for each stage on this date
    for (const stageInfo of contactStages) {
      if (stage !== 'all' && stage !== stageInfo.key) continue;
      if (entityType !== 'all' && entityType !== 'contacts') continue;

      const stageQuery = `
        SELECT 
          COUNT(CASE WHEN ${stageInfo.status_col} = 'completed' AND DATE(${stageInfo.date_col}) = $1 THEN 1 END) as completed,
          COUNT(CASE WHEN ${stageInfo.status_col} = 'failed' AND DATE(updated_at) = $1 THEN 1 END) as failed,
          COUNT(CASE WHEN ${stageInfo.status_col} = 'running' AND DATE(updated_at) = $1 THEN 1 END) as running,
          COUNT(CASE WHEN ${stageInfo.status_col} = 'pending' AND DATE(created_at) = $1 THEN 1 END) as new_leads,
          ROUND(AVG(CASE WHEN ${stageInfo.status_col} = 'completed' AND DATE(${stageInfo.date_col}) = $1 
            THEN EXTRACT(EPOCH FROM (${stageInfo.date_col} - created_at)) / 3600 END), 2) as avg_processing_hours
        FROM contacts c
        WHERE (
          DATE(${stageInfo.date_col}) = $1 OR 
          DATE(updated_at) = $1 OR 
          DATE(created_at) = $1
        )
        ${filters.whereClause}
      `;

      const stageResult = await client.query(stageQuery, [date, ...filters.params]);
      const stageData = stageResult.rows[0];

      dayData.stages[stageInfo.key] = {
        stage_name: stageInfo.name,
        completed: parseInt(stageData.completed || 0),
        failed: parseInt(stageData.failed || 0),
        running: parseInt(stageData.running || 0),
        new_leads: parseInt(stageData.new_leads || 0),
        avg_processing_hours: parseFloat(stageData.avg_processing_hours || 0),
        success_rate: calculateSuccessRate(stageData)
      };

      // Add to daily totals
      dayData.daily_totals.leads_processed += parseInt(stageData.completed || 0) + parseInt(stageData.failed || 0);
      dayData.daily_totals.leads_completed += parseInt(stageData.completed || 0);
      dayData.daily_totals.leads_failed += parseInt(stageData.failed || 0);
    }

    // Calculate daily conversion rate
    if (dayData.daily_totals.leads_processed > 0) {
      dayData.daily_totals.conversion_rate = parseFloat(
        ((dayData.daily_totals.leads_completed / dayData.daily_totals.leads_processed) * 100).toFixed(2)
      );
    }

    timeline.push(dayData);
  }

  return timeline;
}

function calculateSuccessRate(stageData: any): number {
  const completed = parseInt(stageData.completed || 0);
  const failed = parseInt(stageData.failed || 0);
  const total = completed + failed;
  
  if (total === 0) return 0;
  return parseFloat(((completed / total) * 100).toFixed(2));
}

async function getPerformanceTrends(client: any, filters: any, days: number) {
  const trendsQuery = `
    WITH daily_performance AS (
      SELECT 
        DATE(email_verification_date) as date,
        COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as email_validations,
        COUNT(CASE WHEN osint_status = 'completed' THEN 1 END) as research_completed,
        COUNT(CASE WHEN email_generation_status = 'completed' THEN 1 END) as content_created,
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as emails_sent,
        ROUND(AVG(CASE WHEN email_verification_status = 'completed' 
          THEN EXTRACT(EPOCH FROM (email_verification_date - created_at)) / 3600 END), 2) as avg_processing_time
      FROM contacts c
      WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
      ${filters.whereClause}
      GROUP BY DATE(email_verification_date)
      ORDER BY DATE(email_verification_date)
    ),
    trend_analysis AS (
      SELECT 
        *,
        LAG(email_validations) OVER (ORDER BY date) as prev_validations,
        LAG(research_completed) OVER (ORDER BY date) as prev_research,
        LAG(content_created) OVER (ORDER BY date) as prev_content,
        LAG(emails_sent) OVER (ORDER BY date) as prev_emails
      FROM daily_performance
    )
    SELECT 
      date,
      email_validations,
      research_completed,
      content_created,
      emails_sent,
      avg_processing_time,
      CASE 
        WHEN prev_validations IS NULL THEN 'stable'
        WHEN email_validations > prev_validations THEN 'increasing'
        WHEN email_validations < prev_validations THEN 'decreasing'
        ELSE 'stable'
      END as validation_trend,
      CASE 
        WHEN prev_emails IS NULL THEN 'stable'
        WHEN emails_sent > prev_emails THEN 'increasing'
        WHEN emails_sent < prev_emails THEN 'decreasing'
        ELSE 'stable'
      END as outreach_trend
    FROM trend_analysis
    ORDER BY date
  `;

  const trendsResult = await client.query(trendsQuery, filters.params);
  return trendsResult.rows.map(row => ({
    date: row.date,
    metrics: {
      email_validations: parseInt(row.email_validations || 0),
      research_completed: parseInt(row.research_completed || 0),
      content_created: parseInt(row.content_created || 0),
      emails_sent: parseInt(row.emails_sent || 0),
      avg_processing_time: parseFloat(row.avg_processing_time || 0)
    },
    trends: {
      validation: row.validation_trend,
      outreach: row.outreach_trend
    }
  }));
}

async function getDailyInsights(client: any, filters: any, days: number) {
  const insightsQuery = `
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as new_leads,
      COUNT(CASE WHEN email_verification_status = 'completed' THEN 1 END) as validated_today,
      COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) as outreach_sent,
      COUNT(CASE WHEN processing_error_count > 0 THEN 1 END) as leads_with_errors,
      ROUND(
        COUNT(CASE WHEN email_sending_status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(*), 0), 2
      ) as daily_conversion_rate
    FROM contacts c
    WHERE created_at >= CURRENT_DATE - INTERVAL '${days} days'
    ${filters.whereClause}
    GROUP BY DATE(created_at)
    ORDER BY DATE(created_at) DESC
  `;

  const insightsResult = await client.query(insightsQuery, filters.params);
  return insightsResult.rows.map(row => ({
    date: row.date,
    new_leads: parseInt(row.new_leads || 0),
    validated_today: parseInt(row.validated_today || 0),
    outreach_sent: parseInt(row.outreach_sent || 0),
    leads_with_errors: parseInt(row.leads_with_errors || 0),
    daily_conversion_rate: parseFloat(row.daily_conversion_rate || 0),
    health_indicator: getHealthIndicator(row)
  }));
}

function getHealthIndicator(row: any): 'excellent' | 'good' | 'warning' | 'critical' {
  const conversionRate = parseFloat(row.daily_conversion_rate || 0);
  const errorRate = parseInt(row.leads_with_errors || 0) / parseInt(row.new_leads || 1) * 100;
  
  if (conversionRate >= 80 && errorRate < 5) return 'excellent';
  if (conversionRate >= 60 && errorRate < 10) return 'good';
  if (conversionRate >= 30 && errorRate < 20) return 'warning';
  return 'critical';
}

function generateSummary(timelineData: any[], trends: any[]) {
  const totalProcessed = timelineData.reduce((sum, day) => sum + day.daily_totals.leads_processed, 0);
  const totalCompleted = timelineData.reduce((sum, day) => sum + day.daily_totals.leads_completed, 0);
  const avgConversionRate = timelineData.reduce((sum, day) => sum + day.daily_totals.conversion_rate, 0) / timelineData.length;
  
  return {
    total_leads_processed: totalProcessed,
    total_leads_completed: totalCompleted,
    overall_conversion_rate: parseFloat(avgConversionRate.toFixed(2)),
    daily_average: Math.round(totalProcessed / timelineData.length),
    performance_trend: trends.length > 1 ? 
      (trends[trends.length - 1].metrics.emails_sent > trends[0].metrics.emails_sent ? 'improving' : 'declining') : 'stable'
  };
}

function getBusinessContext(dateRange: string): string {
  const contexts = {
    '1d': 'Real-time daily performance snapshot',
    '7d': 'Weekly pipeline performance overview',
    '30d': 'Monthly trend analysis and optimization insights',
    '90d': 'Quarterly strategic performance review'
  };
  return contexts[dateRange as keyof typeof contexts] || 'Pipeline performance analysis';
}
