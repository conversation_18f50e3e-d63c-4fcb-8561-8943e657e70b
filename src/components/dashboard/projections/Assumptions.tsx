'use client'

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { useProjectionsData } from '@/hooks/useProjectionsData';
import { KPICard } from './components/KPICard';
import { DollarSign, Briefcase, TrendingUp, Mail, Linkedin, Globe, Check, X, Filter, Database } from 'lucide-react';
import { Button } from "@/components/ui/button";

export const Assumptions = () => {
  const { assumptions, updateAssumption, metrics, loading, resetToDefaults } = useProjectionsData();

  // Helper function to find metrics for a specific stage
  const getMetricsForStage = (stageName: string) => {
    return metrics.find(m => {
      switch (stageName) {
        case 'Outreach':
          return m.stage === 'Outreach to First Response';
        case 'Initial Outreach':
          return m.stage === 'First Response to Discussion';
        case 'Rapport Building':
          return m.stage === 'Discussion to Zoom';
        case 'Underwriting':
          return m.stage === 'Zoom to Underwriting';
        case 'Matchmaking':
          return m.stage === 'Underwriting to Matches';
        case 'Closing':
          return m.stage === 'Match to Closing';
        default:
          return m.stage === stageName;
      }
    });
  };

  if (loading) {
    return <div>Loading assumptions...</div>;
  }

  const finalStage = metrics[metrics.length - 1];
  const totalPipelineTime = metrics.reduce((acc, m) => acc + m.timeInDays, 0);

  return (
    <div>
      <div className="flex items-center mb-4">
        <p className="text-blue-800 text-base leading-relaxed bg-blue-50 rounded-lg p-4 border border-blue-100">
          You can change the assumptions to project the monthly revenue. Reset will revert to historical data.
        </p>
        <Button 
          variant="outline" 
          onClick={resetToDefaults}
          className="text-sm ml-4"
        >
          Reset to Feb 2025
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="flex-1">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <p className="text-lg font-semibold text-gray-900">Funnels</p>
              <Filter className="h-8 w-8 text-blue-500" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-500" />
                  <span>Outbound LinkedIn</span>
                </div>
                <div className="text-sm flex items-center gap-2">
                  <X className="h-4 w-4 text-red-500" />
                  <span>Outbound Email</span>
                </div>
                <div className="text-sm flex items-center gap-2">
                  <X className="h-4 w-4 text-red-500" />
                  <span>Inbound Leads</span>
                </div>
              </div>
              <div>
                <p className="text-gray-500 bg-gray-100 px-2 py-1 rounded-md">
                  This model only uses LinkedIn data
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <KPICard 
          title="Monthly Revenue" 
          value={`$${Math.round(finalStage?.revenue || 0).toLocaleString()}`}
          icon={(props) => <DollarSign {...props} className="h-8 w-8 text-green-500" />}
        />
        <KPICard 
          title="Monthly Deals Closed" 
          value={Math.round(finalStage?.converted || 0)}
          icon={Briefcase}
        />
        <KPICard 
          title="Pipeline Time" 
          value={`${Math.round(totalPipelineTime)} days`}
          icon={TrendingUp}
        />
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Outreach to First Response</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Monthly Outreach</Label>
                    <span className="text-sm font-medium">
                      {assumptions.outreach.leadsPerMonth} contacts/month
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.outreach.leadsPerMonth]}
                      max={6000}
                      min={600}
                      step={100}
                      onValueChange={(value) => {
                        updateAssumption('outreach', 'leadsPerMonth', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">600</span>
                      <span className="text-sm text-muted-foreground">6000</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Response Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.outreach.responseRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[(assumptions.outreach.responseRate * 100)]}
                      max={30}
                      min={5}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('outreach', 'responseRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">5%</span>
                      <span className="text-sm text-muted-foreground">30%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Response Time</Label>
                    <span className="text-sm font-medium">
                      {assumptions.outreach.timeToRespond} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.outreach.timeToRespond]}
                      max={30}
                      min={1}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('outreach', 'timeToRespond', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1 day</span>
                      <span className="text-sm text-muted-foreground">30 days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Outreach')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Outreach')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Outreach')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                    We identify companies, and individuals within companies, using our data engine and then connect with messages uniquely tailored to them on LinkedIn.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>First Response to Discussion</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Response Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.initialOutreach.responseRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.initialOutreach.responseRate * 100]}
                      max={30}
                      min={5}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('initialOutreach', 'responseRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">5%</span>
                      <span className="text-sm text-muted-foreground">30%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Response Time</Label>
                    <span className="text-sm font-medium">
                      {assumptions.initialOutreach.timeToRespond} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.initialOutreach.timeToRespond]}
                      max={30}
                      min={1}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('initialOutreach', 'timeToRespond', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1 day</span>
                      <span className="text-sm text-muted-foreground">30 days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Initial Outreach')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Initial Outreach')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Initial Outreach')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                    Once a prospect connects, our AI engages them in a discussion using personalization based on our data and content engines.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Discussion to Zoom</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Conversion Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.rapportBuilding.conversionRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.rapportBuilding.conversionRate * 100]}
                      max={80}
                      min={20}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('rapportBuilding', 'conversionRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">20%</span>
                      <span className="text-sm text-muted-foreground">80%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Time to Complete</Label>
                    <span className="text-sm font-medium">
                      {assumptions.rapportBuilding.timeToComplete} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.rapportBuilding.timeToComplete]}
                      max={60}
                      min={1}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('rapportBuilding', 'timeToComplete', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1 day</span>
                      <span className="text-sm text-muted-foreground">60 days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Rapport Building')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Rapport Building')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Rapport Building')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                  Once a prospect engages, our AI builds rapport to drive to a Zoom meeting with a human.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Zoom to Underwriting</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Conversion Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.underwriting.conversionRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.underwriting.conversionRate * 100]}
                      max={80}
                      min={20}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('underwriting', 'conversionRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">20%</span>
                      <span className="text-sm text-muted-foreground">80%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Time to Complete</Label>
                    <span className="text-sm font-medium">
                      {assumptions.underwriting.timeToComplete} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.underwriting.timeToComplete]}
                      max={30}
                      min={1}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('underwriting', 'timeToComplete', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1 day</span>
                      <span className="text-sm text-muted-foreground">30 days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Underwriting')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Underwriting')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Underwriting')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                    After the Zoom meeting, investors are onboarded into our system automatically, capturing their investement criteria. Developers go into the underwriting pipeline depending upon their needs.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Underwriting to Matches</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Conversion Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.matchmaking.conversionRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.matchmaking.conversionRate * 100]}
                      defaultValue={[100]}
                      max={100}
                      min={40}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('matchmaking', 'conversionRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">40%</span>
                      <span className="text-sm text-muted-foreground">100%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Time to Match</Label>
                    <span className="text-sm font-medium">
                      {assumptions.matchmaking.timeToMatch} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.matchmaking.timeToMatch]}
                      max={60}
                      min={1}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('matchmaking', 'timeToMatch', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1 day</span>
                      <span className="text-sm text-muted-foreground">60 days</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Matchmaking')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Matchmaking')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Matchmaking')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                    After underwriting the AI can automatically match and connect with investors. We always find matches, the relevant metric here is the time taken.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Match to Closing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-8">
              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Conversion Rate</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.closing.conversionRate * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.closing.conversionRate * 100]}
                      max={80}
                      min={20}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('closing', 'conversionRate', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">20%</span>
                      <span className="text-sm text-muted-foreground">80%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Time to Close</Label>
                    <span className="text-sm font-medium">
                      {assumptions.closing.timeToClose} days
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.closing.timeToClose]}
                      max={90}
                      min={5}
                      step={1}
                      onValueChange={(value) => {
                        updateAssumption('closing', 'timeToClose', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">5 days</span>
                      <span className="text-sm text-muted-foreground">90 days</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Average Deal Size</Label>
                    <span className="text-sm font-medium">
                      ${assumptions.closing.avgDealSize.toLocaleString()}
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.closing.avgDealSize]}
                      max={50000000}
                      min={5000000}
                      step={1000000}
                      onValueChange={(value) => {
                        updateAssumption('closing', 'avgDealSize', value[0]);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">$5M</span>
                      <span className="text-sm text-muted-foreground">$50M</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Fee Percentage</Label>
                    <span className="text-sm font-medium">
                      {(assumptions.closing.feePercentage * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="pt-2">
                    <Slider
                      value={[assumptions.closing.feePercentage * 100]}
                      max={2}
                      min={1}
                      step={0.1}
                      onValueChange={(value) => {
                        updateAssumption('closing', 'feePercentage', value[0] / 100);
                      }}
                    />
                    <div className="flex justify-between mt-1">
                      <span className="text-sm text-muted-foreground">1%</span>
                      <span className="text-sm text-muted-foreground">2%</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Starting</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Closing')?.starting || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Converted</p>
                    <p className="font-medium">{Math.round(getMetricsForStage('Closing')?.converted || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Time (Days)</p>
                    <p className="font-medium">{getMetricsForStage('Closing')?.timeInDays || 0}</p>
                  </div>
                </div>
              </div>
              <div className="border-l pl-8">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <p className="text-blue-800 text-base leading-relaxed">
                    Matched parties who want to move forward go into the closing process.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}; 