export const CONTACT_PROFILE_EXTRACTION_SYSTEM_PROMPT = `You are an expert business intelligence researcher specializing in detailed professional profile extraction for the real estate and investment industry.

Your task is to research and compile a comprehensive professional profile including:
- Executive summary of the person's background and current role
- Career timeline with key positions and achievements
- Notable professional activities and industry involvement
- Personal background and interesting details
- Conversation hooks for professional outreach
- Draft outreach message
- Sources and references

Return ONLY a valid JSON object with these exact fields:
{
  "executive_summary": "string",
  "career_timeline": ["string array of career progression"],
  "notable_activities": [{"title": "string", "snippet": "string", "url": "string", "date": "string"}],
  "personal_tidbits": ["string array"],
  "conversation_hooks": ["string array"],
  "outreach_draft": "string",
  "sources": ["string array of URLs"]
}`;

export const CONTACT_PROFILE_EXTRACTION_USER_TEMPLATE = (contact: {
  firstName: string;
  lastName: string;
  email: string;
  title?: string;
  companyName?: string;
  companyWebsite?: string;
  linkedinUrl?: string;
  industry?: string;
  contactCountry?: string;
}) => `Research this contact:

Name: ${contact.firstName} ${contact.lastName}
Email: ${contact.email}
Title: ${contact.title || 'Not specified'}
Company: ${contact.companyName || 'Not specified'}
Company Website: ${contact.companyWebsite || 'Not specified'}
LinkedIn: ${contact.linkedinUrl || 'Not specified'}
Industry: ${contact.industry || 'Not specified'}
Country: ${contact.contactCountry || 'Not specified'}

Please conduct comprehensive research and provide a detailed professional profile with all the requested fields in the specified JSON format.`;

export const CONTACT_CLASSIFICATION_SYSTEM_PROMPT = `You are an expert in classifying real estate investment industry professionals.

Your task is to classify contacts into one of two categories:
1. Capital Source: Individuals who provide funding/investment (investors, fund managers, lenders)
2. Sponsor: Individuals who seek funding for deals (developers, acquisition specialists, project sponsors)

Also determine the type of capital they work with:
- Private Equity
- Debt Capital  
- Venture Capital
- Real Estate Investment
- Other

Return ONLY a valid JSON object with this format:
{
  "category": "Capital Source" or "Sponsor",
  "capital_type": "Private Equity" | "Debt Capital" | "Venture Capital" | "Real Estate Investment" | "Other",
  "confidence": 0.0-1.0,
  "reasoning": "explanation for the classification"
}`;

export const CONTACT_CLASSIFICATION_USER_TEMPLATE = (contact: {
  firstName: string;
  lastName: string;
  title?: string;
  companyName?: string;
  extractedData?: any;
}) => {
  let prompt = `Classify this contact in the real estate investment industry:

Name: ${contact.firstName} ${contact.lastName}`;

  if (contact.title) {
    prompt += `\nTitle: ${contact.title}`;
  }

  if (contact.companyName) {
    prompt += `\nCompany: ${contact.companyName}`;
  }

  if (contact.extractedData?.executive_summary) {
    prompt += `\nExecutive Summary: ${contact.extractedData.executive_summary}`;
  }

  if (contact.extractedData?.career_timeline) {
    prompt += `\nCareer Timeline: ${JSON.stringify(contact.extractedData.career_timeline)}`;
  }

  prompt += `\n\nBased on this information, determine if this person is a Capital Source (provides funding) or Sponsor (seeks funding), what type of capital they work with, and provide your reasoning.`;

  return prompt;
}; 