import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'
import { ConflictRecord, CompanyConflict, ContactConflict } from '@/types/conflict'

// GET: List all conflicts with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '20')
    const type = searchParams.get('type') // 'company' | 'contact' | null (all)
    const status = searchParams.get('status') || 'pending'
    
    const offset = (page - 1) * pageSize
    const client = await pool.connect()
    
    try {
      let companies: CompanyConflict[] = []
      let contacts: ContactConflict[] = []
      let totalCompanies = 0
      let totalContacts = 0

      // Get total counts first
      if (!type || type === 'company') {
        const countResult = await client.query(
          'SELECT COUNT(*) FROM companies WHERE conflict_status = $1 AND conflicts IS NOT NULL',
          [status]
        )
        totalCompanies = parseInt(countResult.rows[0].count)
      }

      if (!type || type === 'contact') {
        const countResult = await client.query(
          'SELECT COUNT(*) FROM contacts WHERE conflict_status = $1 AND conflicts IS NOT NULL',
          [status]
        )
        totalContacts = parseInt(countResult.rows[0].count)
      }

      // If filtering by specific type, use simple pagination
      if (type === 'company') {
        const companyQuery = `
          SELECT 
            company_id as id,
            company_name,
            company_website,
            industry,
            conflicts,
            conflict_status,
            conflict_created_at,
            conflict_resolved_at,
            conflict_source,
            extra_attrs
          FROM companies 
          WHERE conflict_status = $1 AND conflicts IS NOT NULL
          ORDER BY conflict_created_at DESC
          LIMIT $2 OFFSET $3
        `
        
        const companyResult = await client.query(companyQuery, [status, pageSize, offset])
        companies = companyResult.rows.map(row => ({
          ...row,
          type: 'company' as const,
          company_id: row.id,
          name: row.company_name,
          match_reason: row.extra_attrs?.match_reason
        }))
      } else if (type === 'contact') {
        const contactQuery = `
          SELECT 
            c.contact_id as id,
            c.full_name,
            c.email,
            c.linkedin_url,
            c.conflicts,
            c.conflict_status,
            c.conflict_created_at,
            c.conflict_resolved_at,
            c.conflict_source,
            c.extra_attrs,
            comp.company_name,
            comp.company_id
          FROM contacts c
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE c.conflict_status = $1 AND c.conflicts IS NOT NULL
          ORDER BY c.conflict_created_at DESC
          LIMIT $2 OFFSET $3
        `
        
        const contactResult = await client.query(contactQuery, [status, pageSize, offset])
        contacts = contactResult.rows.map(row => ({
          ...row,
          type: 'contact' as const,
          contact_id: row.id,
          name: row.full_name,
          match_reason: row.extra_attrs?.match_reason
        }))
      } else {
        // For combined results, we need to fetch both and merge them
        const combinedQuery = `
          (
            SELECT 
              'company' as type,
              company_id as id,
              company_name as name,
              company_website,
              industry,
              conflicts,
              conflict_status,
              conflict_created_at,
              conflict_resolved_at,
              conflict_source,
              extra_attrs,
              NULL as email,
              NULL as full_name,
              NULL as company_id_ref,
              NULL as linkedin_url
            FROM companies 
            WHERE conflict_status = $1 AND conflicts IS NOT NULL
          )
          UNION ALL
          (
            SELECT 
              'contact' as type,
              c.contact_id as id,
              c.full_name as name,
              NULL as company_website,
              NULL as industry,
              c.conflicts,
              c.conflict_status,
              c.conflict_created_at,
              c.conflict_resolved_at,
              c.conflict_source,
              c.extra_attrs,
              c.email,
              c.full_name,
              c.company_id as company_id_ref,
              c.linkedin_url
            FROM contacts c
            WHERE c.conflict_status = $1 AND c.conflicts IS NOT NULL
          )
          ORDER BY conflict_created_at DESC
          LIMIT $2 OFFSET $3
        `
        
        const combinedResult = await client.query(combinedQuery, [status, pageSize, offset])
        
        // Separate the results back into companies and contacts
        combinedResult.rows.forEach(row => {
          if (row.type === 'company') {
            companies.push({
              ...row,
              type: 'company' as const,
              company_id: row.id,
              company_name: row.name,
              match_reason: row.extra_attrs?.match_reason
            })
          } else {
            contacts.push({
              ...row,
              type: 'contact' as const,
              contact_id: row.id,
              full_name: row.name,
              match_reason: row.extra_attrs?.match_reason
            })
          }
        })

        // For combined results, we need to get company names for contacts
        if (contacts.length > 0) {
          const contactIds = contacts.map(c => c.contact_id)
          const companyInfoQuery = `
            SELECT 
              c.contact_id,
              comp.company_name,
              comp.company_id
            FROM contacts c
            LEFT JOIN companies comp ON c.company_id = comp.company_id
            WHERE c.contact_id = ANY($1)
          `
          
          const companyInfoResult = await client.query(companyInfoQuery, [contactIds])
          const companyInfoMap = new Map(
            companyInfoResult.rows.map(row => [row.contact_id, row])
          )
          
          // Update contacts with company info
          contacts = contacts.map(contact => ({
            ...contact,
            company_name: companyInfoMap.get(contact.contact_id)?.company_name,
            company_id: companyInfoMap.get(contact.contact_id)?.company_id,
            match_reason: (contact as any).extra_attrs?.match_reason
          }))
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          companies,
          contacts,
          pagination: {
            page,
            pageSize,
            totalCompanies,
            totalContacts,
            total: totalCompanies + totalContacts,
            totalPages: Math.ceil((totalCompanies + totalContacts) / pageSize)
          }
        }
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error fetching conflicts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch conflicts' },
      { status: 500 }
    )
  }
}

// POST: Resolve conflicts
export async function POST(request: NextRequest) {
  try {
    const { resolutions } = await request.json()
    
    if (!resolutions || !Array.isArray(resolutions)) {
      return NextResponse.json(
        { error: 'Invalid resolutions array' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    const logs: string[] = []
    let resolved = 0
    
    try {
      await client.query('BEGIN')

      for (const resolution of resolutions) {
        const { record_id, record_type, field_name, resolution: resolutionType, manual_value } = resolution

        if (record_type === 'company') {
          // Get current conflicts
          const result = await client.query(
            'SELECT conflicts FROM companies WHERE company_id = $1',
            [record_id]
          )
          
          if (result.rows.length === 0) continue
          
          const conflicts = result.rows[0].conflicts || {}
          const fieldConflict = conflicts[field_name]
          
          if (!fieldConflict) continue

          let finalValue: string | null = null
          
          switch (resolutionType) {
            case 'keep_existing':
              finalValue = fieldConflict.existing_value
              break
            case 'use_new':
              finalValue = fieldConflict.new_value
              break
            case 'manual':
              finalValue = manual_value || null
              break
          }

          // Update the field with resolved value
          await client.query(
            `UPDATE companies SET ${field_name} = $1 WHERE company_id = $2`,
            [finalValue, record_id]
          )

          // Remove this conflict from the conflicts JSON
          delete conflicts[field_name]
          
          // Update conflicts and status
          const isResolved = Object.keys(conflicts).length === 0
          await client.query(`
            UPDATE companies 
            SET conflicts = $1::jsonb,
                conflict_status = $2,
                conflict_resolved_at = CASE WHEN $2 = 'resolved' THEN CURRENT_TIMESTAMP ELSE conflict_resolved_at END,
                updated_at = CURRENT_TIMESTAMP
            WHERE company_id = $3
          `, [
            Object.keys(conflicts).length > 0 ? JSON.stringify(conflicts) : null,
            isResolved ? 'resolved' : 'pending',
            record_id
          ])

          logs.push(`Resolved company field '${field_name}' for record ${record_id}`)
          resolved++

        } else if (record_type === 'contact') {
          // Similar logic for contacts
          const result = await client.query(
            'SELECT conflicts FROM contacts WHERE contact_id = $1',
            [record_id]
          )
          
          if (result.rows.length === 0) continue
          
          const conflicts = result.rows[0].conflicts || {}
          const fieldConflict = conflicts[field_name]
          
          if (!fieldConflict) continue

          let finalValue: string | null = null
          
          switch (resolutionType) {
            case 'keep_existing':
              finalValue = fieldConflict.existing_value
              break
            case 'use_new':
              finalValue = fieldConflict.new_value
              break
            case 'manual':
              finalValue = manual_value || null
              break
          }

          // Update the field with resolved value
          await client.query(
            `UPDATE contacts SET ${field_name} = $1 WHERE contact_id = $2`,
            [finalValue, record_id]
          )

          // Remove this conflict from the conflicts JSON
          delete conflicts[field_name]
          
          // Update conflicts and status
          const isResolved = Object.keys(conflicts).length === 0
          await client.query(`
            UPDATE contacts 
            SET conflicts = $1::jsonb,
                conflict_status = $2,
                conflict_resolved_at = CASE WHEN $2 = 'resolved' THEN CURRENT_TIMESTAMP ELSE conflict_resolved_at END,
                updated_at = CURRENT_TIMESTAMP
            WHERE contact_id = $3
          `, [
            Object.keys(conflicts).length > 0 ? JSON.stringify(conflicts) : null,
            isResolved ? 'resolved' : 'pending',
            record_id
          ])

          logs.push(`Resolved contact field '${field_name}' for record ${record_id}`)
          resolved++
        }
      }

      await client.query('COMMIT')

      return NextResponse.json({
        success: true,
        resolved,
        logs
      })

    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error resolving conflicts:', error)
    return NextResponse.json(
      { error: 'Failed to resolve conflicts' },
      { status: 500 }
    )
  }
}

// DELETE: Clear all conflicts
export async function DELETE(request: NextRequest) {
  try {
    const client = await pool.connect()
    const logs: string[] = []
    let clearedCompanies = 0
    let clearedContacts = 0
    
    try {
      await client.query('BEGIN')

      // Clear all company conflicts
      const companyResult = await client.query(`
        UPDATE companies 
        SET conflicts = NULL,
            conflict_status = 'none',
            conflict_resolved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE conflict_status = 'pending' AND conflicts IS NOT NULL
        RETURNING company_id, company_name
      `)
      
      clearedCompanies = companyResult.rows.length
      companyResult.rows.forEach(row => {
        logs.push(`Cleared conflicts for company: ${row.company_name} (ID: ${row.company_id})`)
      })

      // Clear all contact conflicts
      const contactResult = await client.query(`
        UPDATE contacts 
        SET conflicts = NULL,
            conflict_status = 'none',
            conflict_resolved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE conflict_status = 'pending' AND conflicts IS NOT NULL
        RETURNING contact_id, full_name
      `)
      
      clearedContacts = contactResult.rows.length
      contactResult.rows.forEach(row => {
        logs.push(`Cleared conflicts for contact: ${row.full_name} (ID: ${row.contact_id})`)
      })

      await client.query('COMMIT')

      return NextResponse.json({
        success: true,
        cleared: {
          companies: clearedCompanies,
          contacts: clearedContacts,
          total: clearedCompanies + clearedContacts
        },
        logs: logs.slice(-50) // Return last 50 log entries
      })

    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error clearing conflicts:', error)
    return NextResponse.json(
      { error: 'Failed to clear conflicts' },
      { status: 500 }
    )
  }
} 