import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ContactCheck {
  fullName: string
  companyName: string
}

export async function POST(request: NextRequest) {
  try {
    const { contacts } = await request.json()

    if (!contacts || !Array.isArray(contacts)) {
      return NextResponse.json(
        { error: 'Invalid contacts array' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      const existsMap: Record<string, boolean> = {}
      
      if (contacts.length > 0) {
        // Check each contact individually since we need to match both name and company
        for (const contact of contacts as ContactCheck[]) {
          if (!contact.fullName || !contact.companyName) continue

          const query = `
            SELECT c.contact_id 
            FROM contacts c
            JOIN companies comp ON c.company_id = comp.company_id
            WHERE c.full_name = $1 AND comp.company_name = $2
          `
          
          const result = await client.query(query, [contact.fullName, contact.companyName])
          
          // Use the same key format as the frontend
          const contactKey = `${contact.fullName}||${contact.companyName}`
          existsMap[contactKey] = result.rows.length > 0
        }
      }

      return NextResponse.json({
        success: true,
        exists: existsMap
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Contact check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check contacts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 