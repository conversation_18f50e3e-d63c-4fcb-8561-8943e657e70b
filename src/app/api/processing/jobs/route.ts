import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import { ProcessingJob, ProcessingStage } from '@/types/processing';
import { v4 as uuidv4 } from 'uuid';

// GET /api/processing/jobs - Get all jobs with optional filtering
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100);
    const offset = parseInt(url.searchParams.get('offset') || '0');

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];
    let paramCount = 0;

    if (status) {
      whereClause += ` AND status = $${++paramCount}`;
      params.push(status);
    }

    if (type) {
      whereClause += ` AND type = $${++paramCount}`;
      params.push(type);
    }

    // For now, we'll create a mock jobs table structure
    // In reality, you'd want to create a proper jobs table in your database
    const query = `
      SELECT 
        'mock-job-' || ROW_NUMBER() OVER() as id,
        'email_validation' as type,
        'contact' as entity_type,
        contact_id as entity_id,
        CASE 
          WHEN email_status = 'Verified' THEN 'completed'
          WHEN email_status = 'Failed' THEN 'failed'
          ELSE 'pending'
        END as status,
        1 as priority,
        created_at,
        updated_at as started_at,
        updated_at as completed_at,
        NULL as error_message,
        '{}' as metadata,
        CASE 
          WHEN email_status = 'Verified' THEN 100
          WHEN email_status = 'Failed' THEN 0
          ELSE 50
        END as progress
      FROM contacts
      WHERE email IS NOT NULL
      ${status ? `AND CASE 
        WHEN email_status = 'Verified' THEN 'completed'
        WHEN email_status = 'Failed' THEN 'failed'
        ELSE 'pending'
      END = $${params.length}` : ''}
      ORDER BY created_at DESC
      LIMIT $${++paramCount} OFFSET $${++paramCount}
    `;

    params.push(limit, offset);
    const result = await pool.query(query, params);
    
    await pool.end();

    return NextResponse.json({
      success: true,
      data: {
        jobs: result.rows,
        total: result.rowCount,
        limit,
        offset
      }
    });

  } catch (error) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch jobs' },
      { status: 500 }
    );
  }
}

// POST /api/processing/jobs - Queue new processing jobs
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { entity_type, entity_ids, stage, priority = 1 } = body;

    if (!entity_type || !entity_ids || !stage) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: entity_type, entity_ids, stage' },
        { status: 400 }
      );
    }

    const jobs: ProcessingJob[] = [];

    // In a real implementation, you'd insert these into a jobs table
    // For now, we'll trigger the processing directly or return mock data
    
    for (const entityId of entity_ids) {
      const job: ProcessingJob = {
        id: uuidv4(),
        type: stage as ProcessingStage,
        entity_type,
        entity_id: entityId,
        status: 'pending',
        priority,
        created_at: new Date().toISOString(),
        metadata: body.metadata || {}
      };
      
      jobs.push(job);
      
      // Here you would:
      // 1. Insert job into jobs table
      // 2. Trigger the appropriate Python script or background worker
      // 3. Update job status to 'running'
      
      // For now, let's just simulate immediate processing based on the stage
      await simulateJobProcessing(pool, job);
    }

    await pool.end();

    return NextResponse.json({
      success: true,
      data: {
        jobs_queued: jobs.length,
        jobs
      }
    });

  } catch (error) {
    console.error('Error queueing jobs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to queue jobs' },
      { status: 500 }
    );
  }
}

// Simulate job processing (in reality, this would trigger Python scripts)
async function simulateJobProcessing(client: any, job: ProcessingJob) {
  try {
    switch (job.type) {
      case 'email_validation':
        if (job.entity_type === 'contact') {
          // Simulate email validation - mark as needing validation
          await client.query(
            'UPDATE contacts SET email_status = $1 WHERE contact_id = $2',
            ['pending_validation', job.entity_id]
          );
        }
        break;
        
      
        if (job.entity_type === 'company') {
          // Mark company for website scraping
          await client.query(
            'UPDATE companies SET processed = FALSE WHERE company_id = $1',
            [job.entity_id]
          );
        }
        break;
        
      case 'contact_search':
        if (job.entity_type === 'contact') {
          // Mark contact for OSINT search
          await client.query(
            'UPDATE contacts SET searched = FALSE WHERE contact_id = $1',
            [job.entity_id]
          );
        }
        break;
        
      case 'contact_classification':
        if (job.entity_type === 'contact') {
          // Mark contact for classification
          await client.query(
            'UPDATE contacts SET category = NULL WHERE contact_id = $1',
            [job.entity_id]
          );
        }
        break;
        
      case 'email_generation':
        if (job.entity_type === 'contact') {
          // Mark contact for email generation
          await client.query(
            'UPDATE contacts SET email_generated = FALSE WHERE contact_id = $1',
            [job.entity_id]
          );
        }
        break;
    }
  } catch (error) {
    console.error('Error simulating job processing:', error);
  }
} 