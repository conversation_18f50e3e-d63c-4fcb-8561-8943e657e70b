import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface DatabaseFieldsResponse {
  success: boolean
  companies?: string[]
  contacts?: string[]
  error?: string
}

export async function GET(): Promise<NextResponse<DatabaseFieldsResponse>> {
  try {
    // Fetch companies table columns
    const companiesQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'companies' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `
    
    // Fetch contacts table columns
    const contactsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'contacts' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `

    const [companiesResult, contactsResult] = await Promise.all([
      pool.query(companiesQuery),
      pool.query(contactsQuery)
    ])

    // Filter out system columns and internal fields
    const systemColumns = [
      'created_at', 'updated_at', 'id', 'company_id', 'contact_id',
      'canonical_handle', 'processed', 'extracted', 'processing_state',
      'processing_error_count', 'processing_attempts', 'last_processed_stage',
      'last_processed_at', 'conflict_status', 'conflict_created_at',
      'conflict_resolved_at', 'conflict_source', 'conflicts'
    ]

    const companiesFields = companiesResult.rows
      .map(row => row.column_name)
      .filter(column => !systemColumns.includes(column))

    const contactsFields = contactsResult.rows
      .map(row => row.column_name)
      .filter(column => !systemColumns.includes(column))

    return NextResponse.json({
      success: true,
      companies: companiesFields,
      contacts: contactsFields
    })

  } catch (error) {
    console.error('Database fields fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 })
  }
} 