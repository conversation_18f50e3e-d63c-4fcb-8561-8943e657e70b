import { BaseProcessor, ProcessorResult, ProcessingFilters } from '../processors/BaseProcessor'
import { EmailValidatorProcessor } from '../processors/EmailValidatorProcessor'
import { ContactClassificationProcessor } from '../processors/ContactClassificationProcessor'
import { ContactOverviewProcessor } from '../processors/ContactOverviewProcessor'
import { CompanyOverviewProcessor } from '../processors/CompanyOverviewProcessor'
import { ContactSearchProcessor } from '../processors/ContactSearchProcessor'
import { EmailGenerationProcessor } from '../processors/EmailGenerationProcessor'
import { CompanyWebCrawlerProcessor } from '../processors/CompanyWebCrawlerProcessor'
import { ProcessingStage } from '../../types/processing'

interface ScheduledJob {
  id: string
  stage: ProcessingStage
  processor: string
  schedule: string // cron expression
  enabled: boolean
  lastRun?: Date
  nextRun?: Date
  isRunning: boolean
}

interface JobExecution {
  jobId: string
  stage: ProcessingStage
  startTime: Date
  endTime?: Date
  result?: ProcessorResult
  error?: string
}

export class ProcessorScheduler {
  private jobs: Map<string, ScheduledJob> = new Map()
  private processors: Map<string, BaseProcessor> = new Map()
  private runningJobs: Map<string, JobExecution> = new Map()
  private scheduleIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor() {
    this.initializeProcessors()
    this.initializeDefaultJobs()
  }

  /**
   * Initialize available processors
   */
  private initializeProcessors(): void {
    this.processors.set('email_validator', new EmailValidatorProcessor())
    this.processors.set('contact_classification', new ContactClassificationProcessor())
    this.processors.set('contact_overview', new ContactOverviewProcessor())
    this.processors.set('company_overview', new CompanyOverviewProcessor())
    this.processors.set('company_web_crawler', new CompanyWebCrawlerProcessor())
    this.processors.set('contact_search', new ContactSearchProcessor())
    this.processors.set('email_generation', new EmailGenerationProcessor())
  }

  /**
   * Initialize default scheduled jobs
   */
  private initializeDefaultJobs(): void {
    // Email validation - every 30 minutes
    this.addJob({
      id: 'email_validation_auto',
      stage: 'email_validation',
      processor: 'email_validator',
      schedule: '*/30 * * * *', // Every 30 minutes
      enabled: false, // Disabled by default
      isRunning: false
    })



    // Company web crawler - every 4 hours
    this.addJob({
      id: 'company_web_crawler_auto',
      stage: 'company_web_crawler',
      processor: 'company_web_crawler',
      schedule: '0 */4 * * *', // Every 4 hours
      enabled: true, // Disabled by default
      isRunning: false
    })

    // Company overview extraction - every 2 hours
    this.addJob({
      id: 'company_overview_auto',
      stage: 'company_overview',
      processor: 'company_overview',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: true, // Disabled by default
      isRunning: false
    })

    // Contact search/OSINT - every hour
    this.addJob({
      id: 'contact_search_auto',
      stage: 'contact_search',
      processor: 'contact_search',
      schedule: '0 * * * *', // Every hour
      enabled: true,
      isRunning: false
    })

    // Contact overview/extraction - every hour
    this.addJob({
      id: 'contact_overview_auto',
      stage: 'contact_overview',
      processor: 'contact_overview',
      schedule: '0 * * * *', // Every hour
      enabled: true, // Disabled by default
      isRunning: false
    })

    // Contact classification - every hour
    this.addJob({
      id: 'contact_classification_auto',
      stage: 'contact_classification',
      processor: 'contact_classification',
      schedule: '0 * * * *', // Every hour
      enabled: true, // Disabled by default
      isRunning: false
    })

    // Email generation - every 2 hours
    this.addJob({
      id: 'email_generation_auto',
      stage: 'email_generation',
      processor: 'email_generation',
      schedule: '0 */2 * * *', // Every 2 hours
      enabled: false, // Disabled by default
      isRunning: false
    })
  }

  /**
   * Add a scheduled job
   */
  addJob(job: ScheduledJob): void {
    this.jobs.set(job.id, job)
    if (job.enabled) {
      this.scheduleJob(job)
    }
  }

  /**
   * Remove a scheduled job
   */
  removeJob(jobId: string): void {
    const interval = this.scheduleIntervals.get(jobId)
    if (interval) {
      clearInterval(interval)
      this.scheduleIntervals.delete(jobId)
    }
    this.jobs.delete(jobId)
  }

  /**
   * Enable/disable a job
   */
  toggleJob(jobId: string, enabled: boolean): void {
    const job = this.jobs.get(jobId)
    if (!job) return

    job.enabled = enabled
    
    if (enabled) {
      this.scheduleJob(job)
    } else {
      const interval = this.scheduleIntervals.get(jobId)
      if (interval) {
        clearInterval(interval)
        this.scheduleIntervals.delete(jobId)
      }
    }
  }

  /**
   * Schedule a job using cron-like timing
   */
  private scheduleJob(job: ScheduledJob): void {
    // Simple interval-based scheduling (in production, use a proper cron library)
    const intervalMs = this.parseCronToInterval(job.schedule)
    
    const interval = setInterval(async () => {
      if (!job.isRunning && job.enabled) {
        // Commented out to prevent automatic execution
        await this.executeJob(job.id)
      }
    }, intervalMs)
    
    this.scheduleIntervals.set(job.id, interval)
  }

  /**
   * Simple cron parser (basic implementation)
   * In production, use a proper cron library like node-cron
   */
  private parseCronToInterval(schedule: string): number {
    // Simple mappings for common patterns
    const patterns: Record<string, number> = {
      '*/30 * * * *': 30 * 60 * 1000,     // Every 30 minutes
      '0 * * * *': 60 * 60 * 1000,        // Every hour
      '0 */2 * * *': 2 * 60 * 60 * 1000,  // Every 2 hours
      '0 */4 * * *': 4 * 60 * 60 * 1000,  // Every 4 hours
      '0 0 * * *': 24 * 60 * 60 * 1000,   // Daily
    }
    
    return patterns[schedule] || 60 * 60 * 1000 // Default to 1 hour
  }

  /**
   * Execute a scheduled job
   */
  async executeJob(jobId: string): Promise<ProcessorResult | null> {
    const job = this.jobs.get(jobId)
    if (!job || job.isRunning) return null

    const processor = this.processors.get(job.processor)
    if (!processor) {
      console.error(`Processor ${job.processor} not found for job ${jobId}`)
      return null
    }

    // Set a timeout to prevent jobs from running indefinitely
    const jobTimeout = 30 * 60 * 1000 // 30 minutes timeout
    let timeoutHandle: NodeJS.Timeout | undefined

    job.isRunning = true
    job.lastRun = new Date()

    const execution: JobExecution = {
      jobId,
      stage: job.stage,
      startTime: new Date()
    }

    this.runningJobs.set(jobId, execution)

    try {
      console.log(`[Scheduler] Starting job ${jobId} (${job.stage})`)
      
      // Set timeout to force cleanup if job hangs
      timeoutHandle = setTimeout(() => {
        console.error(`[Scheduler] Job ${jobId} timed out after ${jobTimeout}ms`)
        job.isRunning = false
        this.runningJobs.delete(jobId)
      }, jobTimeout)
      
      const result = await processor.process()
      
      // Clear timeout if job completes normally
      clearTimeout(timeoutHandle)
      
      execution.endTime = new Date()
      execution.result = result
      
      console.log(`[Scheduler] Job ${jobId} completed: ${result.processed} processed, ${result.failed} failed`)
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // Clear timeout on error
      if (timeoutHandle) clearTimeout(timeoutHandle)
      
      execution.endTime = new Date()
      execution.error = errorMessage
      
      console.error(`[Scheduler] Job ${jobId} failed: ${errorMessage}`)
      
      return null
    } finally {
      // Ensure cleanup always happens
      job.isRunning = false
      this.runningJobs.delete(jobId)
      if (timeoutHandle) clearTimeout(timeoutHandle)
    }
  }

  /**
   * Execute a job manually (on-demand)
   */
  async executeManualJob(
    stage: ProcessingStage,
    options: { limit?: number; singleId?: number; filters?: ProcessingFilters; batchSize?: number } = {}
  ): Promise<ProcessorResult | null> {
    const processorMap: Record<ProcessingStage, string> = {
      'email_validation': 'email_validator',
      'contact_classification': 'contact_classification',
      'contact_overview': 'contact_overview',
      'company_overview': 'company_overview',
      'company_web_crawler': 'company_web_crawler',
      'contact_search': 'contact_search',
      'email_generation': 'email_generation'
    }

    const processorName = processorMap[stage]
    const processor = this.processors.get(processorName)
    
    if (!processor) {
      console.error(`Processor for stage ${stage} not found`)
      return null
    }

    // Generate unique job ID
    const jobId = `manual_${stage}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Check if manual job for this stage is already running
    const existingManualJob = Array.from(this.runningJobs.values()).find(
      job => job.stage === stage && job.jobId.startsWith('manual_')
    )
    
    if (existingManualJob) {
      console.warn(`[Scheduler] Manual job for stage ${stage} already running (${existingManualJob.jobId})`)
      return null
    }

    // Create a processor instance copy to avoid shared state issues
    const ProcessorClass = processor.constructor as new () => BaseProcessor
    const processorInstance = new ProcessorClass()
    
    // Update processor options on the copy
    if (options.limit) processorInstance['options'].limit = options.limit
    if (options.singleId) processorInstance['options'].singleId = options.singleId
    if (options.filters) processorInstance['options'].filters = options.filters
    
    // Process in batches if batch size is specified
    if (options.batchSize && options.batchSize > 0) {
      processorInstance['options'].batchSize = options.batchSize
    }
    
    const execution: JobExecution = {
      jobId,
      stage,
      startTime: new Date()
    }

    this.runningJobs.set(jobId, execution)

    try {
      console.log(`[Scheduler] Starting manual job ${jobId} (${stage})`)
      if (options.filters) {
        console.log(`[Scheduler] Applying filters:`, options.filters)
      }
      if (options.batchSize) {
        console.log(`[Scheduler] Processing in batches of ${options.batchSize}`)
      }
      
      const result = await processorInstance.process()
      
      execution.endTime = new Date()
      execution.result = result
      
      console.log(`[Scheduler] Manual job ${jobId} completed: ${result.processed} processed, ${result.successful} successful, ${result.failed} failed`)
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      execution.endTime = new Date()
      execution.error = errorMessage
      
      console.error(`[Scheduler] Manual job ${jobId} failed: ${errorMessage}`)
      
      return null
    } finally {
      // Ensure cleanup always happens
      this.runningJobs.delete(jobId)
    }
  }

  /**
   * Get all jobs
   */
  getJobs(): ScheduledJob[] {
    return Array.from(this.jobs.values())
  }

  /**
   * Get running jobs
   */
  getRunningJobs(): JobExecution[] {
    return Array.from(this.runningJobs.values())
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): ScheduledJob | null {
    return this.jobs.get(jobId) || null
  }

  /**
   * Detect and clean up stuck jobs
   */
  cleanupStuckJobs(): void {
    const maxRunTime = 45 * 60 * 1000 // 45 minutes
    const now = new Date()
    
    console.log('[Scheduler] Checking for stuck jobs...')
    
    let cleanedCount = 0
    
    // Check scheduled jobs
    for (const job of this.jobs.values()) {
      if (job.isRunning && job.lastRun) {
        const runTime = now.getTime() - job.lastRun.getTime()
        if (runTime > maxRunTime) {
          console.warn(`[Scheduler] Cleaning up stuck scheduled job ${job.id} (running for ${Math.round(runTime / 60000)} minutes)`)
          job.isRunning = false
          cleanedCount++
        }
      }
    }
    
    // Check manual jobs in runningJobs map
    for (const [jobId, execution] of this.runningJobs.entries()) {
      const runTime = now.getTime() - execution.startTime.getTime()
      if (runTime > maxRunTime) {
        console.warn(`[Scheduler] Cleaning up stuck manual job ${jobId} (running for ${Math.round(runTime / 60000)} minutes)`)
        this.runningJobs.delete(jobId)
        cleanedCount++
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`[Scheduler] Cleaned up ${cleanedCount} stuck jobs`)
    } else {
      console.log('[Scheduler] No stuck jobs found')
    }
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    // Clear all intervals
    for (const interval of this.scheduleIntervals.values()) {
      clearInterval(interval)
    }
    this.scheduleIntervals.clear()
  }

  /**
   * Start the scheduler
   */
  start(): void {
    console.log('[Scheduler] Starting processor scheduler')
    
    // Clean up any stuck jobs from previous sessions
    this.cleanupStuckJobs()
    
    // Schedule all enabled jobs
    for (const job of this.jobs.values()) {
      if (job.enabled) {
        this.scheduleJob(job)
      }
    }
    
    // Set up periodic cleanup of stuck jobs every 15 minutes
    setInterval(() => {
      this.cleanupStuckJobs()
    }, 15 * 60 * 1000)
  }
}

// Global scheduler instance - initialized but not auto-started
export const processorScheduler = new ProcessorScheduler()
// Commented out auto-start
// processorScheduler.start() 