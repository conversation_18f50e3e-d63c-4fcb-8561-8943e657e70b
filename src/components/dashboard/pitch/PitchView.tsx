"use client"

import { CREPitchPresentation } from './CREPitchPresentation'

export default function PitchView() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-black p-8">
      <div className="max-w-[1400px] mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-syne font-light mb-6
            bg-clip-text text-transparent bg-gradient-to-r 
            from-blue-400 via-blue-500 to-indigo-600
            [text-shadow:_0_0_30px_rgba(59,130,246,0.3)]">
            ANAX Capital Advisory
          </h1>
          <div className="h-1 w-48 mx-auto bg-gradient-to-r from-blue-500 via-blue-400 to-indigo-500 rounded-full 
            shadow-[0_0_15px_rgba(59,130,246,0.5)] mb-6"></div>
          <p className="text-lg text-blue-200/90 max-w-2xl mx-auto font-light">
            Reinventing Commercial Real Estate Financing with Tech & AI
          </p>
        </div>
        <CREPitchPresentation />
      </div>
    </div>
  )
} 