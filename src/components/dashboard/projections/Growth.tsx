import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useState } from 'react';

const scenarios = {
  base: {
    name: 'Base Case',
    data: Array.from({ length: 19 }, (_, i) => {
      const month = i;
      let revenue;
      if (month <= 6) {
        // Linear growth from 150k to 200k in first 6 months
        revenue = 150000 + ((200000 - 150000) * month / 6);
      } else {
        // Exponential growth from 200k to 1M in next 12 months
        const progress = (month - 6) / 12;
        revenue = 200000 + ((1000000 - 200000) * Math.pow(progress, 2));
      }
      return {
        month: new Date(2025, 1 + month, 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        revenue: Math.round(revenue)
      };
    })
  },
  conservative: {
    name: 'Conservative',
    data: Array.from({ length: 19 }, (_, i) => {
      const month = i;
      let revenue;
      if (month <= 6) {
        revenue = 150000 + ((175000 - 150000) * month / 6);
      } else {
        const progress = (month - 6) / 12;
        revenue = 175000 + ((500000 - 175000) * Math.pow(progress, 2));
      }
      return {
        month: new Date(2025, 1 + month, 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        revenue: Math.round(revenue)
      };
    })
  },
  aggressive: {
    name: 'Aggressive',
    data: Array.from({ length: 19 }, (_, i) => {
      const month = i;
      let revenue;
      if (month <= 6) {
        revenue = 150000 + ((250000 - 150000) * month / 6);
      } else {
        const progress = (month - 6) / 12;
        revenue = 250000 + ((1500000 - 250000) * Math.pow(progress, 2));
      }
      return {
        month: new Date(2025, 1 + month, 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        revenue: Math.round(revenue)
      };
    })
  }
};

export const Growth = () => {
  const [selectedScenario, setSelectedScenario] = useState('base');

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Revenue Growth Projections</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <RadioGroup 
              value={selectedScenario} 
              onValueChange={setSelectedScenario}
              className="flex space-x-4"
            >
              {Object.entries(scenarios).map(([key, scenario]) => (
                <div key={key} className="flex items-center space-x-2">
                  <RadioGroupItem value={key} id={key} />
                  <Label htmlFor={key}>{scenario.name}</Label>
                </div>
              ))}
            </RadioGroup>

            <div className="mb-4">
              <p className="text-blue-800 text-base leading-relaxed bg-blue-50 rounded-lg p-4 border border-blue-100">
                Projections based on all 3 funnels (Outbound Linkedin, Outbound Email, Inbound from Marketing) based on 1% transaction fee. We also plan to add investment pools in partnership with strategic investors where we would earn carry in addition to the transaction fee - that revenue is not projected here.
              </p>
            </div>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={scenarios[selectedScenario].data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis 
                    tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip 
                    formatter={(value) => [`$${(value as number).toLocaleString()}`, "Revenue"]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#2563eb" 
                    name="Monthly Revenue" 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 