import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import { CompanyProcessingState } from '../../types/processing'
import { COMPANY_OVERVIEW_SYSTEM_PROMPT, COMPANY_OVERVIEW_USER_TEMPLATE_FUNCTION } from '../prompts'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import OpenAI from 'openai'

interface CompanyData extends EntityData {
  company_id: number
  company_name: string
  company_website: string
  industry?: string
  processing_state: CompanyProcessingState
}

// Updated data model to match Python script's comprehensive structure
interface CompanyOverviewData {
  // Company Profile
  companyName?: string
  companyType?: string
  businessModel?: string
  fundSize?: string
  aum?: string
  numberOfProperties?: string | number
  headquarters?: string
  numberOfOffices?: string | number
  foundedYear?: string | number
  numberOfEmployees?: string | number
  investmentFocus?: string[]
  geographicFocus?: string[]
  
  // Raw JSON storage for complex nested objects
  _rawPartnerships?: string
  
  // Investment Strategy
  mission?: string
  approach?: string
  targetReturn?: string
  propertyTypes?: string[]
  strategies?: string[]
  assetClasses?: string[]
  valueCreation?: string[]
  
  // Investment Criteria
  targetMarkets?: string[]
  dealSize?: string
  minimumDealSize?: string
  maximumDealSize?: string
  holdPeriod?: string
  riskProfile?: string
  propertySubcategories?: string[]
  assetTypes?: string[]
  loanTypes?: string[]
  
  // Track Record
  totalTransactions?: string | number
  totalSquareFeet?: string | number
  totalUnits?: string | number
  historicalReturns?: string
  portfolioValue?: string
  
  // Financial & Other
  capitalSources?: string[]
  financialProducts?: string[]
  recentDeals?: any[]
  partnerships?: string[]
  
  // Contact Information
  website?: string
  linkedin_url?: string
  mainPhone?: string
  mainEmail?: string
  socialMedia?: any
  
  // Executive Team
  executiveTeam?: ExecutiveTeamMember[]
  
  // Additional data
  notes?: string
}

interface ExecutiveTeamMember {
  first_name?: string
  last_name?: string
  full_name?: string
  title?: string
  headline?: string
  seniority?: string
  email?: string
  personal_email?: string
  email_status?: string
  linkedin_url?: string
  contact_city?: string
  contact_state?: string
  contact_country?: string
  phone?: string
  bio?: string
  category?: string
}

export class CompanyOverviewProcessor extends BaseProcessor {
  private llmProvider;
  private queryParams: any[] = []
  private queries: string[] = []
  private companiesProcessed: number = 0

  constructor(options: ProcessorOptions = {}) {
    super('CompanyOverview', options)
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      process.env.PERPLEXITY_API_KEY,
      { 
        temperature: 0.1,
        maxTokens: 8000,  // Increased for comprehensive responses
      }
    );
  }

  async getUnprocessedEntities(): Promise<CompanyData[]> {
    // Updated query to match Python script's approach for selecting unprocessed companies
    const companies = await this.getCompaniesForOverview(
      this.options.limit,
      this.options.singleId
    )

    return companies.map(company => ({
      id: company.company_id as number,
      company_id: company.company_id as number,
      company_name: company.company_name as string,
      company_website: company.company_website as string,
      industry: company.industry as string,
      processing_state: company.processing_state as CompanyProcessingState
    }))
  }

  async processEntity(entity: CompanyData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Extracting overview for company: ${entity.company_name}`)

      // Fetch scraped content for this company
      const companyContent = await this.getCompanyContent(entity.company_id)


      if (companyContent.length === 0) {
        this.log('warn', `No content found for company ${entity.company_id}`);
        return { success: false, error: 'No content found' }
      }

      // Extract company overview using AI
      const overviewData = await this.extractCompanyOverview(entity, companyContent)
      
      if (!overviewData) {
        this.log('warn', `Failed to extract overview for company ${entity.company_id}`);
        return { success: false, error: 'Failed to extract overview' }
      }

      // Store extracted data
      await this.storeCompanyOverview(entity.company_id, overviewData)
      
      // If executive team data is available, store it as well
      if (overviewData.executiveTeam && overviewData.executiveTeam.length > 0) {
        try {
          await this.storeExecutiveTeam(entity.company_id, overviewData.executiveTeam)
        } catch (teamError) {
          this.log('warn', `Error storing executive team for company ${entity.company_id}: ${teamError}`);
          // Continue processing even if team storage fails
        }
      }

      this.log('info', `Successfully extracted overview for ${entity.company_name}`)

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing company ${entity.company_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean): Promise<void> {
    if (success) {
      // Check if any data was actually saved
      const checkSql = `
        SELECT COUNT(*) as data_count
        FROM company_extracted_data
        WHERE company_id = $1
      `;
      
      const result = await this.query(checkSql, [entityId]);
      const dataCount = parseInt(String(result[0]?.data_count) || '0', 10);
      
      // Only update if we have saved actual data
      if (dataCount > 0) {
          await this.updateCompanyOverviewStatus(entityId, 'completed');
          this.log('info', `Company ${entityId} marked as processed with extracted data saved`);
      } else {
        this.log('warn', `Not marking company ${entityId} as processed: no extracted data was saved`);
        
        // For companies without saved data, still update the timestamp but don't mark as processed
        const updateSql = `
          UPDATE companies 
          SET updated_at = NOW()
          WHERE company_id = $1
        `;
        
        await this.query(updateSql, [entityId]);
      }
    } else {
      // For failed processing, don't mark as processed
      await this.updateCompanyOverviewStatus(entityId, 'failed',  "Failed to update overview");
      this.log('info', `Company ${entityId} processing failed, not marking as processed`);
    }
  }

  /**
   * Extract company overview using OpenAI
   */
  private async extractCompanyOverview(
    company: CompanyData, 
    content: string
  ): Promise<CompanyOverviewData | null> {
    try {
      const messages = this.buildExtractionMessages(company, content);

      // Use the LLM provider with fallback support
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.1,
        maxTokens: 8000  // Increased to match constructor setting for comprehensive responses
      });

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Extracted overview using ${response.provider} (${response.model})`);

      // Parse the structured response
      return this.parseOverviewResponse(response.content);
    } catch (error) {
      this.log('error', `Error calling LLM extraction API: ${error}`);
      return null;
    }
  }

  /**
   * Build messages for LLM company overview extraction
   */
  private buildExtractionMessages(
    company: CompanyData, 
    content: string
  ): LLMMessage[] {
    const userPrompt = COMPANY_OVERVIEW_USER_TEMPLATE_FUNCTION(company, content);

    return [
      { role: 'system', content: COMPANY_OVERVIEW_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  /**
   * Parse OpenAI response to extract company overview data
   * Improved to handle the full data model
   */
  private parseOverviewResponse(content: string): CompanyOverviewData | null {
    try {
      this.log('debug', `Parsing LLM response content (length: ${content.length})`);
      
      // Clean the content and extract JSON
      let jsonContent = content.trim();
      
      // Handle Perplexity format: JSON comes AFTER </think> tag, not inside it
      const thinkMatch = jsonContent.match(/<think>[\s\S]*?<\/think>\s*([\s\S]*)/);
      if (thinkMatch) {
        // Extract content that comes AFTER the </think> tag
        const afterThinkContent = thinkMatch[1].trim();
        this.log('debug', `Extracted content after <think> tags (${afterThinkContent.length} chars)`);
        
        if (afterThinkContent) {
          jsonContent = afterThinkContent;
          this.log('debug', `Using content after </think> tag`);
        } else {
          throw new Error('No content found after </think> tag');
        }
      } else {
        // Remove any thinking tags if they appear without content
        jsonContent = jsonContent.replace(/<think>[\s\S]*?<\/think>/g, '');
        
        // Remove any leading/trailing text that isn't JSON
        jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '');
        
        // Try to extract from markdown code blocks
        const markdownJsonMatch = jsonContent.match(/```json\s*([\s\S]*?)\s*```/);
        if (markdownJsonMatch) {
          jsonContent = markdownJsonMatch[1].trim();
          this.log('debug', `Extracted JSON from markdown code block`);
        } else {
          // Look for the main JSON object
          const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
          if (!jsonMatch) {
            throw new Error('No JSON found in response');
          }
          jsonContent = jsonMatch[0];
          this.log('debug', `Extracted JSON using direct object match`);
        }
      }

      // Log the extracted JSON for debugging (truncated for readability)
      const jsonPreview = jsonContent.length > 500 ? jsonContent.substring(0, 500) + '...' : jsonContent;
      this.log('debug', `Extracted JSON text: ${jsonPreview}`);
      
      // Try to parse the JSON, handling potential truncation
      let result: Record<string, any>;
      try {
        result = JSON.parse(jsonContent);
      } catch (parseError) {
        // If JSON is truncated, try to fix common issues
        this.log('warn', `Initial JSON parse failed: ${parseError}. Attempting to fix truncated JSON...`);
        
        // Try to fix truncated JSON by closing incomplete structures
        const fixedJson = this.attemptJsonFix(jsonContent);
        if (fixedJson) {
          result = JSON.parse(fixedJson);
          this.log('info', `Successfully parsed fixed JSON`);
        } else {
          throw parseError;
        }
      }
      
      // Log what keys are available in the result
      this.log('debug', `Response keys: ${Object.keys(result).join(', ')}`);
      
      // Handle nested structure (similar to Python script)
      const companyProfile = result.companyProfile || {}
      const investmentStrategy = result.investmentStrategy || {}
      const investmentCriteria = result.investmentCriteria || {}
      const trackRecord = result.trackRecord || {}
      const contactInfo = result.contactInfo || {}
      const executiveTeam = result.executiveTeam || []
      
      // Build the comprehensive data structure
      const overviewData: CompanyOverviewData = {
        // Company Profile
        companyName: companyProfile.companyName || result.companyName || result.companyname,
        companyType: companyProfile.companyType || result.companyType || result.companytype,
        businessModel: companyProfile.businessModel || result.businessModel || result.businessmodel,
        fundSize: companyProfile.fundSize || result.fundSize || result.fundsize,
        aum: companyProfile.aum || result.aum,
        numberOfProperties: companyProfile.numberOfProperties || result.numberOfProperties || result.numberofproperties,
        headquarters: companyProfile.headquarters || result.headquarters,
        numberOfOffices: companyProfile.numberOfOffices || result.numberOfOffices || result.numberofoffices,
        foundedYear: companyProfile.foundedYear || result.foundedYear || result.foundedyear,
        numberOfEmployees: companyProfile.numberOfEmployees || result.numberOfEmployees || result.numberofemployees,
        investmentFocus: this.ensureArray(companyProfile.investmentFocus || result.investmentFocus || result.investmentfocus),
        geographicFocus: this.ensureArray(companyProfile.geographicFocus || result.geographicFocus || result.geographicfocus),
        
        // Investment Strategy
        mission: investmentStrategy.mission || result.mission,
        approach: investmentStrategy.approach || result.approach,
        targetReturn: investmentStrategy.targetReturn || result.targetReturn || result.targetreturn,
        propertyTypes: this.ensureArray(investmentStrategy.propertyTypes || result.propertyTypes || result.propertytypes),
        strategies: this.ensureArray(investmentStrategy.strategies || result.strategies),
        assetClasses: this.ensureArray(investmentStrategy.assetClasses || result.assetClasses || result.assetclasses),
        valueCreation: this.ensureArray(investmentStrategy.valueCreation || result.valueCreation || result.valuecreation),
        
        // Investment Criteria
        targetMarkets: this.ensureArray(investmentCriteria.targetMarkets || result.targetMarkets || result.targetmarkets),
        dealSize: investmentCriteria.dealSize || result.dealSize || result.dealsize,
        minimumDealSize: investmentCriteria.minimumDealSize || result.minimumDealSize || result.minimumdealsize,
        maximumDealSize: investmentCriteria.maximumDealSize || result.maximumDealSize || result.maximumdealsize,
        holdPeriod: investmentCriteria.holdPeriod || result.holdPeriod || result.holdperiod,
        riskProfile: investmentCriteria.riskProfile || result.riskProfile || result.riskprofile,
        propertySubcategories: this.ensureArray(investmentCriteria.propertySubcategories || result.propertySubcategories || result.propertysubcategories),
        assetTypes: this.ensureArray(investmentCriteria.assetTypes || result.assetTypes || result.assettypes),
        loanTypes: this.ensureArray(investmentCriteria.loanTypes || result.loanTypes || result.loantypes),
        
        // Track Record
        totalTransactions: trackRecord.totalTransactions || result.totalTransactions || result.totaltransactions,
        totalSquareFeet: trackRecord.totalSquareFeet || result.totalSquareFeet || result.totalsquarefeet,
        totalUnits: trackRecord.totalUnits || result.totalUnits || result.totalunits,
        historicalReturns: trackRecord.historicalReturns || result.historicalReturns || result.historicalreturns,
        portfolioValue: trackRecord.portfolioValue || result.portfolioValue || result.portfoliovalue,
        
        // Financial & Other
        capitalSources: this.ensureArray(result.capitalSources || result.capitalsources),
        financialProducts: this.ensureArray(result.financialProducts || result.financialproducts),
        recentDeals: this.ensureArray(result.recentDeals || result.recentdeals),
        partnerships: this.ensureArray(result.partnerships),
        
        // Special handling for complex objects that need to be properly serialized
        _rawPartnerships: this.processComplexArray(result.partnerships),
        
        // Contact Information
        website: contactInfo.website,
        linkedin_url: contactInfo.socialMedia?.linkedin || contactInfo.linkedin_url,
        mainPhone: contactInfo.mainPhone,
        mainEmail: contactInfo.mainEmail,
        socialMedia: contactInfo.socialMedia,
        
        // Executive Team (normalized to match database structure)
        executiveTeam: Array.isArray(executiveTeam) ? executiveTeam.map(member => this.normalizeTeamMember(member)) : [],
        
        // Additional
        notes: result.notes
      }

      return this.cleanNullValues(overviewData)
    } catch (error) {
      this.log('error', `Error parsing overview response: ${error}`);
      
      // Log a portion of the content for debugging
      const contentPreview = content.length > 1500 ? content.substring(0, 1500) + '...' : content;
      this.log('debug', `Response content preview: ${contentPreview}`);
      
      return null;
    }
  }

  /**
   * Clean out null, undefined, and empty values from an object
   */
  private cleanNullValues<T>(obj: T): T {
    const result = { ...obj } as any
    
    for (const key in result) {
      const value = result[key]
      
      // Remove null/undefined values
      if (value === null || value === undefined) {
        delete result[key]
        continue
      }
      
      // Handle arrays - remove empty arrays and clean arrays of objects
      if (Array.isArray(value)) {
        if (value.length === 0) {
          delete result[key]
        } else if (typeof value[0] === 'object') {
          result[key] = value.map(item => this.cleanNullValues(item))
            .filter(item => Object.keys(item).length > 0)
          
          if (result[key].length === 0) {
            delete result[key]
          }
        }
      }
      
      // Handle objects recursively
      else if (typeof value === 'object') {
        result[key] = this.cleanNullValues(value)
        if (Object.keys(result[key]).length === 0) {
          delete result[key]
        }
      }
      
      // Handle empty strings
      else if (typeof value === 'string' && value.trim() === '') {
        delete result[key]
      }
    }
    
    return result as T
  }

  /**
   * Ensure a value is an array of strings
   */
  private ensureArray(value: any): string[] {
    if (!value) return []
    if (Array.isArray(value)) {
      return value.filter(Boolean).map(v => {
        if (typeof v === 'object' && v !== null) {
          // For objects, convert to JSON string to avoid [object Object]
          return JSON.stringify(v);
        }
        return String(v);
      });
    }
    return [String(value)]
  }
  
  /**
   * Handle complex nested structures like partnerships
   * Ensures proper object serialization for database storage
   */
  private processComplexArray(value: any): string | undefined {
    if (!value || (Array.isArray(value) && value.length === 0)) return undefined;
    
    // If it's already a string, assume it's serialized JSON
    if (typeof value === 'string') return value;
    
    // Otherwise, serialize it properly
    return JSON.stringify(value);
  }

  /**
   * Normalize team member data to match database structure
   */
  private normalizeTeamMember(member: any): ExecutiveTeamMember {
    return {
      first_name: member.firstName || member.first_name,
      last_name: member.lastName || member.last_name,
      full_name: member.fullName || member.full_name || `${member.firstName || ''} ${member.lastName || ''}`.trim(),
      title: member.title || member.position,
      headline: member.headline,
      seniority: member.seniority,
      email: member.email,
      personal_email: member.personalEmail || member.personal_email,
      email_status: member.emailStatus || member.email_status,
      linkedin_url: member.linkedinUrl || member.linkedin_url || member.linkedin,
      contact_city: member.city || member.contact_city,
      contact_state: member.state || member.contact_state,
      contact_country: member.country || member.contact_country,
      phone: member.phone || member.phoneNumber,
      bio: member.bio || member.biography,
      category: member.category || 'Executive'
    }
  }

  /**
   * Store extracted company overview in database
   * Updated to match Python script's comprehensive data model
   */
  private async storeCompanyOverview(companyId: number, data: CompanyOverviewData): Promise<void> {
    this.log('debug', `Storing overview data for company ${companyId} with name: ${data.companyName || 'unknown'}, type: ${data.companyType || 'unknown'}`);
    // Create a new object without the notes field to avoid database errors
    const { notes, _rawPartnerships, ...dataWithoutNotes } = data;
    
    const sql = `
      INSERT INTO company_extracted_data (
        company_id, companyname, companytype, businessmodel, fundsize, aum,
        numberOfProperties, headquarters, numberOfOffices, foundedYear, numberOfEmployees,
        investmentfocus, geographicfocus, recentdeals,
        mission, approach, targetreturn, propertytypes, strategies, assetclasses, valueCreation,
        targetmarkets, dealsize, minimumDealSize, maximumDealSize, holdperiod, riskprofile,
        capitalSources, financialProducts,
        totalTransactions, totalSquareFeet, totalUnits, historicalReturns, portfolioValue,
        partnerships, website, linkedin_url, mainPhone, mainEmail, socialMedia,
        investment_criteria_property_types, investment_criteria_property_subcategories,
        investment_criteria_asset_types, investment_criteria_loan_types
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21,
        $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40,
        $41, $42, $43, $44
      )
      ON CONFLICT (company_id) 
      DO UPDATE SET 
        companyname = EXCLUDED.companyname,
        companytype = EXCLUDED.companytype,
        businessmodel = EXCLUDED.businessmodel,
        fundsize = EXCLUDED.fundsize,
        aum = EXCLUDED.aum,
        numberOfProperties = EXCLUDED.numberOfProperties,
        headquarters = EXCLUDED.headquarters,
        numberOfOffices = EXCLUDED.numberOfOffices,
        foundedYear = EXCLUDED.foundedYear,
        numberOfEmployees = EXCLUDED.numberOfEmployees,
        investmentfocus = EXCLUDED.investmentfocus,
        geographicfocus = EXCLUDED.geographicfocus,
        recentdeals = EXCLUDED.recentdeals,
        mission = EXCLUDED.mission,
        approach = EXCLUDED.approach,
        targetreturn = EXCLUDED.targetreturn,
        propertytypes = EXCLUDED.propertytypes,
        strategies = EXCLUDED.strategies,
        assetclasses = EXCLUDED.assetclasses,
        valueCreation = EXCLUDED.valueCreation,
        targetmarkets = EXCLUDED.targetmarkets,
        dealsize = EXCLUDED.dealsize,
        minimumDealSize = EXCLUDED.minimumDealSize, 
        maximumDealSize = EXCLUDED.maximumDealSize,
        holdperiod = EXCLUDED.holdperiod,
        riskprofile = EXCLUDED.riskprofile,
        capitalSources = EXCLUDED.capitalSources,
        financialProducts = EXCLUDED.financialProducts,
        totalTransactions = EXCLUDED.totalTransactions,
        totalSquareFeet = EXCLUDED.totalSquareFeet,
        totalUnits = EXCLUDED.totalUnits,
        historicalReturns = EXCLUDED.historicalReturns,
        portfolioValue = EXCLUDED.portfolioValue,
        partnerships = EXCLUDED.partnerships,
        website = EXCLUDED.website,
        linkedin_url = EXCLUDED.linkedin_url,
        mainPhone = EXCLUDED.mainPhone,
        mainEmail = EXCLUDED.mainEmail,
        socialMedia = EXCLUDED.socialMedia,
        investment_criteria_property_types = EXCLUDED.investment_criteria_property_types,
        investment_criteria_property_subcategories = EXCLUDED.investment_criteria_property_subcategories,
        investment_criteria_asset_types = EXCLUDED.investment_criteria_asset_types,
        investment_criteria_loan_types = EXCLUDED.investment_criteria_loan_types
    `
    
    // Prepare parameters array with proper JSON serialization for arrays
    const params = [
      companyId,
      data.companyName,
      data.companyType,
      data.businessModel,
      data.fundSize,
      data.aum,
      data.numberOfProperties,
      data.headquarters,
      data.numberOfOffices,
      data.foundedYear,
      data.numberOfEmployees,
      data.investmentFocus ? JSON.stringify(data.investmentFocus) : null,
      data.geographicFocus ? JSON.stringify(data.geographicFocus) : null,
      data.recentDeals ? JSON.stringify(data.recentDeals) : null,
      data.mission,
      data.approach,
      data.targetReturn,
      data.propertyTypes ? JSON.stringify(data.propertyTypes) : null,
      data.strategies ? JSON.stringify(data.strategies) : null,
      data.assetClasses ? JSON.stringify(data.assetClasses) : null,
      data.valueCreation ? JSON.stringify(data.valueCreation) : null,
      data.targetMarkets ? JSON.stringify(data.targetMarkets) : null,
      data.dealSize,
      data.minimumDealSize,
      data.maximumDealSize,
      data.holdPeriod,
      data.riskProfile,
      data.capitalSources ? JSON.stringify(data.capitalSources) : null,
      data.financialProducts ? JSON.stringify(data.financialProducts) : null,
      data.totalTransactions,
      data.totalSquareFeet,
      data.totalUnits,
      data.historicalReturns,
      data.portfolioValue,
      data.partnerships ? JSON.stringify(data.partnerships) : null,
      data.website,
      data.linkedin_url,
      data.mainPhone,
      data.mainEmail,
      data.socialMedia ? JSON.stringify(data.socialMedia) : null,
      data.propertyTypes ? JSON.stringify(data.propertyTypes) : null,
      data.propertySubcategories ? JSON.stringify(data.propertySubcategories) : null,
      data.assetTypes ? JSON.stringify(data.assetTypes) : null,
      data.loanTypes ? JSON.stringify(data.loanTypes) : null
    ]
    
    await this.query(sql, params)
    
    this.log('debug', `Stored company overview for company ${companyId}`)
  }

  /**
   * Store executive team members
   * New method to handle team data similar to Python script
   */
  private async storeExecutiveTeam(companyId: number, teamMembers: ExecutiveTeamMember[]): Promise<void> {
    if (!teamMembers || teamMembers.length === 0) {
      return
    }
    
    this.log('info', `Storing ${teamMembers.length} executive team members for company ${companyId}`)
    
    for (const member of teamMembers) {
      try {
        // Ensure full_name is present
        if (!member.full_name) {
          if (member.first_name || member.last_name) {
            member.full_name = `${member.first_name || ''} ${member.last_name || ''}`.trim();
          } else {
            this.log('warn', `Skipping team member with no name information for company ${companyId}`);
            continue;
          }
        }
        
        // Check if table exists and has the expected structure
        const checkTableSql = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'company_extracted_contact'
          ) as table_exists
        `;
        
        const tableCheck = await this.query(checkTableSql, []);
        if (!tableCheck[0]?.table_exists) {
          this.log('error', `Table company_extracted_contact does not exist`);
          throw new Error('Table company_extracted_contact does not exist');
        }
        
        // First check if this contact already exists for this company
        const checkSql = `
          SELECT contact_id FROM company_extracted_contact 
          WHERE company_id = $1 AND full_name = $2
        `;
        
        const existingContact = await this.query(checkSql, [companyId, member.full_name]);
        
        let sql: string;
        let params: unknown[];
        
        if (existingContact.length > 0) {
          // Update existing contact
          const contactId = existingContact[0].contact_id;
          sql = `
            UPDATE company_extracted_contact SET
              first_name = $2, last_name = $3, title = $4, headline = $5, seniority = $6,
              email = $7, personal_email = $8, email_status = $9, linkedin_url = $10,
              contact_city = $11, contact_state = $12, contact_country = $13,
              phone = $14, bio = $15, category = $16
            WHERE contact_id = $1
          `;
          params = [
            contactId, member.first_name, member.last_name, member.title, member.headline,
            member.seniority, member.email, member.personal_email, member.email_status,
            member.linkedin_url, member.contact_city, member.contact_state, member.contact_country,
            member.phone, member.bio, member.category || 'Executive'
          ];
        } else {
          // Insert new contact
          sql = `
            INSERT INTO company_extracted_contact (
              company_id, first_name, last_name, full_name, title, headline, seniority, 
              email, personal_email, email_status, linkedin_url, contact_city, 
              contact_state, contact_country, phone, bio, category
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
            )
          `;
          params = [
            companyId, member.first_name, member.last_name, member.full_name, member.title,
            member.headline, member.seniority, member.email, member.personal_email,
            member.email_status, member.linkedin_url, member.contact_city, member.contact_state,
            member.contact_country, member.phone, member.bio, member.category || 'Executive'
          ];
        }
        
        await this.query(sql, params);
        
        this.log('debug', `Stored team member: ${member.full_name}`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.log('error', `Error storing team member ${member.full_name || '[unnamed]'}: ${errorMessage}`);
        // Continue with other team members even if one fails
      }
    }
  }

  /**
   * Attempt to fix truncated JSON by closing incomplete structures
   */
  private attemptJsonFix(jsonContent: string): string | null {
    try {
      // Common truncation patterns and fixes
      let fixed = jsonContent.trim();
      
      this.log('debug', `Attempting to fix JSON. Original length: ${fixed.length}`);
      
      // Remove any incomplete trailing content that might cause parsing issues
      // Look for common truncation patterns
      
      // 1. Remove incomplete string values (strings that don't end with quotes)
      fixed = fixed.replace(/:\s*"[^"]*$/, ': null');
      
      // 2. Remove incomplete object/array elements at the end
      fixed = fixed.replace(/,\s*[^,}\]]*$/, '');
      
      // 3. Remove trailing commas
      fixed = fixed.replace(/,(\s*[}\]])/, '$1');
      
      // 4. Handle incomplete key-value pairs
      fixed = fixed.replace(/:\s*$/, ': null');
      
      // 5. Remove any trailing incomplete content after the last valid character
      const lastValidChar = Math.max(
        fixed.lastIndexOf('}'),
        fixed.lastIndexOf(']'),
        fixed.lastIndexOf('"'),
        fixed.lastIndexOf('null'),
        fixed.lastIndexOf('true'),
        fixed.lastIndexOf('false')
      );
      
      if (lastValidChar > 0) {
        // Find the next structural character after the last valid content
        let cutPoint = lastValidChar + 1;
        for (let i = lastValidChar + 1; i < fixed.length; i++) {
          const char = fixed[i];
          if (char === ',' || char === '}' || char === ']') {
            cutPoint = i + 1;
            break;
          }
          if (char === '"' || char === '{' || char === '[') {
            cutPoint = i;
            break;
          }
        }
        fixed = fixed.substring(0, cutPoint);
      }
      
      // Count open/close braces and brackets
      const openBraces = (fixed.match(/\{/g) || []).length;
      const closeBraces = (fixed.match(/\}/g) || []).length;
      const openBrackets = (fixed.match(/\[/g) || []).length;
      const closeBrackets = (fixed.match(/\]/g) || []).length;
      
      // Close missing brackets and braces
      const missingCloseBrackets = openBrackets - closeBrackets;
      const missingCloseBraces = openBraces - closeBraces;
      
      for (let i = 0; i < missingCloseBrackets; i++) {
        fixed += ']';
      }
      for (let i = 0; i < missingCloseBraces; i++) {
        fixed += '}';
      }
      
      this.log('debug', `JSON fix completed. Added ${missingCloseBrackets} ] and ${missingCloseBraces} }. New length: ${fixed.length}`);
      
      return fixed;
    } catch (error) {
      this.log('debug', `JSON fix attempt failed: ${error}`);
      return null;
    }
  }

} 