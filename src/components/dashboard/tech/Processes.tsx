"use client"

import React from 'react';
import { 
  Users, 
  Building2,
  Search,
  Mail,
  MessageSquare,
  FileText,
  HandshakeIcon,
  ArrowRight,
  Database,
  Phone,
  Calendar,
  Folder,
  CheckSquare,
  LucideIcon
} from 'lucide-react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

interface ProcessStepProps {
  icon: LucideIcon;
  title: string;
  description: string;
  automation?: boolean;
  side: 'left' | 'right';
}

interface ProcessStep {
  icon: LucideIcon;
  title: string;
  description: string;
  automation: boolean;
}

interface ProcessSteps {
  borrower: ProcessStep[];
  capital: ProcessStep[];
}

const Card: React.FC<CardProps> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
);

const ProcessStep: React.FC<ProcessStepProps> = ({ icon: Icon, title, description, automation, side }) => (
  <div className={`flex items-start gap-3 ${automation ? 'opacity-100' : 'opacity-80'}`}>
    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
      ${side === 'left' ? 'bg-blue-50' : 'bg-purple-50'}`}>
      <Icon className={`h-5 w-5 ${side === 'left' ? 'text-blue-600' : 'text-purple-600'}`} />
    </div>
    <div className="flex-1">
      <div className="flex items-center gap-2">
        <h4 className="font-medium text-gray-900">{title}</h4>
        {automation && (
          <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
            <Database className="w-3 h-3 mr-1" />
            Auto
          </span>
        )}
      </div>
      <p className="mt-1 text-sm text-gray-500">{description}</p>
    </div>
  </div>
);

const Processes: React.FC = () => {
  const processSteps: ProcessSteps = {
    borrower: [
      {
        icon: Search,
        title: "Find",
        description: "Market research & lead generation",
        automation: true
      },
      {
        icon: Mail,
        title: "Engage",
        description: "Initial outreach & marketing",
        automation: true
      },
      {
        icon: MessageSquare,
        title: "Meet",
        description: "Deal qualification call",
        automation: false
      },
      {
        icon: HandshakeIcon,
        title: "Retain",
        description: "Secure representation agreement",
        automation: true
      },
      {
        icon: FileText,
        title: "Create OM",
        description: "Prepare offering memorandum",
        automation: true
      }
    ],
    capital: [
      {
        icon: Search,
        title: "Find",
        description: "Identify capital sources",
        automation: true
      },
      {
        icon: Mail,
        title: "Engage",
        description: "Targeted outreach",
        automation: true
      },
      {
        icon: Database,
        title: "Onboard",
        description: "Document investment criteria",
        automation: true
      },
      {
        icon: CheckSquare,
        title: "Match",
        description: "Connect with deals",
        automation: true
      },
      {
        icon: FileText,
        title: "Term Sheet",
        description: "Secure formal interest",
        automation: false
      }
    ]
  };

  interface AutomationArea {
    title: string;
    items: string[];
    bgColor: string;
    textColor: string;
    headerColor: string;
  }

  const automationAreas: AutomationArea[] = [
    {
      title: "Lead Generation",
      items: ["Deal news monitoring", "Contact enrichment"],
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      headerColor: "text-blue-900"
    },
    {
      title: "Engagement",
      items: ["Content creation", "Email campaigns"],
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      headerColor: "text-purple-900"
    },
    {
      title: "Deal Management",
      items: ["OM generation", "Deal matching"],
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      headerColor: "text-green-900"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Business Process Map</h2>
        <p className="mt-1 text-sm text-gray-500">
          Core marketplace workflows and automation opportunities
        </p>
      </div>

      {/* Process Flow */}
      <Card>
        <div className="p-6">
          {/* Title Row */}
          <div className="grid grid-cols-5 gap-6 mb-8">
            <div className="col-span-2">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-900">Borrower Side</h3>
              </div>
            </div>
            <div className="col-span-1 flex items-center justify-center">
              <div className="w-px h-12 bg-gray-200" />
            </div>
            <div className="col-span-2">
              <div className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900">Capital Provider Side</h3>
              </div>
            </div>
          </div>

          {/* Process Steps */}
          <div className="space-y-12">
            {processSteps.borrower.map((step, idx) => (
              <div key={idx} className="grid grid-cols-5 gap-6">
                <div className="col-span-2">
                  <ProcessStep {...step} side="left" />
                </div>
                <div className="col-span-1 flex items-center justify-center">
                  <div className="w-px h-full bg-gray-200" />
                </div>
                <div className="col-span-2">
                  <ProcessStep {...processSteps.capital[idx]} side="right" />
                </div>
              </div>
            ))}
          </div>

          {/* Deal Room Convergence */}
          <div className="mt-12 pt-12 border-t border-gray-200">
            <div className="flex flex-col items-center">
              <Folder className="h-12 w-12 text-green-600 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Deal Room</h3>
              <p className="text-sm text-gray-500 text-center max-w-md">
                Final convergence point where both parties come together to complete documentation and close the deal
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Automation Focus Areas */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Key Automation Priorities</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {automationAreas.map((area, idx) => (
              <div key={idx} className={`${area.bgColor} rounded-lg p-4`}>
                <h4 className={`font-medium ${area.headerColor} mb-3`}>{area.title}</h4>
                <ul className={`space-y-2 text-sm ${area.textColor}`}>
                  {area.items.map((item, itemIdx) => (
                    <li key={itemIdx} className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export { Processes };
export default Processes; 