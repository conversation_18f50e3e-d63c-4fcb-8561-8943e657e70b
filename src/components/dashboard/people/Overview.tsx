"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Users, Building2, MessageSquare, Target } from 'lucide-react'

export const Overview = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Total Contacts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,547</div>
            <p className="text-sm text-gray-500">Active in last 30 days</p>
          </CardContent>
        </Card>
        
        {/* Add more metric cards */}
      </div>
      
      {/* Add more sections */}
    </div>
  )
}