"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { ExternalLink } from 'lucide-react';
import { Contact } from '../types';

interface NotableActivitiesSectionProps {
  contact?: Contact;
}

const NotableActivitiesSection: React.FC<NotableActivitiesSectionProps> = ({ contact }) => {
  if (!Array.isArray(contact?.notable_activities) || contact.notable_activities.length === 0) return null;
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">Notable Activities</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {contact.notable_activities.map((item, index) => {
            let content = '';
            if (typeof item === 'string') {
              content = item;
            } else if (item.title && item.snippet) {
              content = `${item.title}: ${item.snippet}`;
            } else if (item.activity || item.description) {
              content = item.activity || item.description;
            } else {
              content = JSON.stringify(item);
            }
            return (
              <li key={index} className="relative pl-6 text-sm">
                <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
                {content}
                {item.url && (
                  <a 
                    href={item.url} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="ml-1 text-blue-600 hover:underline inline-flex items-center"
                  >
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                )}
                {item.date && (
                  <span className="text-xs text-gray-500 ml-2">({item.date})</span>
                )}
              </li>
            );
          })}
        </ul>
      </CardContent>
    </Card>
  );
};

export default NotableActivitiesSection; 