"use client"

import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import RichTextEditor, { RichTextEditorRef } from '@/components/common/RichTextEditor';
import { toast } from "sonner";
import { 
  Send, 
  RefreshCw,
  CheckCircle2, 
  Eye, 
  FileText,
  Filter
} from 'lucide-react';
import { CampaignSequence, Contact, Message } from '../types';
import { replaceVariables } from '../utils';
import { fetchCampaignSequence, syncToSmartlead } from '../services/campaignService';
import { cn } from '@/lib/utils';

interface CampaignSequenceSectionProps {
  contactId: string | number;
  contact?: Contact;
  campaignId: string;
  campaignName?: string;
  messageData: Message;
  onMessageSaved?: (messageData: any) => void;
}

const CampaignSequenceSection: React.FC<CampaignSequenceSectionProps> = ({
  contactId,
  contact,
  campaignId,
  campaignName,
  messageData,
  onMessageSaved
}) => {
  const [sequence, setSequence] = useState<CampaignSequence | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<string>('');
  const [editedSubject, setEditedSubject] = useState('');
  const [editedBody, setEditedBody] = useState('');
  const [variableValues, setVariableValues] = useState<Record<string, string>>({});
  const [allCampaignVariables, setAllCampaignVariables] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState('template');
  const editorRef = useRef<RichTextEditorRef>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Fetch campaign sequence when campaignId changes
  useEffect(() => {
    if (!campaignId || campaignId === 'all') {
      setSequence(null);
      return;
    }

    async function loadSequence() {
      setLoading(true);
      try {
        const sequenceData = await fetchCampaignSequence(campaignId);
        setSequence(sequenceData);
        
        // If we have sequence variants, select the first one by default
        if (sequenceData?.sequence_variants?.length) {
          setSelectedVariant(sequenceData.sequence_variants[0].variant_label);
          
          // Store the raw template values - don't modify them
          setEditedSubject(sequenceData.sequence_variants[0].subject);
          setEditedBody(sequenceData.sequence_variants[0].email_body);

          // IMPORTANT: Extract variables from ALL sequence variants, not just the selected one
          const allVarsFromAllSequences = extractAllVariablesFromAllSequences(sequenceData.sequence_variants);
          
          // Additionally, extract any template variables defined in the sequence
          const templateVars = sequenceData.variables?.map(v => v.name) || [];
          
          // Combine all variables from all sequences and template variables
          const comprehensiveVariableList = [...new Set([...allVarsFromAllSequences, ...templateVars])];
          
          // Initialize values for ALL variables from ALL sequences
          const initialValues: Record<string, string> = {};
          comprehensiveVariableList.forEach(variable => {
            initialValues[variable] = getVariableValue(variable, contact, messageData);
          });
          
          // Set both the comprehensive variable list and current editing values
          setAllCampaignVariables(initialValues);
          setVariableValues(initialValues);
        }
      } catch (error) {
        console.error('Error loading sequence:', error);
        toast.error('Failed to load campaign sequence');
      } finally {
        setLoading(false);
      }
    }

    loadSequence();
  }, [campaignId, contact, messageData]);

  // NEW FUNCTION: Extract variables from ALL sequence variants
  const extractAllVariablesFromAllSequences = (sequenceVariants: any[]): string[] => {
    const allVariables: string[] = [];
    
    sequenceVariants.forEach(variant => {
      // Extract variables from each variant's subject and body
      const subjectVars = extractVariables(variant.subject || '');
      const bodyVars = extractVariables(variant.email_body || '');
      
      // Add to comprehensive list
      allVariables.push(...subjectVars, ...bodyVars);
    });
    
    // Return unique variables only
    return [...new Set(allVariables)];
  };

  // Set up iframe contents when content changes
  useEffect(() => {
    if (iframeRef.current && iframeRef.current.contentDocument) {
      const doc = iframeRef.current.contentDocument;
      
      // Get the content based on current tab
      let bodyContent = '';
      let subjectContent = '';
      
      if (activeTab === 'preview') {
        // In preview mode, show processed content with variables replaced
        bodyContent = getPreviewContent(editedBody, contact, messageData, variableValues);
        subjectContent = getPreviewContent(editedSubject, contact, messageData, variableValues);
      } else {
        // In template mode, show raw template with variable highlighting
        bodyContent = highlightVariables(editedBody);
        subjectContent = highlightVariables(editedSubject);
      }
      
      // Create comprehensive HTML document
      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              font-size: 14px;
              line-height: 1.6;
              padding: 1rem;
              margin: 0;
              color: #333;
              background: white;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            p { margin: 0 0 1em 0; }
            ul, ol { margin: 0 0 1em 1.5em; padding: 0; }
            li { margin-bottom: 0.5em; }
            img { max-width: 100%; height: auto; }
            a { color: #0070f3; text-decoration: none; }
            a:hover { text-decoration: underline; }
          </style>
        </head>
        <body>
          <div class="content-wrapper">
            ${bodyContent || '<p style="color: #999; font-style: italic;">No content to preview</p>'}
          </div>
        </body>
        </html>
      `;
      
      try {
        doc.open();
        doc.write(content);
        doc.close();
        
        // Ensure iframe content is properly sized
        setTimeout(() => {
          if (iframeRef.current && iframeRef.current.contentDocument) {
            const body = iframeRef.current.contentDocument.body;
            if (body) {
              body.style.margin = '0';
              body.style.padding = '16px';
            }
          }
        }, 100);
        
      } catch (error) {
        console.error('Error updating iframe:', error);
        // Fallback: write simple content
        try {
          doc.open();
          doc.write(`<html><body style="font-family: sans-serif; padding: 16px; margin: 0;">${bodyContent || 'Content unavailable'}</body></html>`);
          doc.close();
        } catch (fallbackError) {
          console.error('Fallback iframe update failed:', fallbackError);
        }
      }
    }
  }, [editedBody, editedSubject, activeTab, contact, messageData, variableValues]);

  // Helper function to highlight variables in template mode
  const highlightVariables = (template: string): string => {
    if (!template) return '';
    
    return template.replace(/{{\s*([^}]+)\s*}}/g, (match, variable) => {
      return `<span class="variable-highlight">{{\u00A0${variable.trim()}\u00A0}}</span>`;
    });
  };

  // Update the content when the selected variant changes
  const handleVariantChange = (variantLabel: string) => {
    if (!sequence) return;
    
    const variant = sequence.sequence_variants.find(v => v.variant_label === variantLabel);
    if (variant) {
      setSelectedVariant(variantLabel);
      
      // Store the raw template values without modification
      setEditedSubject(variant.subject);
      setEditedBody(variant.email_body);
      
      // IMPORTANT: Keep the comprehensive variable list from all sequences
      // Don't reset variableValues - keep all variables from all sequences loaded
      // This way when users switch between variants, all variables remain available
      
      // Only update values if they don't already exist (preserve user edits)
      const currentVariantVars = extractVariables(variant.subject + ' ' + variant.email_body);
      const updatedValues = { ...variableValues };
      
      currentVariantVars.forEach(variable => {
        // Only set if not already defined (preserve existing values)
        if (updatedValues[variable] === undefined) {
          updatedValues[variable] = getVariableValue(variable, contact, messageData);
        }
      });
      
      setVariableValues(updatedValues);
    }
  };

  // Handle tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Update variable value
  const handleVariableChange = (variable: string, value: string) => {
    setVariableValues(prev => ({
      ...prev,
      [variable]: value
    }));
  };

  // Save and optionally sync the message
  const handleSaveMessage = async (sync: boolean = false) => {
    if (!contactId || !campaignId) {
      toast.error('Missing contact or campaign ID');
      return;
    }

    // Use the raw templates for subject and body (with variables intact)
    const subjectTemplate = "";
    const bodyTemplate = "";

    setSaving(true);
    try {
      // First, save the message to our local database
      const response = await fetch(`/api/messages?contact_id=${contactId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          direction: 'outbound',
          from_email: '<EMAIL>',
          to_email: contact?.email,
          role: 'user',
          smartlead_campaign_id: campaignId,
          // Include metadata for reference and variable values
          metadata: {
            campaign_id: campaignId,
            campaign_name: campaignName,
            variant_label: selectedVariant,
            variables: variableValues,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save message: ${response.status}`);
      }

      const savedMessageData = await response.json();
      
      // Notify parent component
      if (onMessageSaved) {
        onMessageSaved(savedMessageData);
      }

      toast.success('Message saved successfully');

      // If sync is true, also sync to Smartlead
      if (sync) {
        // Prepare custom fields - include all variable values for Smartlead
        const customFields: Record<string, string> = {
          // Include variant label and campaign name
          variant_label: selectedVariant,
          campaign_name: campaignName || '',
          // Include all variable values
          ...variableValues
        };

        const syncData = await syncToSmartlead(
          contactId,
          campaignId,
          subjectTemplate,
          bodyTemplate,
          customFields
        );

        // Update contact data if available
        if (syncData.contact && syncData.contact.smartlead_lead_id && contact) {
          // This assumes that contact is a reference that can be modified
          contact.smartlead_lead_id = syncData.contact.smartlead_lead_id;
          contact.smartlead_status = syncData.contact.smartlead_status;
          contact.last_email_sent_at = new Date().toISOString();
        }

        toast.success('Message synced to Smartlead');
      }
    } catch (error) {
      console.error('Error saving message:', error);
      toast.error(`Failed to save message: ${(error as Error).message}`);
    } finally {
      setSaving(false);
    }
  };

  // Handle adding a new message
  const handleAddMessage = async () => {
    if (!contactId || !campaignId) {
      toast.error('Missing contact or campaign ID');
      return;
    }

    // Use the raw templates for subject and body (with variables intact)
    const subjectTemplate = editedSubject;
    const bodyTemplate = editedBody;

    setSaving(true);
    try {
      // First, save the message to our local database
      const response = await fetch(`/api/messages?contact_id=${contactId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: subjectTemplate, // Raw subject with variables
          body: bodyTemplate, // Raw body with variables
          direction: 'outbound',
          from_email: '<EMAIL>',
          to_email: contact?.email,
          role: 'user',
          smartlead_campaign_id: campaignId,
          // Include variable values in metadata
          metadata: {
            campaign_id: campaignId,
            campaign_name: campaignName,
            variant_label: selectedVariant,
            variables: variableValues,
            derived_subject: getPreviewContent(subjectTemplate, contact, messageData, variableValues),
            derived_body: getPreviewContent(bodyTemplate, contact, messageData, variableValues),
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save message: ${response.status}`);
      }

      const savedMessageData = await response.json();
      
      // Notify parent component
      if (onMessageSaved) {
        onMessageSaved(savedMessageData);
      }

      toast.success('Message added successfully');
    } catch (error) {
      console.error('Error adding message:', error);
      toast.error(`Failed to add message: ${(error as Error).message}`);
    } finally {
      setSaving(false);
    }
  };

  // functions for variable extraction and display
  const extractVariables = (template: string): string[] => {
    if (!template) return [];
    
    const matches = template.match(/{{\s*([^}]+)\s*}}/g) || [];
    return matches
      .map(match => match.replace(/^{{\s*|\s*}}$/g, ''))
      .filter((value, index, self) => self.indexOf(value) === index); // Unique values only
  };
  
  // Get the preview content with special handling for template variables
  const getPreviewContent = (
    template: string, 
    contact: Contact | undefined | null,
    msgData: {subject: string, body: string} = {subject: '', body: ''},
    customVariables: Record<string, string> = {}
  ): string => {
    console.log('getPreviewContent', template, contact, msgData, customVariables);
    if (!template) return '';
    
    // First check if template is just a single template variable
    if (/^{{\s*([^}]+)\s*}}$/.test(template)) {
      const varName = template.match(/^{{\s*([^}]+)\s*}}$/)?.[1];
      
      if (varName) {
        // Use the same logic as getVariableValue for consistency
        return getVariableValue(varName, contact, msgData);
      }
    }

    // Process template variables in the content
    let processedTemplate = template;
    
    // Replace all variables with their values using consistent logic
    processedTemplate = processedTemplate.replace(/{{\s*([^}]+)\s*}}/g, (match, variable) => {
      const trimmedVar = variable.trim();
      
      // Check custom variables first (these take precedence)
      if (customVariables[trimmedVar] !== undefined) {
        return customVariables[trimmedVar];
      }
      
      // Use getVariableValue for consistent variable resolution
      const variableValue = getVariableValue(trimmedVar, contact, msgData);
      
      // If we got a value, use it; otherwise return placeholder
      return variableValue || `[${trimmedVar}]`;
    });
    
    return processedTemplate;
  };

  // Helper function to get contact variable values
  const getVariableValue = (
    variable: string, 
    contact: Contact | undefined | null,
    msgData: Message | {subject: string, body: string} = {subject: '', body: ''}
  ): string => {
    
    // Handle simple subject and body variables (these come from message data, not contact)
    if (variable === 'subject') {
      return 'subject' in msgData ? msgData.subject || '' : '';
    }
    
    if (variable === 'body') {
      return 'body' in msgData ? msgData.body || '' : '';
    }
    
    // // IMPORTANT: Handle email template variables that end with _subject or _body
    // // These should come from the sequence data, not msgData
    // if (variable.endsWith('_subject')) {
    //   // Look for this variable in the sequence variants
    //   if (sequence?.sequence_variants) {
    //     for (const variant of sequence.sequence_variants) {
    //       // Check if this variable matches a subject from any variant
    //       const variantName = variable.replace('_subject', '');
    //       if (variant.subject && (
    //         variant.variant_label.toLowerCase().includes(variantName.toLowerCase()) ||
    //         variantName.toLowerCase().includes(variant.variant_label.toLowerCase())
    //       )) {
    //         return variant.subject;
    //       }
    //     }
    //   }
    //   // Fallback to current edited subject if no specific variant found
    //   return editedSubject || '';
    // }
    
    // if (variable.endsWith('_body')) {
    //   // Look for this variable in the sequence variants
    //   if (sequence?.sequence_variants) {
    //     for (const variant of sequence.sequence_variants) {
    //       // Check if this variable matches a body from any variant
    //       const variantName = variable.replace('_body', '');
    //       if (variant.email_body && (
    //         variant.variant_label.toLowerCase().includes(variantName.toLowerCase()) ||
    //         variantName.toLowerCase().includes(variant.variant_label.toLowerCase())
    //       )) {
    //         return variant.email_body;
    //       }
    //     }
    //   }
    //   // Fallback to current edited body if no specific variant found
    //   return editedBody || '';
    // }
    
    // // Check if variable ends with _intro and get intro content
    // if (variable.endsWith('_intro')) {
    //   if (sequence?.sequence_variants) {
    //     for (const variant of sequence.sequence_variants) {
    //       const variantName = variable.replace('_intro', '');
    //       if (variant.email_body && (
    //         variant.variant_label.toLowerCase().includes(variantName.toLowerCase()) ||
    //         variantName.toLowerCase().includes(variant.variant_label.toLowerCase())
    //       )) {
    //         // Extract first paragraph as intro
    //         const bodyText = variant.email_body.replace(/<[^>]*>/g, ''); // Strip HTML
    //         const firstParagraph = bodyText.split('\n\n')[0] || bodyText.substring(0, 200);
    //         return firstParagraph.trim();
    //       }
    //     }
    //   }
    //   return '';
    // }
    
    // IMPORTANT: First check if the variable exists in message metadata
    if (msgData && 'metadata' in msgData && msgData.metadata?.variables && 
          msgData.metadata.variables[variable] !== undefined) {
        return msgData.metadata.variables[variable];
      }
    
    // IMPORTANT: Check if variable exists in the comprehensive campaign variables
    if (allCampaignVariables[variable] !== undefined) {
      return allCampaignVariables[variable];
      }
    
    // Handle standard contact variables (these require contact data)
    if (!contact) return '';
    
    const variableMap: Record<string, string> = {
      first_name: contact?.first_name || '',
      last_name: contact?.last_name || '',
      email: contact?.email || '',
      company_name: contact?.company_name || '',
      job_title: contact?.job_title || '',
      phone_number: contact?.phone_number || '',
      company_website: contact?.company_website || '',
      industry: contact?.industry || ''
    };
    
    return variableMap[variable] || '';
  };

  // Group variables by type (contact vs custom) - Updated to work with comprehensive variable list
  const groupVariables = (variables: string[]): { contactVars: string[], customVars: string[] } => {
    const contactVarNames = ['first_name', 'last_name', 'email', 'company_name', 'job_title', 'phone_number', 'company_website', 'industry'];
    const contactVars: string[] = [];
    const customVars: string[] = [];
    
    variables.forEach(variable => {
      if (contactVarNames.includes(variable) || 
          variable === 'subject' || variable === 'body') {
        contactVars.push(variable);
      } else {
        customVars.push(variable);
      }
    });
    
    return { contactVars, customVars };
  };

  if (loading) {
    return (
      <Card className="bg-white shadow-sm border border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Campaign Sequence</CardTitle>
          <CardDescription>
            Loading sequence data...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <RefreshCw className="h-8 w-8 text-blue-400 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (!sequence || !sequence.sequence_variants?.length) {
    return (
      <Card className="bg-white shadow-sm border border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Campaign Sequence</CardTitle>
          <CardDescription>
            No sequence data available for this campaign
          </CardDescription>
        </CardHeader>
        <CardContent className="text-gray-500 text-center py-8">
          This campaign doesn't have any sequence data available.
        </CardContent>
      </Card>
    );
  }

  // Extract all variables from current template AND from comprehensive campaign list
  const subjectVariables = extractVariables(editedSubject);
  const bodyVariables = extractVariables(editedBody);
  const currentTemplateVariables = [...new Set([...subjectVariables, ...bodyVariables])];
  
  // IMPORTANT: Only show variables that are actually used in the current template
  // Filter to only include variables that are referenced in subject or body
  const usedVariables = currentTemplateVariables;
  const { contactVars, customVars } = groupVariables(usedVariables);

  return (
    <Card className="bg-white shadow-sm border border-gray-200">
      <CardHeader className="pb-3 border-b border-gray-100">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-medium">Campaign Sequence</CardTitle>
            <CardDescription className="text-xs text-gray-500">
              Configure and send campaign messages • Only showing used variables ({usedVariables.length} total)
            </CardDescription>
          </div>
          <Select
            value={selectedVariant}
            onValueChange={handleVariantChange}
          >
            <SelectTrigger className="w-[140px] h-8 text-xs">
              <SelectValue placeholder="Variant Step 1" />
            </SelectTrigger>
            <SelectContent>
              {sequence?.sequence_variants?.map(variant => (
                <SelectItem key={variant.variant_label} value={variant.variant_label}>
                  Variant Step {variant.variant_label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="flex flex-col md:flex-row">
          {/* Left Column: Variables Editor */}
          <div className="w-full md:w-1/3 p-4 border-r border-gray-100">
            <div className="mb-1">
              <h3 className="text-sm font-medium">
                Variables
              </h3>
              <p className="text-xs text-gray-500 mb-3">
                Variables used in current template ({usedVariables.length} total)
              </p>
            </div>
          
            
            {/* Contact Variables - Only show used ones */}
            {contactVars.length > 0 && (
              <div className="mb-4">
                <h4 className="text-xs text-gray-500 mb-2">Contact Variables ({contactVars.length})</h4>
                <div>
                  {contactVars.map(variable => {
                    // Since we've already filtered to only used variables, all should be marked as used
                    return (
                      <div key={variable} className="flex mb-2 ring-1 ring-blue-200 rounded-lg p-1">
                        <div className="w-full">
                          <div className="text-sm text-gray-700 mb-1 flex items-center">
                            {variable}
                            <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-1 py-0.5 rounded">Used</span>
                          </div>
                          <Input
                            value={getVariableValue(variable, contact, messageData)}
                            readOnly
                            className="text-xs h-8 bg-gray-50 border-gray-200"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            
            {/* Custom Variables - Only show used ones */}
            {customVars.length > 0 && (
              <div>
                <h4 className="text-xs text-gray-500 mb-2">Custom Variables ({customVars.length})</h4>
                <div>
                  {customVars.map(variable => {
                    // Since we've already filtered to only used variables, all should be marked as used
                    const currentValue = variableValues[variable] || '';
                    const isHtmlContent = currentValue.includes('<') && currentValue.includes('>');
                    const isLongContent = currentValue.length > 100;
                    const shouldUseTextarea = isHtmlContent || isLongContent;
                    const lineCount = isHtmlContent ? Math.min(Math.max(4, Math.ceil(currentValue.length / 60)), 10) : Math.min(Math.max(3, Math.ceil(currentValue.length / 80)), 6);
                    
                    return (
                      <div key={variable} className="mb-3 ring-1 ring-blue-200 rounded-lg p-2 bg-white">
                        <div className="w-full">
                          <div className="text-sm text-gray-700 mb-2 flex items-center justify-between">
                            <div className="flex items-center">
                              <span className="font-mono">{variable}</span>
                              <span className="ml-2 text-xs bg-blue-100 text-blue-600 px-1 py-0.5 rounded">Used</span>
                              {isHtmlContent && (
                                <span className="ml-1 text-xs bg-orange-100 text-orange-600 px-1 py-0.5 rounded">HTML</span>
                              )}
                              {isLongContent && !isHtmlContent && (
                                <span className="ml-1 text-xs bg-purple-100 text-purple-600 px-1 py-0.5 rounded">Long</span>
                              )}
                            </div>
                            <div className="text-xs text-gray-400">
                              {currentValue.length} chars
                            </div>
                          </div>
                          
                          {shouldUseTextarea ? (
                            <div className="space-y-2">
                              <textarea
                                value={currentValue}
                                onChange={(e) => handleVariableChange(variable, e.target.value)}
                                className={cn(
                                  "w-full text-xs p-2 border border-gray-200 rounded resize-vertical",
                                  isHtmlContent ? "font-mono bg-orange-50" : "bg-gray-50"
                                )}
                                placeholder={`Value for ${variable}`}
                                rows={lineCount}
                                style={{ minHeight: '60px' }}
                              />
                            </div>
                          ) : (
                            <Input
                              value={currentValue}
                              onChange={(e) => handleVariableChange(variable, e.target.value)}
                              className="text-xs h-8 border-gray-200 bg-gray-50"
                              placeholder={`Value for ${variable}`}
                            />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            
            {usedVariables.length === 0 && (
              <p className="text-sm text-gray-500 italic">
                No variables detected in current template
              </p>
            )}
          </div>
          
          {/* Right Column: Template and Preview */}
          <div className="w-full md:w-2/3 p-0">
            {/* Tabs for Template and Preview */}
            <Tabs defaultValue="template" value={activeTab} onValueChange={handleTabChange} className="w-full">
              <div className="border-b border-gray-100">
                <TabsList className="grid grid-cols-2 h-10 w-full rounded-none bg-transparent border-b border-gray-100">
                  <TabsTrigger 
                    value="template" 
                    className={cn(
                      "data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none rounded-none bg-transparent",
                      "text-xs font-medium flex items-center justify-center h-10"
                    )}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Template
                  </TabsTrigger>
                  <TabsTrigger 
                    value="preview" 
                    className={cn(
                      "data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:shadow-none rounded-none bg-transparent",
                      "text-xs font-medium flex items-center justify-center h-10"
                    )}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <div className="p-4">
                <TabsContent value="template" className="mt-0 p-0">
                  {/* Subject Template */}
                  <div className="mb-4">
                    <Label htmlFor="subject" className="text-xs font-medium mb-1 block">Subject Template</Label>
                    <div className="p-2 border rounded bg-gray-50 text-sm font-mono">
                      {editedSubject}
                    </div>
                  </div>
                  
                  {/* Message Template */}
                  <div className="mb-4">
                    <Label htmlFor="message-body" className="text-xs font-medium mb-1 block">Message Template</Label>
                    <div className="border rounded bg-gray-50 h-[300px] overflow-hidden">
                      <iframe 
                        ref={iframeRef}
                        className="w-full h-full border-none" 
                        title="Template Preview"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Variables like {"{{"} variable_name {"}}"} will be replaced with values
                    </p>
                  </div>
                </TabsContent>
                
                <TabsContent value="preview" className="mt-0 p-0">
                  {/* Subject Preview */}
                  <div className="mb-4">
                    <Label htmlFor="subject-preview" className="text-xs font-medium mb-1 block">Subject Preview</Label>
                    <div className="p-2 border rounded bg-white text-sm">
                      {getPreviewContent(editedSubject, contact, messageData, variableValues)}
                    </div>
                  </div>
                  
                  {/* Message Preview */}
                  <div className="mb-4">
                    <Label htmlFor="message-preview" className="text-xs font-medium mb-1 block">Message Preview</Label>
                    <div className="border rounded bg-white h-[300px] overflow-hidden">
                      <iframe 
                        ref={iframeRef}
                        className="w-full h-full border-none" 
                        title="Message Preview"
                      />
                    </div>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end gap-2 p-4 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={handleAddMessage}
            disabled={saving}
            size="sm"
            className="mr-auto h-9 text-xs"
          >
            {saving ? (
              <RefreshCw className="h-3.5 w-3.5 mr-1 animate-spin" />
            ) : null}
            Add Message
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSaveMessage(false)}
            disabled={saving}
            size="sm"
            className="h-9 text-xs"
          >
            {saving ? (
              <RefreshCw className="h-3.5 w-3.5 mr-1 animate-spin" />
            ) : null}
            Save Template
          </Button>
          <Button
            variant="default"
            className="bg-green-600 hover:bg-green-700 h-9 text-xs"
            onClick={() => handleSaveMessage(true)}
            disabled={saving}
            size="sm"
          >
            {saving ? (
              <RefreshCw className="h-3.5 w-3.5 mr-1 animate-spin" />
            ) : (
              <Send className="h-3.5 w-3.5 mr-1" />
            )}
            Sync to Smartlead
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CampaignSequenceSection; 