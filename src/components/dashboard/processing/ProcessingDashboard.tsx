"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../ui/tabs'
import { Input } from '../../ui/input'
import { Label } from '../../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select'
import { Checkbox } from '../../ui/checkbox'
import { 
  Play, 
  Pause, 
  Building, 
  Mail, 
  Search, 
  Target, 
  PenTool,
  CheckCircle2,
  Clock,
  Activity,
  RefreshCw,
  Filter,
  X,
  Upload,
  AlertTriangle,
  Users,
  TrendingUp,
  AlertCircle
} from 'lucide-react'
import { ProcessingStage, StageStats, ProcessingError } from '../../../types/processing'
import { ProcessingFilters } from '../../../lib/processors/BaseProcessor'
import EnhancedCSVUploader from '../../investors/EnhancedCSVUploader'

interface ProcessingJob {
  id: string
  stage: ProcessingStage
  processor: string
  schedule: string
  enabled: boolean
  lastRun?: Date
  isRunning: boolean
}

interface ProcessingStats {
  contacts: {
    email_verification: StageStats
    osint_research: StageStats
    overview_extraction: StageStats
    classification: StageStats
    email_generation: StageStats
    email_sending: StageStats
  }
  companies: {
    website_scraping: StageStats
    company_overview: StageStats
  }
  totals: {
    total_contacts: number
    total_companies: number
  }
  errors: {
    contact_errors: ProcessingError[]
    company_errors: ProcessingError[]
  }
  metadata?: {
    statsSource: string
    lastUpdated: string
    queryInfo?: string
    explanations?: {
      contacts: Record<string, string>
      companies: Record<string, string>
    }
  }
}

interface FilterState {
  source: string
}

const ProcessingDashboard = () => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([])
  const [stats, setStats] = useState<ProcessingStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [executing, setExecuting] = useState<Set<string>>(new Set())
  const [processingLimit, setProcessingLimit] = useState(50)
  const [singleEntityId, setSingleEntityId] = useState('')
  const [filters, setFilters] = useState<FilterState>({
    source: 'all'
  })
  const [sources, setSources] = useState<{source: string, count: number}[]>([])
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false) // Disabled by default

  useEffect(() => {
    fetchJobs()
    fetchStats()
    fetchSources()
  }, [filters])

  useEffect(() => {
    // Only set up auto-refresh if enabled
    if (!autoRefreshEnabled) return

    const interval = setInterval(() => {
      // Only refresh if not already refreshing
      if (!isRefreshing) {
        setIsRefreshing(true)
        Promise.all([fetchJobs(), fetchStats()])
          .finally(() => setIsRefreshing(false))
      }
    }, 30000) // Refresh every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefreshEnabled, isRefreshing])

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/processing/trigger?action=jobs', {
        signal: AbortSignal.timeout(15000) // 15 second timeout
      })
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      if (data.success) {
        setJobs(data.data.jobs)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch jobs:', error)
      }
    }
  }

  const fetchStats = async () => {
    try {
      // Build filter query string
      const queryParams = new URLSearchParams();
      if (filters.source !== 'all') queryParams.set('source', filters.source);

      const statsUrl = `/api/processing/stats?${queryParams.toString()}`;
      const response = await fetch(statsUrl, {
        signal: AbortSignal.timeout(20000) // 20 second timeout
      })
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const data = await response.json()
      if (data.success) {
        setStats(data.data)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch stats:', error)
      }
    } finally {
      setLoading(false)
    }
  }

  const fetchSources = async () => {
    try {
      const response = await fetch('/api/contacts/sources')
      const data = await response.json()
      setSources(Array.isArray(data) ? data : [])
    } catch (error) {
      console.error('Failed to fetch sources:', error)
      setSources([])
    }
  }

  const manualRefresh = async () => {
    setIsRefreshing(true)
    try {
      await Promise.all([fetchJobs(), fetchStats()])
    } finally {
      setIsRefreshing(false)
    }
  }

  const executeJob = async (stage: ProcessingStage, options: { limit?: number; singleId?: number; source?: string } = {}) => {
    const jobKey = `${stage}_${options.singleId || 'batch'}`
    setExecuting(prev => new Set([...prev, jobKey]))

    try {
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage,
          options: {
            limit: options.limit,
            singleId: options.singleId,
            filters: filters.source !== 'all' ? { source: filters.source } : undefined
          }
        })
      })

      const data = await response.json()
      if (data.success) {
        // Refresh data after execution
        await fetchJobs()
        await fetchStats()
      }
    } catch (error) {
      console.error('Failed to execute job:', error)
    } finally {
      setExecuting(prev => {
        const newSet = new Set(prev)
        newSet.delete(jobKey)
        return newSet
      })
    }
  }

  const toggleJob = async (jobId: string, enabled: boolean) => {
    try {
      await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'toggle_scheduled_job',
          options: { jobId, enabled }
        })
      })
      await fetchJobs()
    } catch (error) {
      console.error('Failed to toggle job:', error)
    }
  }

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      return newFilters;
    });
  }

  const getStageIcon = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return <Mail className="h-4 w-4" />
      case 'company_overview': return <Building className="h-4 w-4" />
      case 'company_web_crawler': return <Search className="h-4 w-4" />
      case 'contact_search': return <Search className="h-4 w-4" />
      case 'contact_overview': return <Target className="h-4 w-4" />
      case 'contact_classification': return <Target className="h-4 w-4" />
      case 'email_generation': return <PenTool className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getStageColor = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return 'bg-blue-500'
      case 'company_overview': return 'bg-emerald-500'
      case 'company_web_crawler': return 'bg-cyan-500'
      case 'contact_search': return 'bg-purple-500'
      case 'contact_overview': return 'bg-indigo-500'
      case 'contact_classification': return 'bg-orange-500'
      case 'email_generation': return 'bg-pink-500'
      default: return 'bg-gray-500'
    }
  }

  const formatStageTitle = (stage: ProcessingStage) => {
    switch (stage) {
      case 'email_validation': return 'Email Validation'
      case 'company_overview': return 'Company Overview'
      case 'company_web_crawler': return 'Web Crawler'
      case 'contact_search': return 'OSINT Research'
      case 'contact_overview': return 'Contact Overview'
      case 'contact_classification': return 'Contact Classification'
      case 'email_generation': return 'Email Generation'
      default: return String(stage).replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
    }
  }

  const getStageStats = (stage: ProcessingStage): StageStats => {
    if (!stats) return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0 }
    
    switch (stage) {
      case 'email_validation':
        return stats.contacts.email_verification
      case 'contact_search':
        return stats.contacts.osint_research
      case 'contact_overview':
        return stats.contacts.overview_extraction
      case 'contact_classification':
        return stats.contacts.classification
      case 'email_generation':
        return stats.contacts.email_generation
      case 'company_overview':
        return stats.companies.company_overview
      case 'company_web_crawler':
        return stats.companies.website_scraping
      default:
        return { total: 0, pending: 0, running: 0, completed: 0, failed: 0, error: 0 }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600'
      case 'running': return 'text-blue-600'
      case 'completed': return 'text-green-600'
      case 'failed': return 'text-red-600'
      case 'error': return 'text-red-800'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-3 w-3" />
      case 'running': return <RefreshCw className="h-3 w-3 animate-spin" />
      case 'completed': return <CheckCircle2 className="h-3 w-3" />
      case 'failed': return <AlertTriangle className="h-3 w-3" />
      case 'error': return <AlertCircle className="h-3 w-3" />
      default: return <Activity className="h-3 w-3" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Processing Dashboard</h1>
          <p className="text-gray-600 mt-2">Monitor and control automated processing pipelines</p>
        </div>
        <div className="flex items-center gap-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center gap-2">
            <Checkbox
              id="auto-refresh"
              checked={autoRefreshEnabled}
              onCheckedChange={(checked) => setAutoRefreshEnabled(checked === true)}
            />
            <Label htmlFor="auto-refresh" className="text-sm">
              Auto-refresh (30s)
            </Label>
          </div>
          
          {/* Manual refresh button */}
          <Button 
            onClick={manualRefresh} 
            variant="outline"
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Contacts</p>
                  <p className="text-2xl font-bold">{stats.totals.total_contacts.toLocaleString()}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Companies</p>
                  <p className="text-2xl font-bold">{stats.totals.total_companies.toLocaleString()}</p>
                </div>
                <Building className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Contact Errors</p>
                  <p className="text-2xl font-bold text-red-600">{stats.errors.contact_errors.length}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Company Errors</p>
                  <p className="text-2xl font-bold text-red-600">{stats.errors.company_errors.length}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Global Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Global Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 max-w-md">
            <div>
              <Label htmlFor="source">Data Source</Label>
              <Select value={filters.source} onValueChange={(value) => handleFilterChange('source', value)} >
                <SelectTrigger>
                  <SelectValue placeholder="All sources" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {sources.map(source => (
                    <SelectItem key={source.source} value={source.source}>
                      {source.source} ({source.count})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="manual" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="manual">Manual Processing</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Jobs</TabsTrigger>
          <TabsTrigger value="single">Single Entity</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
        </TabsList>

        {/* Manual Processing */}
        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                Manual Processing
                {filters.source !== 'all' && (
                  <Badge variant="secondary" className="ml-2">
                    Source: {filters.source}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Label htmlFor="limit">Batch Limit:</Label>
                  <Input
                    id="limit"
                    type="number"
                    value={processingLimit}
                    onChange={(e) => setProcessingLimit(parseInt(e.target.value) || 50)}
                    className="w-20"
                    min="1"
                    max="1000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {([
                  'email_validation',
                  'company_overview',
                  'company_web_crawler',
                  'contact_search',
                  'contact_overview',
                  'contact_classification',
                  'email_generation'
                ] as ProcessingStage[]).map((stage) => {
                  const stageStats = getStageStats(stage)
                  return (
                    <Card key={stage} className="border-l-4" style={{ borderLeftColor: getStageColor(stage).replace('bg-', '') }}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getStageIcon(stage)}
                            <h3 className="font-medium">{formatStageTitle(stage)}</h3>
                          </div>
                          <Badge variant="outline">
                            {stageStats.pending} pending / {stageStats.total} total
                            {filters.source !== 'all' && (
                              <Filter className="h-3 w-3 ml-1 text-blue-500" />
                            )}
                          </Badge>
                        </div>

                        {/* Status breakdown */}
                        <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
                          <div className={`flex items-center gap-1 ${getStatusColor('pending')}`}>
                            {getStatusIcon('pending')}
                            <span>P: {stageStats.pending}</span>
                          </div>
                          <div className={`flex items-center gap-1 ${getStatusColor('running')}`}>
                            {getStatusIcon('running')}
                            <span>R: {stageStats.running}</span>
                          </div>
                          <div className={`flex items-center gap-1 ${getStatusColor('completed')}`}>
                            {getStatusIcon('completed')}
                            <span>C: {stageStats.completed}</span>
                          </div>
                          <div className={`flex items-center gap-1 ${getStatusColor('failed')}`}>
                            {getStatusIcon('failed')}
                            <span>F: {stageStats.failed}</span>
                          </div>
                          <div className={`flex items-center gap-1 ${getStatusColor('error')}`}>
                            {getStatusIcon('error')}
                            <span>E: {stageStats.error}</span>
                          </div>
                        </div>
                        
                        <Button
                          onClick={() => executeJob(stage, { 
                            limit: processingLimit
                          })}
                          disabled={executing.has(`${stage}_batch`) || stageStats.pending === 0}
                          className="w-full"
                          size="sm"
                        >
                          {executing.has(`${stage}_batch`) ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              {(() => {
                                const willProcess = Math.min(processingLimit, stageStats.pending)
                                return `Process ${willProcess}${stageStats.pending === 0 ? ' (none available)' : ''}`
                              })()}
                              {filters.source !== 'all' && ' (Filtered)'}
                            </>
                          )}
                        </Button>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Jobs */}
        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Scheduled Jobs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {jobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className={`w-3 h-3 rounded-full ${job.enabled ? 'bg-green-500' : 'bg-gray-400'}`} />
                      <div>
                        <div className="flex items-center gap-2">
                          {getStageIcon(job.stage)}
                          <h3 className="font-medium">{formatStageTitle(job.stage)}</h3>
                        </div>
                        <p className="text-sm text-gray-600">Schedule: {job.schedule}</p>
                        {job.lastRun && (
                          <p className="text-xs text-gray-500">
                            Last run: {new Date(job.lastRun).toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {job.isRunning && (
                        <Badge variant="secondary">
                          <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                          Running
                        </Badge>
                      )}
                      
                      <Button
                        onClick={() => toggleJob(job.id, !job.enabled)}
                        variant={job.enabled ? "secondary" : "outline"}
                        size="sm"
                      >
                        {job.enabled ? (
                          <>
                            <Pause className="h-4 w-4 mr-1" />
                            Disable
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-1" />
                            Enable
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Single Entity */}
        <TabsContent value="single" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Single Entity Processing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Label htmlFor="entityId">Entity ID:</Label>
                  <Input
                    id="entityId"
                    type="number"
                    value={singleEntityId}
                    onChange={(e) => setSingleEntityId(e.target.value)}
                    placeholder="Enter contact or company ID"
                    className="w-48"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {([
                  'email_validation',
                  'company_overview',
                  'company_web_crawler', 
                  'contact_search',
                  'contact_overview',
                  'contact_classification',
                  'email_generation'
                ] as ProcessingStage[]).map((stage) => (
                  <Card key={stage} className="border-l-4" style={{ borderLeftColor: getStageColor(stage).replace('bg-', '') }}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        {getStageIcon(stage)}
                        <h3 className="font-medium">{formatStageTitle(stage)}</h3>
                      </div>
                      
                      <Button
                        onClick={() => executeJob(stage, { singleId: parseInt(singleEntityId) })}
                        disabled={!singleEntityId || executing.has(`${stage}_${singleEntityId}`)}
                        className="w-full"
                        size="sm"
                      >
                        {executing.has(`${stage}_${singleEntityId}`) ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Process ID {singleEntityId || '...'}
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Errors Tab */}
        <TabsContent value="errors" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Errors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Contact Processing Errors
                  <Badge variant="destructive">{stats?.errors.contact_errors.length || 0}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {stats?.errors.contact_errors.map((error, index) => (
                    <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">
                          Contact {error.entity_id} - {formatStageTitle(error.stage)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {error.retry_count && `Retries: ${error.retry_count}`}
                        </span>
                      </div>
                      <p className="text-sm text-red-700">{error.error_message}</p>
                      {error.occurred_at && (
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(error.occurred_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )) || <p className="text-gray-500">No contact errors found</p>}
                </div>
              </CardContent>
            </Card>

            {/* Company Errors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Company Processing Errors
                  <Badge variant="destructive">{stats?.errors.company_errors.length || 0}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {stats?.errors.company_errors.map((error, index) => (
                    <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">
                          Company {error.entity_id} - {formatStageTitle(error.stage)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {error.retry_count && `Retries: ${error.retry_count}`}
                        </span>
                      </div>
                      <p className="text-sm text-red-700">{error.error_message}</p>
                      {error.occurred_at && (
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(error.occurred_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )) || <p className="text-gray-500">No company errors found</p>}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ProcessingDashboard 