"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Input } from '../../ui/input'
import { Label } from '../../ui/label'
import { Textarea } from '../../ui/textarea'
import { 
  User, 
  Building, 
  Mail, 
  Search, 
  Target, 
  PenTool,
  CheckCircle2,
  Clock,
  Activity,
  RefreshCw,
  ExternalLink,
  Calendar,
  MessageSquare,
  Lightbulb,
  FileText,
  Eye,
  Play
} from 'lucide-react'

interface ContactData {
  contact_id: number
  full_name: string
  first_name?: string
  last_name?: string
  title?: string
  email: string
  linkedin_url?: string
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  email_status?: string
  searched?: boolean
  extracted?: boolean
  email_generated?: boolean
}

interface ExtractedData {
  contact_id: number
  executive_summary: string
  career_timeline: string[]
  notable_activities: Array<{
    title: string
    snippet: string
    url?: string
    date?: string
  }>
  personal_tidbits: string[]
  conversation_hooks: string[]
  outreach_draft: string
  sources: string[]
  created_at: string
  updated_at: string
}

const ContactOverview = () => {
  const [contacts, setContacts] = useState<ContactData[]>([])
  const [selectedContact, setSelectedContact] = useState<ContactData | null>(null)
  const [extractedData, setExtractedData] = useState<ExtractedData | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchContacts()
  }, [])

  const fetchContacts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/contacts/overview')
      const data = await response.json()
      if (data.success) {
        setContacts(data.data.contacts)
      }
    } catch (error) {
      console.error('Failed to fetch contacts:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchExtractedData = async (contactId: number) => {
    try {
      const response = await fetch(`/api/contacts/overview/${contactId}`)
      const data = await response.json()
      if (data.success) {
        setExtractedData(data.data.extracted_data)
      }
    } catch (error) {
      console.error('Failed to fetch extracted data:', error)
      setExtractedData(null)
    }
  }

  const processContact = async (contactId: number) => {
    try {
      setProcessing(true)
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage: 'contact_overview',
          options: { singleId: contactId }
        })
      })

      const data = await response.json()
      if (data.success) {
        // Refresh the contact data
        await fetchContacts()
        if (selectedContact?.contact_id === contactId) {
          await fetchExtractedData(contactId)
        }
      }
    } catch (error) {
      console.error('Failed to process contact:', error)
    } finally {
      setProcessing(false)
    }
  }

  const selectContact = async (contact: ContactData) => {
    setSelectedContact(contact)
    if (contact.extracted) {
      await fetchExtractedData(contact.contact_id)
    } else {
      setExtractedData(null)
    }
  }

  const filteredContacts = contacts.filter(contact =>
    contact.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.company_name?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (contact: ContactData) => {
    if (!contact.searched) {
      return <Badge variant="secondary">Needs OSINT</Badge>
    }
    if (!contact.extracted) {
      return <Badge variant="outline">Ready for Extraction</Badge>
    }
    return <Badge variant="default" className="bg-green-500">Extracted</Badge>
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Contact Overview</h1>
          <p className="text-gray-600 mt-2">View and manage extracted contact profiles</p>
        </div>
        <Button onClick={fetchContacts} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contact List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contacts ({filteredContacts.length})
              </CardTitle>
              <div className="mt-4">
                <Label htmlFor="search">Search Contacts</Label>
                <Input
                  id="search"
                  placeholder="Search by name, email, or company..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mt-1"
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-2 max-h-96 overflow-y-auto">
              {filteredContacts.map((contact) => (
                <div
                  key={contact.contact_id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedContact?.contact_id === contact.contact_id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => selectContact(contact)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">
                        {contact.full_name || `${contact.first_name} ${contact.last_name}`}
                      </p>
                      <p className="text-xs text-gray-500 truncate">{contact.title}</p>
                      <p className="text-xs text-gray-500 truncate">{contact.company_name}</p>
                    </div>
                    <div className="ml-2 flex flex-col items-end gap-1">
                      {getStatusBadge(contact)}
                      {contact.searched && !contact.extracted && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation()
                            processContact(contact.contact_id)
                          }}
                          disabled={processing}
                          className="text-xs h-6 px-2"
                        >
                          {processing ? (
                            <RefreshCw className="h-3 w-3 animate-spin" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Contact Details */}
        <div className="lg:col-span-2">
          {selectedContact ? (
            <div className="space-y-6">
              {/* Contact Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    {selectedContact.full_name || `${selectedContact.first_name} ${selectedContact.last_name}`}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Email</Label>
                      <p className="text-sm text-gray-600">{selectedContact.email}</p>
                    </div>
                    <div>
                      <Label>Title</Label>
                      <p className="text-sm text-gray-600">{selectedContact.title || 'Not specified'}</p>
                    </div>
                    <div>
                      <Label>Company</Label>
                      <p className="text-sm text-gray-600">{selectedContact.company_name || 'Not specified'}</p>
                    </div>
                    <div>
                      <Label>Industry</Label>
                      <p className="text-sm text-gray-600">{selectedContact.industry || 'Not specified'}</p>
                    </div>
                    <div>
                      <Label>Country</Label>
                      <p className="text-sm text-gray-600">{selectedContact.contact_country || 'Not specified'}</p>
                    </div>
                    <div>
                      <Label>LinkedIn</Label>
                      {selectedContact.linkedin_url ? (
                        <a 
                          href={selectedContact.linkedin_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                        >
                          View Profile <ExternalLink className="h-3 w-3" />
                        </a>
                      ) : (
                        <p className="text-sm text-gray-600">Not available</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Extracted Data */}
              {extractedData ? (
                <div className="space-y-4">
                  {/* Executive Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Executive Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">
                        {extractedData.executive_summary}
                      </p>
                    </CardContent>
                  </Card>

                  {/* Career Timeline */}
                  {extractedData.career_timeline && extractedData.career_timeline.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Calendar className="h-5 w-5" />
                          Career Timeline
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {extractedData.career_timeline.map((item, index) => (
                            <li key={index} className="text-sm text-gray-700 border-l-2 border-blue-200 pl-3">
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {/* Notable Activities */}
                  {extractedData.notable_activities && extractedData.notable_activities.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Target className="h-5 w-5" />
                          Notable Activities
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {extractedData.notable_activities.map((activity, index) => (
                            <div key={index} className="border-l-2 border-green-200 pl-3">
                              <h4 className="font-medium text-sm">{activity.title}</h4>
                              <p className="text-sm text-gray-600">{activity.snippet}</p>
                              {activity.date && (
                                <p className="text-xs text-gray-500 mt-1">{activity.date}</p>
                              )}
                              {activity.url && (
                                <a 
                                  href={activity.url} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-xs text-blue-600 hover:underline flex items-center gap-1 mt-1"
                                >
                                  View Source <ExternalLink className="h-3 w-3" />
                                </a>
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Personal Tidbits */}
                  {extractedData.personal_tidbits && extractedData.personal_tidbits.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5" />
                          Personal Tidbits
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1">
                          {extractedData.personal_tidbits.map((tidbit, index) => (
                            <li key={index} className="text-sm text-gray-700">
                              • {tidbit}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {/* Conversation Hooks */}
                  {extractedData.conversation_hooks && extractedData.conversation_hooks.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5" />
                          Conversation Hooks
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {extractedData.conversation_hooks.map((hook, index) => (
                            <li key={index} className="text-sm text-gray-700 bg-yellow-50 p-2 rounded border-l-2 border-yellow-300">
                              💡 {hook}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {/* Outreach Draft */}
                  {extractedData.outreach_draft && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <PenTool className="h-5 w-5" />
                          Outreach Draft
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Textarea
                          value={extractedData.outreach_draft}
                          readOnly
                          className="min-h-32 text-sm"
                        />
                      </CardContent>
                    </Card>
                  )}

                  {/* Sources */}
                  {extractedData.sources && extractedData.sources.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <ExternalLink className="h-5 w-5" />
                          Sources
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1">
                          {extractedData.sources.map((source, index) => (
                            <li key={index}>
                              <a 
                                href={source} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                              >
                                {source} <ExternalLink className="h-3 w-3" />
                              </a>
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : selectedContact.extracted ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-gray-500">Loading extracted data...</p>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-6 text-center">
                    <div className="space-y-4">
                      <div className="text-gray-500">
                        <Target className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                        <p>No extracted data available</p>
                        <p className="text-sm">
                          {!selectedContact.searched 
                            ? 'Contact needs OSINT research first'
                            : 'Click the process button to extract profile data'
                          }
                        </p>
                      </div>
                      {selectedContact.searched && !selectedContact.extracted && (
                        <Button
                          onClick={() => processContact(selectedContact.contact_id)}
                          disabled={processing}
                          className="mt-4"
                        >
                          {processing ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Extract Profile Data
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <User className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Contact</h3>
                <p className="text-gray-500">Choose a contact from the list to view their profile data</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

export default ContactOverview 