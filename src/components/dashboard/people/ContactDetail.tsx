import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building2, Phone, Mail, MapPin, Calendar, MessageSquare, Briefcase, BarChart, Clock, Send, ArrowLeft, Link, ExternalLink, CheckCircle, FileText, Users, Globe, Activity, DollarSign, Linkedin as LinkedinIcon, Database, FileSearch, MessageCircle, AlertCircle, RefreshCw, CheckCircle2, Trash2, Edit } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>eader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import TrainingDataTab from './TrainingDataTab';
// Add import for RichTextEditor
import RichTextEditor, { RichTextEditorRef } from '@/components/common/RichTextEditor';

interface ContactDetailProps {
  contactId: number | string;
  onBack: () => void;
}

interface Contact {
  person_id?: number;
  contact_id: number;
  company_id?: number;
  first_name: string;
  last_name: string;
  email?: string;
  phone_number?: string;
  job_title?: string;
  company_name?: string;
  industry?: string;
  capital_type?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_website?: string;
  company_country?: string;
  source?: string;
  investment_criteria?: {
    asset_types?: string[];
    deal_size?: {
      min?: number;
      max?: number;
    };
    markets?: string[];
  };
  person_summary?: string;
  key_interests?: string[];
  recent_activities: {
    interaction_type: string;
    interaction_date: string;
    notes: string;
  }[];
  executive_summary?: string;
  career_timeline?: any[];
  notable_activities?: any[];
  personal_tidbits?: any[];
  conversation_hooks?: any[];
  outreach_draft?: any;
  sources?: any[];
  company_data?: CompanyData;
  // Search data fields
  searched?: boolean;
  searched_profile?: string;
  searched_input_data?: any;
  searched_tokens_used?: number;
  searched_date?: string;
  // Email validation fields
  email_generated?: boolean;
  email_validated_date?: string;
  email_status?: string;
  smartlead_lead_id?: string;
  smartlead_status?: string;
  last_email_sent_at?: string;
  extracted?: boolean;
}

interface CompanyData {
  company_id?: number;
  name?: string;
  company_name?: string;
  company_linkedin?: string;
  summary?: string;
  founded_year?: number;
  main_activities?: string[];
  contact_count?: string | number;
  focus_areas?: string[];
  risk_factors?: string[];
  recent_developments?: string[];
  engagement_opportunities?: string[];
  overview?: {
    structure_history?: string;
    executive_contacts?: ExecutiveContact[];
    investment_program?: InvestmentProgram;
    lending_program?: LendingProgram;
    capital_commitments?: CapitalCommitments;
    investment_strategy?: string[];
    recent_transactions?: Transaction[];
    hold_horizon?: string;
  };
  scraped_data?: ScrapedData;
  scraped_contacts?: ScrapedContact[];
}

interface ScrapedData {
  companyProfile?: {
    companyName?: string;
    companyType?: string;
    companyWebsite?: string;
    businessModel?: string;
    fundSize?: string;
    aum?: string;
    numberOfProperties?: number;
    headquarters?: string;
    numberOfOffices?: number;
    officeLocations?: string[];
    foundedYear?: number;
    numberOfEmployees?: string;
    investmentFocus?: string[];
    geographicFocus?: string[];
  };
  executiveTeam?: Array<{
    first_name: string;
    last_name: string;
    full_name: string;
    title: string;
    headline?: string;
    seniority?: string;
    email?: string;
    personal_email?: string;
    email_status?: string;
    linkedin_url?: string;
    contact_city?: string;
    contact_state?: string;
    contact_country?: string;
    phone?: string;
    bio?: string;
    category?: string;
  }>;
  recentDeals?: Array<{
    property?: string;
    location?: string;
    dealType?: string;
    amount?: string;
    date?: string;
    propertyType?: string;
    squareFeet?: string;
    units?: number;
  }>;
  investmentStrategy?: {
    mission?: string;
    approach?: string;
    targetReturn?: string;
    propertyTypes?: string[];
    strategies?: string[];
    assetClasses?: string[];
    valueCreation?: string[];
  };
  investmentCriteria?: {
    targetMarkets?: string[];
    dealSize?: string;
    minimumDealSize?: string;
    maximumDealSize?: string;
    holdPeriod?: string;
    riskProfile?: string;
    propertyTypes?: string[];
    propertySubcategories?: string[];
    assetTypes?: string[];
    loanTypes?: string[];
  };
  capitalSources?: string[];
  financialProducts?: Array<{
    productType?: string;
    terms?: string[];
    typicalAmount?: string;
    description?: string;
  }>;
  trackRecord?: {
    totalTransactions?: string;
    totalSquareFeet?: string;
    totalUnits?: string;
    historicalReturns?: string;
    portfolioValue?: string;
  };
  partnerships?: Array<{
    partnerName?: string;
    relationshipType?: string;
    description?: string;
  }>;
  contactInfo?: {
    website?: string;
    mainPhone?: string;
    mainEmail?: string;
    socialMedia?: {
      linkedin?: string;
      twitter?: string;
      facebook?: string;
      instagram?: string;
    };
  };
}

interface ScrapedContact {
  first_name?: string;
  last_name?: string;
  full_name?: string;
  title?: string;
  seniority?: string;
  category?: string;
  phone?: string;
  email?: string;
  personal_email?: string;
  email_status?: string;
  contact_city?: string;
  contact_state?: string;
  contact_country?: string;
  linkedin_url?: string;
  headline?: string;
  bio?: string;
}

interface ActivityProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}

interface Overview {
  executive_contacts?: ExecutiveContact[];
  investment_program?: InvestmentProgram;
  lending_program?: LendingProgram;
  capital_commitments?: CapitalCommitments;
  investment_strategy?: string[];
  recent_transactions?: Transaction[];
  structure_history?: string;
  recent_developments?: string[];
  contact_count?: number;
  company_linkedin?: string;
}

interface ExecutiveContact {
  name: string;
  title: string;
  email?: string;
  phone?: string;
}

interface InvestmentProgram {
  equity_size_range?: string;
  asset_type?: string[];
  deal_structure?: string[];
  property_types?: string[];
  geography?: string;
  hold_period?: string;
}

interface LendingProgram {
  program_name?: string;
  loan_size_range?: string;
  loan_types?: string[];
  geographic_focus?: string;
  property_types?: string[];
}

interface CapitalCommitments {
  debt_range?: string;
  equity_range?: string;
}

interface Transaction {
  asset_type: string;
  close_date?: string;
  location: string;
  capital_deployed?: string;
}

// Add normalization function for scraped_data
function normalizeScrapedData(data: any): ScrapedData {
  if (!data) return {};
  return {
    companyProfile: {
      companyName: data.companyname,
      companyType: data.companytype,
      companyWebsite: data.website,
      businessModel: data.businessmodel,
      fundSize: data.fundsize,
      aum: data.aum,
      numberOfProperties: data.numberofproperties,
      headquarters: data.headquarters,
      numberOfOffices: data.numberofoffices,
      foundedYear: data.foundedyear,
      numberOfEmployees: data.numberofemployees,
      investmentFocus: data.investmentfocus,
      geographicFocus: data.geographicfocus,
    },
    investmentStrategy: {
      mission: data.mission,
      approach: data.approach,
      targetReturn: data.targetreturn,
      propertyTypes: data.propertytypes,
      strategies: data.strategies,
      assetClasses: data.assetclasses,
      valueCreation: data.valuecreation,
    },
    investmentCriteria: {
      targetMarkets: data.targetmarkets,
      dealSize: data.dealsize,
      minimumDealSize: data.minimumdealsize,
      maximumDealSize: data.maximumdealsize,
      holdPeriod: data.holdperiod,
      riskProfile: data.riskprofile,
      propertyTypes: data.investment_criteria_property_types || data.propertytypes,
      propertySubcategories: data.investment_criteria_property_subcategories || data.propertysubcategories,
      assetTypes: data.investment_criteria_asset_types || data.assettypes,
      loanTypes: data.investment_criteria_loan_types || data.loantypes,
    },
    contactInfo: {
      website: data.website,
      mainPhone: data.mainphone,
      mainEmail: data.mainemail,
      socialMedia: data.socialmedia,
    },
    financialProducts: data.financialproducts,
    capitalSources: data.capitalsources,
    partnerships: data.partnerships,
    recentDeals: data.recentdeals,
    trackRecord: {
      totalTransactions: data.totaltransactions,
      totalSquareFeet: data.totalsquarefeet,
      totalUnits: data.totalunits,
      historicalReturns: data.historicalreturns,
      portfolioValue: data.portfoliovalue,
    },
  };
}

// Add new interfaces for threads and messages
interface Thread {
  thread_id: string;
  subject: string;
  status: string;
  created_at: string;
  updated_at: string;
  metadata?: {
    source?: string;
    campaign_id?: string;
  };
  messages: Message[];
}

interface Message {
  message_id: string;
  thread_id: string;
  from_email: string;
  to_email: string;
  subject: string;
  direction: string;
  role: string;
  body: string;
  created_at: string;
  sent_at: string;
  metadata?: {
    lead_id?: string;
    campaign_id?: string;
    source?: string;
    smartlead_response?: any;
  };
}

// Add SmartleadData interface
interface SmartleadData {
  contact: {
    contact_id: number;
    email: string;
    smartlead_lead_id?: string;
    smartlead_status?: string;
    last_email_sent_at?: string;
  };
  threads: any[];
  campaigns: any[];
  hasSmartleadData: boolean;
}

interface EmailDraft {
  subject: string;
  body: string;
}

const ContactDetail: React.FC<ContactDetailProps> = ({ contactId, onBack }) => {
  const [contact, setContact] = useState<Contact | null>(null);
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [loading, setLoading] = useState(true);
  const [companyLoading, setCompanyLoading] = useState(false);
  // Add state for threads and messages
  const [threads, setThreads] = useState<Thread[]>([]);
  const [loadingThreads, setLoadingThreads] = useState(true);
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const [messagesSort, setMessagesSort] = useState('newest');
  
  // Smartlead states
  const [smartleadData, setSmartleadData] = useState<SmartleadData | null>(null);
  const [smartleadLoading, setSmartleadLoading] = useState(false);
  const [smartleadSyncing, setSmartleadSyncing] = useState(false);
  const [smartleadError, setSmartleadError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  
  // Add state for message deletion
  const [messageToDelete, setMessageToDelete] = useState<string | null>(null);

  // Add state for email editor
  const [emailDraft, setEmailDraft] = useState<EmailDraft>({ 
    subject: '', 
    body: '' 
  });
  const [draftSaving, setDraftSaving] = useState(false);
  const [draftSaved, setDraftSaved] = useState(false);

  // Add state for editing messages
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editMessageSubject, setEditMessageSubject] = useState('');
  const [editMessageBody, setEditMessageBody] = useState('');
  const [editSaving, setEditSaving] = useState(false);
  const editTextareaRef = useRef<HTMLTextAreaElement>(null);
  // Add state for message editing
  const [messageContent, setMessageContent] = useState<string>('');
  const messageEditorRef = useRef<RichTextEditorRef>(null);

  useEffect(() => {
    const fetchContactDetails = async () => {
      try {
        setLoading(true);
        // Ensure contactId is properly formatted whether it's a number or string
        const formattedContactId = typeof contactId === 'number' ? contactId : contactId;
        console.log('Fetching contact details for ID:', formattedContactId);
        
        const response = await fetch(`/api/contacts/${formattedContactId}`);
        
        if (!response.ok) {
          const errorData = await response.text();
          console.error('Server response:', response.status, errorData);
          throw new Error(`Server returned ${response.status}: ${errorData}`);
        }
        
        const data = await response.json();
        if (!data.recent_activities) {
          data.recent_activities = [];
        }
        if (!data.investment_criteria) {
          data.investment_criteria = {
            asset_types: [],
            deal_size: { min: 0, max: 0 },
            markets: []
          };
        }
        
        console.log('Fetched contact details:', data);
        setContact(data);
        
        // If the contact has a company_id, fetch company data directly
        if (data.company_id) {
          await fetchCompanyById(data.company_id);
        } 
        // Otherwise, try to fetch by company name if available
        else if (data.company_name) {
          await fetchCompanyByName(data.company_name);
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error('Failed to fetch contact details:', error);
        setLoading(false);
      }
    };

    const fetchCompanyById = async (companyId: number) => {
      try {
        setCompanyLoading(true);
        console.log('Fetching company data for ID:', companyId);
        
        const response = await fetch(`/api/companies/${companyId}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch company data: ${response.status}`);
        }
        
        const companyData = await response.json();
        
        // Normalize scraped data if it exists
        
        console.log('Fetched company data by ID:', companyData);
        console.log('Company data:', companyData);
        setCompanyData(companyData);
      } catch (error) {
        console.error('Failed to fetch company data by ID:', error);
      } finally {
        setCompanyLoading(false);
        setLoading(false);
      }
    };

    const fetchCompanyByName = async (companyName: string) => {
      try {
        setCompanyLoading(true);
        console.log('Searching company by name:', companyName);
        
        // First try to find company by name
        const response = await fetch(`/api/companies/search?name=${encodeURIComponent(companyName)}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch company data: ${response.status}`);
        }
        
        const companies = await response.json();
        
        if (companies && companies.length > 0) {
          // Get full company details for the first match
          await fetchCompanyById(companies[0].company_id);
        } else {
          console.log('No companies found with name:', companyName);
          setCompanyLoading(false);
          setLoading(false);
        }
      } catch (error) {
        console.error('Failed to fetch company by name:', error);
        setCompanyLoading(false);
        setLoading(false);
      }
    };

    if (contactId) {
      fetchContactDetails();
    }
  }, [contactId]);

  // Add new useEffect for fetching messages
  useEffect(() => {
    const fetchThreadsAndMessages = async () => {
      if (!contactId) return;
      
      try {
        setLoadingThreads(true);
        // Use the same formatting for contactId
        const formattedContactId = typeof contactId === 'number' ? contactId : contactId;
        console.log('Fetching threads and messages for contact:', formattedContactId);
        
        const response = await fetch(`/api/contacts/${formattedContactId}/messages`);
        
        if (!response.ok) {
          const errorData = await response.text();
          console.error('Server response:', response.status, errorData);
          throw new Error(`Server returned ${response.status}: ${errorData}`);
        }
        
        const data = await response.json();
        console.log('Fetched threads and messages:', data);
        setThreads(data.threads || []);
        
        // Select the first thread by default if available
        if (data.threads && data.threads.length > 0) {
          setSelectedThreadId(data.threads[0].thread_id);
        }
      } catch (error) {
        console.error('Failed to fetch threads and messages:', error);
      } finally {
        setLoadingThreads(false);
      }
    };
    
    fetchThreadsAndMessages();
  }, [contactId]);

  // Add new function to fetch Smartlead data
  const fetchSmartleadData = async () => {
    if (!contactId) return;
    
    try {
      setSmartleadLoading(true);
      setSmartleadError(null);
      
      // Format contactId consistently
      const formattedContactId = typeof contactId === 'number' ? contactId : contactId;
      const response = await fetch(`/api/smartlead/contacts/${formattedContactId}/sync?sort=${sortOption}&order=${sortOrder}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server response error:', response.status, errorText);
        setSmartleadError(`Failed to fetch Smartlead data: Server returned ${response.status}`);
        return;
      }
      
      const data = await response.json();
      console.log('Fetched Smartlead data:', data);
      setSmartleadData(data);
    } catch (error) {
      console.error('Failed to fetch Smartlead data:', error);
      setSmartleadError(`Failed to fetch Smartlead data: ${(error as Error).message}`);
    } finally {
      setSmartleadLoading(false);
    }
  };

  // Add function to trigger Smartlead sync
  const triggerSmartleadSync = async (campaignId?: string, subject?: string, body?: string) => {
    if (!contactId) return;
    
    try {
      setSmartleadSyncing(true);
      setSmartleadError(null);
      
      // Build payload based on provided parameters
      const payload: any = {};
      if (campaignId) payload.campaign_id = campaignId;
      if (subject && body) {
        payload.subject = subject;
        payload.body = body;
      }
      
      // Format contactId consistently
      const formattedContactId = typeof contactId === 'number' ? contactId : contactId;
      const response = await fetch(`/api/smartlead/contacts/${formattedContactId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Server response error:', response.status, errorData);
        setSmartleadError(errorData.error || `Failed to sync with Smartlead: Server returned ${response.status}`);
        return;
      }
      
      const data = await response.json();
      console.log('Smartlead sync result:', data);
      
      // Refresh Smartlead data after sync
      await fetchSmartleadData();
      
      // Update contact data with new Smartlead info
      if (contact && data.contact) {
        setContact({
          ...contact,
          smartlead_lead_id: data.contact.smartlead_lead_id,
          smartlead_status: data.contact.smartlead_status,
        });
      }
    } catch (error) {
      console.error('Failed to sync with Smartlead:', error);
      setSmartleadError(`Failed to sync with Smartlead: ${(error as Error).message}`);
    } finally {
      setSmartleadSyncing(false);
    }
  };

  // Add useEffect to fetch Smartlead data
  useEffect(() => {
    if (contactId && !loading) {
      fetchSmartleadData();
    }
  }, [contactId, loading, sortOption, sortOrder]);

  // Add delete message function
  const deleteMessage = async () => {
    if (!messageToDelete) return;
    
    try {
      setLoadingThreads(true);
      
      const response = await fetch(`/api/messages/${messageToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete message: ${response.status} - ${errorText}`);
      }
      
      // Update the threads data by removing the deleted message
      setThreads(threads.map(thread => {
        if (thread.messages.some(msg => msg.message_id === messageToDelete)) {
          return {
            ...thread,
            messages: thread.messages.filter(msg => msg.message_id !== messageToDelete)
          };
        }
        return thread;
      }));
      
      toast.success("Message deleted successfully");
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error(`Failed to delete message: ${(error as Error).message}`);
    } finally {
      setMessageToDelete(null);
      setLoadingThreads(false);
    }
  };

  // Start editing a message
  const startEditMessage = (message: Message) => {
    setEditingMessageId(message.message_id);
    setEditMessageSubject(message.subject || '');
    setEditMessageBody(message.body || '');
    setTimeout(() => {
      editTextareaRef.current?.focus();
    }, 100);
  };

  // Cancel editing
  const cancelEditMessage = () => {
    setEditingMessageId(null);
    setEditMessageSubject('');
    setEditMessageBody('');
  };

  // Save edited message
  const saveEditMessage = async (syncToSmartlead = false) => {
    if (!editingMessageId) return;
    setEditSaving(true);
    try {
      const response = await fetch(`/api/messages/${editingMessageId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subject: editMessageSubject, body: editMessageBody })
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update message: ${response.status} - ${errorText}`);
      }
      const data = await response.json();
      // Update the threads state with the edited message
      setThreads(threads.map(thread => {
        if (thread.messages.some(msg => msg.message_id === editingMessageId)) {
          return {
            ...thread,
            messages: thread.messages.map(msg =>
              msg.message_id === editingMessageId
                ? { ...msg, subject: editMessageSubject, body: editMessageBody }
                : msg
            )
          };
        }
        return thread;
      }));
      toast.success('Message updated successfully');
      
      // Optionally sync to Smartlead after saving
      if (syncToSmartlead) {
        await triggerSmartleadSync(undefined, editMessageSubject, editMessageBody);
        toast.success('Message synced to Smartlead');
      }
      
      cancelEditMessage();
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error(`Failed to update message: ${(error as Error).message}`);
    } finally {
      setEditSaving(false);
    }
  };

  // Add a function to sync a specific message to Smartlead
  const syncMessageToSmartlead = async (message: Message) => {
    if (!message.subject || !message.body) {
      toast.error("Message must have both subject and body to sync");
      return;
    }
    
    try {
      await triggerSmartleadSync(undefined, message.subject, message.body);
      toast.success("Message synced to Smartlead successfully");
    } catch (error) {
      console.error('Failed to sync message to Smartlead:', error);
      toast.error(`Failed to sync message: ${(error as Error).message}`);
    }
  };

  // Add this function to handle message editing
  const handleEditMessage = (message: any) => {
    setEditingMessageId(message.message_id);
    setMessageContent(message.body || '');
  };

  const handleSaveMessage = async (messageId: string) => {
    if (!messageEditorRef.current) return;
    
    const content = messageEditorRef.current.getContent();
    
    try {
      const response = await fetch(`/api/messages/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          body: content,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update message: ${response.status}`);
      }
      
      // Update local state
      setThreads(threads.map(thread => {
        if (thread.thread_id === selectedThreadId) {
          return {
            ...thread,
            messages: thread.messages.map((msg: any) => 
              msg.message_id === messageId 
                ? { ...msg, body: content } 
                : msg
            )
          };
        }
        return thread;
      }));
      
      setEditingMessageId(null);
      setMessageContent('');
      
      toast.success('Message updated successfully');
    } catch (error) {
      console.error('Error updating message:', error);
      toast.error(`Failed to update message: ${(error as Error).message}`);
    }
  };

  if (loading || !contact) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading contact information...</p>
        </div>
      </div>
    );
  }

  const initials = `${contact.first_name[0]}${contact.last_name[0]}`;
  const fullName = `${contact.first_name} ${contact.last_name}`;
  const dealSize = contact.investment_criteria?.deal_size;
  const dealSizeText = dealSize ?   
    `$${dealSize.min || 0}M - $${dealSize.max || 0}M` : 
    'Not specified';
  const normalizedScrapedData = normalizeScrapedData(companyData?.scraped_data);

  // Add the new Smartlead tab content
  const renderSmartleadContent = () => {
    if (smartleadLoading) {
      return (
        <div className="text-center py-8">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-gray-600">Loading Smartlead data...</p>
          </div>
        </div>
      );
    }

    if (smartleadError) {
      return (
        <Card className="bg-white shadow-sm">
          <CardContent className="pt-6">
            <div className="text-center py-6">
              <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Smartlead Data</h3>
              <p className="text-red-500 mb-4">{smartleadError}</p>
              <Button onClick={fetchSmartleadData} variant="outline" className="mx-auto">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (!smartleadData || !smartleadData.hasSmartleadData) {
      return (
        <Card className="bg-white shadow-sm">
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-700 mb-2">Not Synced with Smartlead</h3>
              <p className="text-gray-500 max-w-md mx-auto mb-6">
                This contact hasn't been synced to Smartlead yet. Sync to create a lead in Smartlead and track email interactions.
              </p>
              {/* Only show sync button if contact doesn't have a Smartlead lead ID */}
              {!contact.smartlead_lead_id ? (
                <Button 
                  onClick={() => triggerSmartleadSync()} 
                  disabled={smartleadSyncing || !contact?.email || !contact?.email_generated}
                  className="mx-auto"
                >
                  {smartleadSyncing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Syncing...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Sync to Smartlead
                    </>
                  )}
                </Button>
              ) : (
                <div className="flex justify-center">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1 py-2 px-3">
                    <CheckCircle2 size={16} />
                    Already synced to Smartlead
                  </Badge>
                </div>
              )}
              {!contact?.email && (
                <p className="text-red-500 text-sm mt-4">Contact has no email address</p>
              )}
              {contact?.email && !contact?.email_generated && (
                <p className="text-red-500 text-sm mt-4">Contact email has not been validated</p>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }

    // Contact has Smartlead data
    return (
      <div className="space-y-6">
        {/* Smartlead Status Card */}
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium">Smartlead Status</CardTitle>
              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <label htmlFor="sort-option" className="text-sm text-gray-500">Sort by:</label>
                  <select 
                    id="sort-option" 
                    className="text-sm border rounded p-1"
                    value={sortOption}
                    onChange={(e) => setSortOption(e.target.value)}
                  >
                    <option value="created_at">Created Date</option>
                    <option value="updated_at">Updated Date</option>
                    <option value="subject">Subject</option>
                    <option value="status">Status</option>
                  </select>
                  <select 
                    id="sort-order" 
                    className="text-sm border rounded p-1"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value)}
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </div>
                <Button 
                  onClick={() => triggerSmartleadSync()} 
                  disabled={smartleadSyncing || (contact.smartlead_lead_id ? true : false)} 
                  size="sm" 
                  variant="outline"
                >
                  {smartleadSyncing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Syncing...
                    </>
                  ) : contact.smartlead_lead_id ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Already Synced
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Sync to Smartlead
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Lead ID</div>
                <div className="font-medium">
                  {smartleadData.contact.smartlead_lead_id || 'Not assigned'}
                </div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Status</div>
                <div className="flex items-center">
                  <Badge className={`
                    ${smartleadData.contact.smartlead_status === 'SENT' ? 'bg-blue-100 text-blue-800' : 
                      smartleadData.contact.smartlead_status === 'DELIVERED' ? 'bg-green-100 text-green-800' : 
                      smartleadData.contact.smartlead_status === 'OPENED' ? 'bg-yellow-100 text-yellow-800' :
                      smartleadData.contact.smartlead_status === 'REPLIED' ? 'bg-purple-100 text-purple-800' :
                      smartleadData.contact.smartlead_status === 'BOUNCED' ? 'bg-red-100 text-red-800' :
                      smartleadData.contact.smartlead_status === 'UNSUBSCRIBED' ? 'bg-gray-100 text-gray-800' : 
                      'bg-gray-100 text-gray-800'
                    }`
                  }>
                    {smartleadData.contact.smartlead_status || 'Unknown'}
                  </Badge>
                </div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500 mb-1">Last Email Sent</div>
                <div className="font-medium">
                  {smartleadData.contact.last_email_sent_at 
                    ? new Date(smartleadData.contact.last_email_sent_at).toLocaleString() 
                    : 'No emails sent'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Threads Card */}
        {smartleadData.threads.length > 0 ? (
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">Email Threads</CardTitle>
              <CardDescription>
                {smartleadData.threads.length} thread{smartleadData.threads.length !== 1 ? 's' : ''} found for this contact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {smartleadData.threads.map((thread) => (
                  <div key={thread.thread_id} className="border rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b">
                      <div className="flex justify-between">
                        <h3 className="font-medium">{thread.subject || 'No Subject'}</h3>
                        <Badge variant="outline" className="bg-white">
                          {thread.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        Created: {new Date(thread.created_at).toLocaleString()}
                        {thread.metadata?.campaign_id && (
                          <span className="ml-4">Campaign ID: {thread.metadata.campaign_id}</span>
                        )}
                      </div>
                    </div>
                    {thread.messages && thread.messages.length > 0 ? (
                      <div className="divide-y">
                        {thread.messages.map((message: any) => (
                          <div key={message.message_id} className="p-4">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <div className="font-medium">{message.subject || 'No Subject'}</div>
                                <div className="text-sm text-gray-500">
                                  {message.from_email} → {message.to_email}
                                </div>
                              </div>
                              <Badge variant="outline" className={`
                                ${message.direction === 'outbound' ? 'bg-blue-50 text-blue-700' : 'bg-green-50 text-green-700'}
                              `}>
                                {message.direction === 'outbound' ? 'Sent' : 'Received'}
                              </Badge>
                            </div>
                            <div className="mt-2 bg-gray-50 p-3 rounded text-sm whitespace-pre-wrap">
                              {message.body}
                            </div>
                            <div className="text-xs text-gray-500 mt-2">
                              {message.sent_at 
                                ? new Date(message.sent_at).toLocaleString() 
                                : new Date(message.created_at).toLocaleString()}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-500">
                        No messages in this thread
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-white shadow-sm">
            <CardContent className="pt-6">
              <div className="text-center py-6">
                <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">No Email Threads Found</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  This contact has been synced to Smartlead, but no email threads have been created yet.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Header */}
      <header className="bg-white border-b shadow-sm">
        <div className="container mx-auto py-4 px-6">
          <div className="flex items-center">
            <Button variant="ghost" onClick={onBack} size="sm" className="mr-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <h1 className="text-lg font-semibold text-gray-600">ANAX</h1>
          </div>
        </div>
      </header>
      
      {/* Contact Hero Section */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            <div className="flex items-center">
              <Avatar className="h-16 w-16 bg-blue-500 text-white font-bold text-xl">
                <AvatarFallback>{initials}</AvatarFallback>
              </Avatar>
              <div className="ml-4">
                <h1 className="text-2xl font-bold">{fullName}</h1>
                <div className="flex items-center mt-1 text-gray-600">
                  <Building2 className="h-4 w-4 mr-2" />
                  <span>{contact.job_title || 'No title'} at {contact.company_name || 'No company'}</span>
                </div>
                <div className="flex mt-2 gap-2">
                  {contact.extracted && (
                    <Badge className="bg-green-600 hover:bg-green-700 text-white border-0 flex items-center gap-1 px-3 py-1">
                      <CheckCircle className="h-3.5 w-3.5" />
                      Extracted
                    </Badge>
                  )}
                  {contact.searched && (
                    <Badge className="bg-blue-600 hover:bg-blue-700 text-white border-0 flex items-center gap-1 px-3 py-1">
                      <Database className="h-3.5 w-3.5" />
                      Searched
                    </Badge>
                  )}
                  {contact.email_status && (
                    <Badge className="bg-purple-600 hover:bg-purple-700 text-white border-0 flex items-center gap-1 px-3 py-1">
                      <Mail className="h-3.5 w-3.5" />
                      {contact.email_status}
                    </Badge>
                  )}
                  {contact.email_generated && (
                    <Badge className="bg-purple-600 hover:bg-purple-700 text-white border-0 flex items-center gap-1 px-3 py-1">
                      <Mail className="h-3.5 w-3.5" />
                      Email Generated
                    </Badge>
                  )}
                  {contact.smartlead_lead_id && (
                    <Badge className="bg-green-600 hover:bg-green-700 text-white border-0 flex items-center gap-1 px-3 py-1">
                      <Send className="h-3.5 w-3.5" />
                      Smartlead Lead
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <Phone className="h-4 w-4 mr-2" />
                Call
              </Button>
              <Button variant="outline" size="sm">
                <Mail className="h-4 w-4 mr-2" />
                Email
              </Button>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Meeting
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white border mb-6 p-1 rounded-md">
            <TabsTrigger value="overview" className="rounded px-4 py-2">Overview</TabsTrigger>
            <TabsTrigger value="company" className="rounded px-4 py-2">Company Overview</TabsTrigger>
            <TabsTrigger value="searched" className="rounded px-4 py-2">Searched Info</TabsTrigger>
            <TabsTrigger value="messages" className="rounded px-4 py-2">Messages</TabsTrigger>
            <TabsTrigger value="smartlead" className="rounded px-4 py-2">Smartlead</TabsTrigger>
            <TabsTrigger value="training" className="rounded px-4 py-2">Training Data</TabsTrigger>
            <TabsTrigger value="deals" className="rounded px-4 py-2">Deals</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Top Information Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Contact Information */}
              <Card className="bg-white shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-medium">Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 text-sm">
                  <div className="flex items-start">
                    <Mail className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                    <div>
                      {contact.email ? (
                        <span>{contact.email}</span>
                      ) : (
                        <span className="text-gray-500">No email available</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <Phone className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                    <div>
                      {contact.phone_number ? (
                        <span>{contact.phone_number}</span>
                      ) : (
                        <span className="text-gray-500">No phone number available</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                    <div>
                      {contact.company_city || contact.company_state ? (
                        <span>{[contact.company_city, contact.company_state].filter(Boolean).join(', ')}</span>
                      ) : (
                        <span className="text-gray-500">No location available</span>
                      )}
                    </div>
                  </div>
                  
                  {contact.source && (
                    <div className="flex items-start">
                      <Briefcase className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                      <div>
                        <span>Source: {contact.source}</span>
                      </div>
                    </div>
                  )}
          </CardContent>
        </Card>

              {/* Investment Criteria */}
        <Card className="bg-white shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-medium">Investment Criteria</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 text-sm">
                  <div>
                    <div className="text-gray-500 mb-1">Asset Types</div>
                    <div>
                      {contact.investment_criteria?.asset_types?.length ? (
                        <div className="flex flex-wrap gap-2">
                          {contact.investment_criteria.asset_types.map((type, index) => (
                            <Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700">
                              {type}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <span className="text-gray-500">Not specified</span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-gray-500 mb-1">Deal Size</div>
                    <div className="font-medium">{dealSizeText}</div>
                  </div>
                  
                  <div>
                    <div className="text-gray-500 mb-1">Markets</div>
                    <div>
                      {contact.investment_criteria?.markets?.length ? (
                        <span>{contact.investment_criteria.markets.join(', ')}</span>
                      ) : (
                        <span className="text-gray-500">Not specified</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Information */}
              <Card className="bg-white shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-medium">Company Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 text-sm">
                  <div className="flex items-start">
                    <Building2 className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                    <div>
                      <span className="font-medium">{contact.company_name || 'Not specified'}</span>
                    </div>
                  </div>
                  
                  {contact.industry && (
                    <div className="flex items-start">
                      <BarChart className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                      <div>
                        <span>Industry: {contact.industry}</span>
                      </div>
                    </div>
                  )}
                  
                  {contact.company_website && (
                    <div className="flex items-start">
                      <Link className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                      <div>
                        <a 
                          href={contact.company_website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center"
                        >
                          {contact.company_website}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
            </div>
                    </div>
                  )}
          </CardContent>
        </Card>
            </div>

            {/* Executive Summary */}
            {contact.executive_summary && (
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">Executive Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm leading-relaxed whitespace-pre-line">{contact.executive_summary}</p>
                </CardContent>
              </Card>
            )}

            {/* Career Timeline */}
            {Array.isArray(contact.career_timeline) && contact.career_timeline.length > 0 && (
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">Career Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {contact.career_timeline.map((item, index) => {
                      // Parse string entries like "2015 – Founder & CEO at YieldStreet"
                      let role = '';
                      let company = '';
                      let period = '';
                      
                      if (typeof item === 'string') {
                        // Try to extract year/period
                        const yearMatch = item.match(/^(\d{4}(?:\s*[-–—]\s*\d{4}|\s*[-–—]\s*present)?)/i);
                        period = yearMatch ? yearMatch[0] : '';
                        
                        // Remove period from string
                        let remaining = period ? item.substring(period.length).trim() : item;
                        
                        // Remove any dash/separator after the year
                        remaining = remaining.replace(/^[-–—]\s*/, '');
                        
                        // Try to extract role and company with "at" separator
                        if (remaining.includes(' at ')) {
                          const parts = remaining.split(' at ');
                          role = parts[0].trim();
                          company = parts.slice(1).join(' at ').trim();
                        } else {
                          role = remaining;
                        }
                      } else {
                        // Handle object format
                        role = item.role || item.position || '';
                        company = item.company || item.organization || '';
                        period = item.period || item.date || item.timeframe || '';
                      }
                      
                      return (
                        <div key={index} className="flex items-start">
                          <div className="flex-shrink-0 w-6 flex justify-center">
                            <div className="h-4 w-4 rounded-full bg-blue-600 mt-1"></div>
                          </div>
                          <div className="ml-4">
                            <div className="font-medium">{role || 'Role'}</div>
                            {company && (
                              <div className="text-gray-700">{company}</div>
                            )}
                            {period && (
                              <div className="text-gray-500 text-xs mt-1">{period}</div>
                            )}
                            {typeof item === 'object' && item.description && (
                              <div className="mt-2 text-sm text-gray-600">
                                {item.description}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Notable Activities */}
              {Array.isArray(contact.notable_activities) && contact.notable_activities.length > 0 && (
                <Card className="bg-white shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-medium">Notable Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {contact.notable_activities.map((item, index) => {
                        let content = '';
                        if (typeof item === 'string') {
                          content = item;
                        } else if (item.title && item.snippet) {
                          content = `${item.title}: ${item.snippet}`;
                        } else if (item.activity || item.description) {
                          content = item.activity || item.description;
                        } else {
                          content = JSON.stringify(item);
                        }
                        
    return (
                          <li key={index} className="relative pl-6 text-sm">
                            <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
                            {content}
                            {item.url && (
                              <a 
                                href={item.url} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="ml-1 text-blue-600 hover:underline inline-flex items-center"
                              >
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </a>
                            )}
                            {item.date && (
                              <span className="text-xs text-gray-500 ml-2">({item.date})</span>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Personal Tidbits */}
              {Array.isArray(contact.personal_tidbits) && contact.personal_tidbits.length > 0 && (
        <Card className="bg-white shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-medium">Personal Tidbits</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {contact.personal_tidbits.map((item, index) => (
                        <li key={index} className="relative pl-6 text-sm">
                          <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
                          {typeof item === 'string' ? item : item.tidbit || item.description || JSON.stringify(item)}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
                </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Conversation Hooks */}
              {Array.isArray(contact.conversation_hooks) && contact.conversation_hooks.length > 0 && (
                <Card className="bg-white shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-medium">Conversation Hooks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {contact.conversation_hooks.map((item, index) => (
                        <li key={index} className="relative pl-6 text-sm">
                          <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
                          {typeof item === 'string' ? item : item.hook || item.description || JSON.stringify(item)}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}

              {/* Sources */}
              {Array.isArray(contact.sources) && contact.sources.length > 0 && (
                <Card className="bg-white shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-medium">Sources</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {contact.sources.map((source, index) => {
                        const sourceUrl = typeof source === 'string' ? source : source.url || source.link;
                        const sourceName = typeof source === 'string' ? source : source.name || sourceUrl;
                        
                        return (
                          <li key={index} className="relative pl-6 text-sm">
                            <span className="absolute left-0 top-1 h-3 w-3 text-blue-600">•</span>
                            {sourceUrl ? (
                              <a 
                                href={sourceUrl} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="text-blue-600 hover:underline inline-flex items-center"
                              >
                                {sourceName}
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </a>
                            ) : (
                              <span>{sourceName || JSON.stringify(source)}</span>
                            )}
                          </li>
                        );
                      })}
                    </ul>
                  </CardContent>
                </Card>
              )}
              </div>

            {/* Outreach Draft */}
            {contact.outreach_draft && (
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">Outreach Draft</CardTitle>
                  <CardDescription>Use this as a starting point for your outreach message</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-200 text-sm whitespace-pre-line">
                    {typeof contact.outreach_draft === 'string' 
                      ? contact.outreach_draft 
                      : contact.outreach_draft.content || JSON.stringify(contact.outreach_draft)}
            </div>
                </CardContent>
              </Card>
            )}

            {/* Recent Activity - Optional based on data */}
            {contact.recent_activities && contact.recent_activities.length > 0 && (
              <Card className="bg-white shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
                  <div className="space-y-4">
                    {contact.recent_activities.map((activity, index) => (
                      <ActivityItem 
                        key={index}
                        icon={getActivityIcon(activity.interaction_type)}
                        title={activity.interaction_type}
                        description={activity.notes}
                        time={formatDate(activity.interaction_date)}
                      />
                    ))}
                </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="company" className="space-y-8">
            {companyLoading ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-gray-600">Loading company information...</p>
              </div>
              </div>
            ) : companyData?.scraped_data ? (
              <div className="space-y-6">
                {/* Company Overview Card */}
                <div className="space-y-6">
                                <div className="space-y-6">
                  {/* Company Profile Section */}
                  {(normalizedScrapedData.companyProfile && 
                    (normalizedScrapedData.companyProfile.companyName || 
                     normalizedScrapedData.companyProfile.companyType || 
                     normalizedScrapedData.companyProfile.businessModel || 
                     normalizedScrapedData.companyProfile.headquarters || 
                     (normalizedScrapedData.companyProfile.investmentFocus && normalizedScrapedData.companyProfile.investmentFocus.length > 0) || 
                     (normalizedScrapedData.companyProfile.geographicFocus && normalizedScrapedData.companyProfile.geographicFocus.length > 0))) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <Globe className="h-5 w-5 text-red-500" />
                          Company Profile
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-5">
                        {/* Company Profile Core Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Company website */}
                          {normalizedScrapedData.companyProfile?.companyWebsite && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Company Website:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.companyWebsite}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.companyName && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Company Name:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.companyName}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.companyType && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Company Type:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.companyType}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.aum && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">AUM:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.aum}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.fundSize && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Fund Size:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.fundSize}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.headquarters && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Headquarters:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.headquarters}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.numberOfEmployees && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Employees:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.numberOfEmployees}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.foundedYear && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Founded:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.foundedYear}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.numberOfProperties && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Properties:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.numberOfProperties}</span>
                            </div>
                          )}
                          {normalizedScrapedData.companyProfile?.numberOfOffices && (
                            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
                              <span className="font-medium text-gray-700 min-w-32">Offices:</span>
                              <span className="ml-2 text-gray-800">{normalizedScrapedData.companyProfile.numberOfOffices}</span>
                            </div>
                          )}
                        </div>

                        {/* Business Model - Text element (Highlighted) */}
                        {normalizedScrapedData.companyProfile?.businessModel && (
                          <div className="mt-5 pt-4 border-t border-red-100">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <Activity className="h-4 w-4 mr-2 text-red-500" />
                              Business Model:
                            </h4>
                            <p className="text-gray-800 bg-red-50 p-4 rounded-md border-l-4 border-red-300">
                              {normalizedScrapedData.companyProfile.businessModel}
                            </p>
                          </div>
                        )}
                        
                        {/* Office Locations */}
                        {normalizedScrapedData.companyProfile?.officeLocations && normalizedScrapedData.companyProfile.officeLocations.length > 0 && (
                          <div className="mt-5 pt-4 border-t border-red-100">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <MapPin className="h-4 w-4 mr-2 text-red-500" />
                              Office Locations:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {normalizedScrapedData.companyProfile.officeLocations.map((location, idx) => (
                                <Badge key={idx} className="bg-gray-100/80 text-gray-800 hover:bg-gray-200 transition-colors py-1 px-2.5">
                                  {location}
                  </Badge>
                              ))}
                </div>
              </div>
                        )}
                        
                        {/* Investment Focus */}
                        {normalizedScrapedData.companyProfile?.investmentFocus && normalizedScrapedData.companyProfile.investmentFocus.length > 0 && (
                          <div className="mt-5 pt-4 border-t border-red-100">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <Activity className="h-4 w-4 mr-2 text-red-500" />
                              Investment Focus:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {normalizedScrapedData.companyProfile.investmentFocus.map((focus, idx) => (
                                <Badge key={idx} className="bg-blue-100/70 text-blue-800 hover:bg-blue-200 transition-colors py-1 px-2.5">{focus}</Badge>
                              ))}
                </div>
              </div>
                        )}
                        
                        {/* Geographic Focus */}
                        {normalizedScrapedData.companyProfile?.geographicFocus && normalizedScrapedData.companyProfile.geographicFocus.length > 0 && (
                          <div className="mt-5 pt-4 border-t border-red-100">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <MapPin className="h-4 w-4 mr-2 text-red-500" />
                              Geographic Focus:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {normalizedScrapedData.companyProfile.geographicFocus.map((geo, idx) => (
                                <Badge key={idx} className="bg-green-100/70 text-green-800 hover:bg-green-200 transition-colors py-1 px-2.5">{geo}</Badge>
                              ))}
            </div>
                          </div>
                        )}
          </CardContent>
        </Card>
                  )}

                  {/* Executive Team Section */}
                  {normalizedScrapedData.executiveTeam && normalizedScrapedData.executiveTeam.length > 0 && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <Users className="h-5 w-5 text-red-500" />
                          Executive Team
                        </CardTitle>
            </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y divide-red-100">
                          {normalizedScrapedData.executiveTeam.map((exec, idx) => (
                            <div key={idx} className="p-4 hover:bg-red-50/40 transition-colors">
                              <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-10 w-10 bg-red-100 text-red-800 border border-red-200">
                                    <AvatarFallback>{exec.first_name?.[0]}{exec.last_name?.[0]}</AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <h3 className="font-semibold text-gray-900">{exec.full_name || `${exec.first_name} ${exec.last_name}`}</h3>
                                    {exec.title && <p className="text-sm text-gray-600">{exec.title}</p>}
                                  </div>
                                </div>
                                <div className="flex space-x-3 mt-2 sm:mt-0">
                                  {exec.email && (
                                    <a href={`mailto:${exec.email}`} className="text-blue-600 hover:text-blue-800 flex items-center px-3 py-1 rounded-full bg-blue-50 hover:bg-blue-100 transition-colors">
                                      <Mail className="h-4 w-4 mr-1" />
                                      <span className="text-sm">Email</span>
                                    </a>
                                  )}
                                  {exec.linkedin_url && (
                                    <a href={exec.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 flex items-center px-3 py-1 rounded-full bg-blue-50 hover:bg-blue-100 transition-colors">
                                      <LinkedinIcon className="h-4 w-4 mr-1" />
                                      <span className="text-sm">LinkedIn</span>
                                    </a>
                                  )}
                                </div>
                              </div>
                              {(exec.phone || exec.bio) && (
                                <div className="mt-3 text-sm ml-13">
                                  {exec.phone && (
                                    <div className="flex items-center text-gray-700 mt-1">
                                      <Phone className="h-4 w-4 mr-2 text-red-400" />
                                      <span>{exec.phone}</span>
                                    </div>
                                  )}
                                  {exec.bio && (
                                    <div className="mt-2 text-gray-700 bg-gray-50 p-3 rounded-md border-l-2 border-red-300">
                                      <p>{exec.bio}</p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Investment Strategy Section */}
                  {(normalizedScrapedData.investmentStrategy && normalizedScrapedData.investmentStrategy.mission && normalizedScrapedData.investmentStrategy.approach && normalizedScrapedData.investmentStrategy.targetReturn && normalizedScrapedData.investmentStrategy.propertyTypes && normalizedScrapedData.investmentStrategy.strategies && normalizedScrapedData.investmentStrategy.assetClasses && normalizedScrapedData.investmentStrategy.valueCreation) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <DollarSign className="h-5 w-5 text-red-500" />
                          Investment Strategy
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-5">
                        {/* Text elements */}
                        {normalizedScrapedData.investmentStrategy.mission && (
                          <div className="mb-5">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <Activity className="h-4 w-4 mr-2 text-red-500" />
                              Mission:
                            </h4>
                            <p className="text-gray-800 bg-gray-50 p-3 rounded-md border-l-2 border-red-300">
                              {normalizedScrapedData.investmentStrategy.mission}
                            </p>
                          </div>
                        )}
                        
                        {normalizedScrapedData.investmentStrategy.approach && (
                          <div className="mb-5">
                            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                              <Activity className="h-4 w-4 mr-2 text-red-500" />
                              Approach:
                            </h4>
                            <p className="text-gray-800 bg-gray-50 p-3 rounded-md border-l-2 border-red-300">
                              {normalizedScrapedData.investmentStrategy.approach}
                            </p>
                          </div>
                        )}
                        
                        {/* One-liner elements */}
                        {normalizedScrapedData.investmentStrategy.targetReturn && (
                          <div className="flex items-center p-2 mb-4 rounded-md bg-gray-50 transition-colors">
                            <span className="font-medium text-gray-700 min-w-32">Target Return:</span>
                            <span className="ml-2 text-gray-800">{normalizedScrapedData.investmentStrategy.targetReturn}</span>
                          </div>
                        )}
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                          {/* Array elements - displayed in columns */}
                          {normalizedScrapedData.investmentStrategy.propertyTypes && normalizedScrapedData.investmentStrategy.propertyTypes.length > 0 && (
                            <div className="bg-white p-4 rounded-lg border border-red-100 shadow-sm">
                              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                                <Globe className="h-4 w-4 mr-2 text-red-500" />
                                Property Types:
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                {normalizedScrapedData.investmentStrategy.propertyTypes.map((type, idx) => (
                                  <Badge key={idx} className="bg-blue-50 text-blue-800 hover:bg-blue-100 transition-colors">{type}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {normalizedScrapedData.investmentStrategy.strategies && normalizedScrapedData.investmentStrategy.strategies.length > 0 && (
                            <div className="bg-white p-4 rounded-lg border border-red-100 shadow-sm">
                              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                                <Activity className="h-4 w-4 mr-2 text-red-500" />
                                Strategies:
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                {normalizedScrapedData.investmentStrategy.strategies.map((strategy, idx) => (
                                  <Badge key={idx} className="bg-indigo-50 text-indigo-800 hover:bg-indigo-100 transition-colors">{strategy}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {normalizedScrapedData.investmentStrategy.assetClasses && normalizedScrapedData.investmentStrategy.assetClasses.length > 0 && (
                            <div className="bg-white p-4 rounded-lg border border-red-100 shadow-sm">
                              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                                <DollarSign className="h-4 w-4 mr-2 text-red-500" />
                                Asset Classes:
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                {normalizedScrapedData.investmentStrategy.assetClasses.map((assetClass, idx) => (
                                  <Badge key={idx} className="bg-green-50 text-green-800 hover:bg-green-100 transition-colors">{assetClass}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {normalizedScrapedData.investmentStrategy.valueCreation && normalizedScrapedData.investmentStrategy.valueCreation.length > 0 && (
                            <div className="bg-white p-4 rounded-lg border border-red-100 shadow-sm">
                              <h4 className="font-medium text-gray-700 mb-2 flex items-center">
                                <Activity className="h-4 w-4 mr-2 text-red-500" />
                                Value Creation:
                              </h4>
                              <div className="flex flex-wrap gap-2">
                                {normalizedScrapedData.investmentStrategy.valueCreation.map((value, idx) => (
                                  <Badge key={idx} className="bg-amber-50 text-amber-800 hover:bg-amber-100 transition-colors">{value}</Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Recent Deals Section */}
                  {normalizedScrapedData.recentDeals && normalizedScrapedData.recentDeals.length > 0 && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <DollarSign className="h-5 w-5 text-red-500" />
                          Recent Deals
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y divide-red-100">
                          {normalizedScrapedData.recentDeals.map((deal, idx) => (
                            <div key={idx} className="p-5 hover:bg-red-50/30 transition-colors">
                              <div className="flex justify-between items-start mb-3">
                                <div>
                                  <h3 className="font-semibold text-gray-900 flex items-center">
                                    {deal.property || 'Unnamed Property'}
                                    {deal.propertyType && (
                                      <Badge className="ml-2 bg-blue-100 text-blue-800">
                                        {deal.propertyType}
                        </Badge>
                                    )}
                                  </h3>
                                  {deal.location && (
                                    <div className="flex items-center text-sm text-gray-600 mt-1">
                                      <MapPin className="h-4 w-4 mr-1 text-red-400" />
                                      {deal.location}
                      </div>
                        )}
                      </div>
                                <div className="flex items-center">
                                  {deal.date && (
                                    <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 flex items-center">
                                      <Calendar className="h-3 w-3 mr-1" />
                                      {deal.date}
                                    </span>
                                  )}
                    </div>
                              </div>

                              <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg">
                                {deal.dealType && (
                                  <div className="flex items-center">
                                    <span className="text-sm font-medium text-gray-700">Deal Type:</span>
                                    <Badge className="ml-2 bg-red-100/60 text-red-800 hover:bg-red-100">
                                      {deal.dealType}
                                    </Badge>
                                  </div>
                                )}
                                {deal.amount && (
                                  <div className="flex items-center">
                                    <span className="text-sm font-medium text-gray-700">Amount:</span>
                                    <span className="ml-2 text-gray-800 font-medium text-sm">{deal.amount}</span>
                                  </div>
                                )}
                                {deal.squareFeet && (
                                  <div className="flex items-center">
                                    <span className="text-sm font-medium text-gray-700">Size:</span>
                                    <span className="ml-2 text-gray-800 text-sm">{deal.squareFeet}</span>
                                  </div>
                                )}
                                {typeof deal.units === 'number' && (
                                  <div className="flex items-center">
                                    <span className="text-sm font-medium text-gray-700">Units:</span>
                                    <span className="ml-2 text-gray-800 text-sm">{deal.units}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Track Record Section */}
                  {(normalizedScrapedData.trackRecord && 
                    (normalizedScrapedData.trackRecord.totalTransactions || 
                     normalizedScrapedData.trackRecord.totalSquareFeet || 
                     normalizedScrapedData.trackRecord.totalUnits || 
                     normalizedScrapedData.trackRecord.historicalReturns || 
                     normalizedScrapedData.trackRecord.portfolioValue)) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <Activity className="h-5 w-5 text-red-500" />
                          Track Record
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-5">
                        <div className="grid grid-cols-1 gap-3">
                          {normalizedScrapedData.trackRecord.totalTransactions && (
                            <div className="flex items-center p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors">
                              <span className="font-medium text-gray-700 min-w-48">Total Transactions:</span>
                              <span className="ml-2 text-gray-800 font-medium">{normalizedScrapedData.trackRecord.totalTransactions}</span>
                            </div>
                          )}
                          {normalizedScrapedData.trackRecord.totalSquareFeet && (
                            <div className="flex items-center p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors">
                              <span className="font-medium text-gray-700 min-w-48">Total Square Feet:</span>
                              <span className="ml-2 text-gray-800 font-medium">{normalizedScrapedData.trackRecord.totalSquareFeet}</span>
                            </div>
                          )}
                          {normalizedScrapedData.trackRecord.totalUnits && (
                            <div className="flex items-center p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors">
                              <span className="font-medium text-gray-700 min-w-48">Total Units:</span>
                              <span className="ml-2 text-gray-800 font-medium">{normalizedScrapedData.trackRecord.totalUnits}</span>
                            </div>
                          )}
                          {normalizedScrapedData.trackRecord.historicalReturns && (
                            <div className="flex items-center p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors">
                              <span className="font-medium text-gray-700 min-w-48">Historical Returns:</span>
                              <span className="ml-2 text-gray-800 font-medium">{normalizedScrapedData.trackRecord.historicalReturns}</span>
                            </div>
                          )}
                          {normalizedScrapedData.trackRecord.portfolioValue && (
                            <div className="flex items-center p-3 rounded-md bg-gray-50 hover:bg-gray-100 transition-colors">
                              <span className="font-medium text-gray-700 min-w-48">Portfolio Value:</span>
                              <span className="ml-2 text-gray-800 font-medium">{normalizedScrapedData.trackRecord.portfolioValue}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Financial Products Section */}
                  {(normalizedScrapedData.financialProducts && Array.isArray(normalizedScrapedData.financialProducts) && normalizedScrapedData.financialProducts.length > 0 && normalizedScrapedData.financialProducts.some(p => p.productType && p.typicalAmount && p.description && Array.isArray(p.terms) && p.terms.length > 0)) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <DollarSign className="h-5 w-5 text-red-500" />
                          Financial Products
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-5">
                        <div className="divide-y divide-red-100">
                          {normalizedScrapedData.financialProducts.map((product, idx) => (
                            <div key={idx} className="p-5 hover:bg-red-50/30 transition-colors">
                              <div className="flex justify-between items-start mb-3">
                                <div>
                                  <h3 className="font-semibold text-gray-900">
                                    {product.productType}
                                  </h3>
                                  {product.typicalAmount && (
                                    <div className="flex items-center text-sm text-gray-600 mt-1">
                                      <DollarSign className="h-4 w-4 mr-1 text-red-400" />
                                      {product.typicalAmount}
                            </div>
                          )}
                                </div>
                              </div>
                              
                              {product.description && (
                                <div className="mb-3 text-gray-700 bg-gray-50 p-3 rounded-md border-l-2 border-red-300">
                                  <p>{product.description}</p>
                            </div>
                          )}

                              {product.terms && product.terms.length > 0 && (
                                <div>
                                  <h4 className="text-sm font-medium text-gray-700 mb-2">Terms:</h4>
                                  <ul className="list-disc pl-5 space-y-1 text-sm text-gray-700">
                                    {product.terms.map((term, termIdx) => (
                                      <li key={termIdx}>{term}</li>
                                    ))}
                                  </ul>
                            </div>
                          )}
                        </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Capital Sources Section */}
                  {(normalizedScrapedData.capitalSources && Array.isArray(normalizedScrapedData.capitalSources) && normalizedScrapedData.capitalSources.length > 0 && normalizedScrapedData.capitalSources.some(source => source && source.trim().length > 0)) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-red-50 to-red-100/50">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <Database className="h-5 w-5 text-red-500" />
                          Capital Sources
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-5">
                        <div className="flex flex-wrap gap-2">
                          {normalizedScrapedData.capitalSources.map((source, idx) => (
                            <Badge key={idx} className="bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors py-1.5 px-3">
                              {source}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Partnerships Section */}
                  {(normalizedScrapedData.partnerships && Array.isArray(normalizedScrapedData.partnerships) && normalizedScrapedData.partnerships.length > 0 && normalizedScrapedData.partnerships.some(p => p.partnerName && p.relationshipType && p.description)) && (
                    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-red-50 to-red-100/50">
                      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
                        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
                          <Users className="h-5 w-5 text-red-500" />
                          Partnerships
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y divide-red-100">
                          {normalizedScrapedData.partnerships.map((partnership, idx) => (
                            <div key={idx} className="p-5 hover:bg-red-50/30 transition-colors">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                  <h3 className="font-semibold text-gray-900">
                                    {partnership.partnerName}
                                  </h3>
                                  {partnership.relationshipType && (
                                    <div className="mt-1">
                                      <Badge className="bg-green-100 text-green-800">
                                        {partnership.relationshipType}
                                      </Badge>
                                </div>
                              )}
                              </div>
                              </div>
                              
                              {partnership.description && (
                                <div className="mt-3 text-gray-700 bg-gray-50 p-3 rounded-md border-l-2 border-red-300">
                                  <p>{partnership.description}</p>
                          </div>
                        )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
                {/* ... existing scraped data content ... */}
              </div>
              </div>
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">No company overview found</h3>
                <p className="text-gray-500">This contact doesn't have any company overview associated.</p>
              </div>
            )}
            
          </TabsContent>
          
          {/* New Searched Info Tab */}
          <TabsContent value="searched" className="space-y-8">
            {contact.searched ? (
              <div className="grid grid-cols-1 gap-6">
                <Card className="bg-white shadow-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg font-medium">Search Results</CardTitle>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        <FileSearch className="h-3 w-3 mr-1" />
                        {contact.searched_date ? new Date(contact.searched_date).toLocaleDateString() : 'No date'}
                              </Badge>
                            </div>
                    <CardDescription>
                      AI-generated information from searching the web about {fullName}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {contact.searched_tokens_used && (
                      <div className="text-xs text-gray-500 mb-4">
                        Tokens used: {contact.searched_tokens_used}
                            </div>
                    )}
                    <div className="prose max-w-none">
                      {contact.searched_profile ? (
                        <div className="whitespace-pre-wrap">{contact.searched_profile}</div>
                      ) : (
                        <div className="text-gray-500 italic">No search profile available</div>
                      )}
                            </div>
                  </CardContent>
                </Card>

                {contact.searched_input_data && (
                  <Card className="bg-white shadow-sm">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg font-medium">Search Parameters</CardTitle>
                      <CardDescription>
                        Information used to generate the search results
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(contact.searched_input_data).map(([key, value]) => (
                          <div key={key} className="space-y-1">
                            <div className="text-sm font-medium text-gray-700">{key.replace(/_/g, ' ')}</div>
                            <div className="text-sm text-gray-600">{String(value || 'None')}</div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
                      </div>
                    ) : (
              <Card className="bg-white shadow-sm">
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <FileSearch className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700 mb-2">No Search Data Available</h3>
                    <p className="text-gray-500 max-w-md mx-auto">
                      This contact hasn't been processed through the search system yet. Run the contact search script to generate search information.
                    </p>
                      </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          {/* New Messages Tab */}
          <TabsContent value="messages" className="space-y-8">
            {loadingThreads ? (
              <Card className="bg-white shadow-sm">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-gray-600">Loading messages...</p>
                  </div>
                </CardContent>
              </Card>
            ) : threads.length > 0 ? (
              <div className="grid grid-cols-1 gap-6">
                {/* Smartlead Status Card */}
                {contact.smartlead_lead_id && smartleadData && (
                  <Card className="bg-white shadow-sm">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg font-medium">Smartlead Status</CardTitle>
                        <Button 
                          onClick={() => triggerSmartleadSync()} 
                          disabled={smartleadSyncing || !contact?.email || !contact?.email_generated} 
                          size="sm" 
                          variant="outline"
                        >
                          {smartleadSyncing ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Syncing...
                            </>
                          ) : (
                            <>
                              <Send className="h-4 w-4 mr-2" />
                              Re-sync to Smartlead
                            </>
                          )}
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-500 mb-1">Lead ID</div>
                          <div className="font-medium">
                            {smartleadData.contact.smartlead_lead_id || 'Not assigned'}
                          </div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-500 mb-1">Status</div>
                          <div className="flex items-center">
                            <Badge className={`
                              ${smartleadData.contact.smartlead_status === 'SENT' ? 'bg-blue-100 text-blue-800' : 
                                smartleadData.contact.smartlead_status === 'DELIVERED' ? 'bg-green-100 text-green-800' : 
                                smartleadData.contact.smartlead_status === 'OPENED' ? 'bg-yellow-100 text-yellow-800' :
                                smartleadData.contact.smartlead_status === 'REPLIED' ? 'bg-purple-100 text-purple-800' :
                                smartleadData.contact.smartlead_status === 'BOUNCED' ? 'bg-red-100 text-red-800' :
                                smartleadData.contact.smartlead_status === 'UNSUBSCRIBED' ? 'bg-gray-100 text-gray-800' : 
                                'bg-gray-100 text-gray-800'
                              }`
                            }>
                              {smartleadData.contact.smartlead_status || 'Unknown'}
                            </Badge>
                          </div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <div className="text-sm text-gray-500 mb-1">Last Email Sent</div>
                          <div className="font-medium">
                            {smartleadData.contact.last_email_sent_at 
                              ? new Date(smartleadData.contact.last_email_sent_at).toLocaleString() 
                              : 'No emails sent'}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
                
                <Card className="bg-white shadow-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">Conversations</h3>
                      <div className="flex items-center space-x-4">
                        {/* Only show sync button if contact doesn't have a Smartlead lead ID */}
                        {!contact.smartlead_lead_id && (
                          <Button 
                            onClick={() => triggerSmartleadSync()} 
                            disabled={smartleadSyncing || !contact?.email || !contact?.email_generated}
                            size="sm" 
                            variant="outline"
                          >
                            {smartleadSyncing ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Syncing...
                              </>
                            ) : (
                              <>
                                <Send className="h-4 w-4 mr-2" />
                                Sync to Smartlead
                              </>
                            )}
                          </Button>
                        )}
                        {/* Show Smartlead status if already synced */}
                        {contact.smartlead_lead_id && (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1">
                            <CheckCircle2 size={14} />
                            Synced to Smartlead
                          </Badge>
                        )}
                        <select
                          value={messagesSort}
                          onChange={(e) => setMessagesSort(e.target.value)}
                          className="border rounded px-2 py-1 text-sm"
                        >
                          <option value="newest">Newest First</option>
                          <option value="oldest">Oldest First</option>
                        </select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="divide-y divide-gray-100">
                      {threads.map(thread => (
                        <div 
                          key={thread.thread_id}
                          className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${selectedThreadId === thread.thread_id ? 'bg-blue-50' : ''}`}
                          onClick={() => setSelectedThreadId(thread.thread_id)}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900 truncate">
                                {thread.subject || 'No Subject'}
                              </h3>
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(thread.updated_at).toLocaleString()}
                              </div>
                              <div className="flex mt-2 gap-2">
                                <Badge variant="outline" className={`text-xs ${thread.status === 'open' ? 'bg-green-50 text-green-700' : 'bg-gray-50 text-gray-700'}`}>
                                  {thread.status}
                                </Badge>
                                {/* Add Smartlead campaign badge if available */}
                                {thread.metadata?.source === 'smartlead' && (
                                  <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs">
                                    <Send className="h-3 w-3 mr-1" />
                                    Smartlead
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="ml-2">
                              <Badge className="bg-blue-100 text-blue-800">
                                {thread.messages.length}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                {/* Message Display */}
                <Card className="bg-white shadow-sm md:col-span-2">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg font-medium">
                        {selectedThreadId && threads.find(t => t.thread_id === selectedThreadId)?.subject || 'Thread Messages'}
                      </CardTitle>
                      {selectedThreadId && threads.find(t => t.thread_id === selectedThreadId)?.metadata?.source === 'smartlead' && (
                        <div className="flex items-center">
                          <Badge className="bg-blue-100 text-blue-800 flex items-center">
                            <Send className="h-3.5 w-3.5 mr-1" />
                            Campaign: {threads.find(t => t.thread_id === selectedThreadId)?.metadata?.campaign_id || 'Unknown'}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="max-h-[600px] overflow-y-auto p-4">
                    {selectedThreadId ? (
                      <div className="space-y-4">
                        {threads
                          .find(t => t.thread_id === selectedThreadId)
                          ?.messages.map((message: any) => (
                            <div 
                              key={message.message_id}
                              className={`p-4 rounded-lg ${
                                message.direction === 'outbound' ? 'bg-blue-50 ml-12' : 'bg-gray-50 mr-12'
                              } relative group`}
                            >
                              <div className="flex justify-between items-start mb-2">
                                <div className="font-medium text-sm">
                                  {message.direction === 'outbound' ? 'Sent' : 'Received'} - {new Date(message.created_at).toLocaleString()}
                                </div>
                                <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                                  {editingMessageId !== message.message_id && (
                                    <>
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        onClick={() => handleEditMessage(message)}
                                        className="text-blue-600"
                                      >
                                        <Edit className="h-4 w-4 mr-1" />
                                        Edit
                                      </Button>

                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        onClick={() => setMessageToDelete(message.message_id)}
                                        className="text-red-600"
                                      >
                                        <Trash2 className="h-4 w-4 mr-1" />
                                        Delete
                                      </Button>
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        onClick={() => syncMessageToSmartlead(message)}
                                        className="text-green-600"
                                      >
                                        <Send className="h-4 w-4 mr-1" />
                                        Sync
                                      </Button>
                                    </>
                                  )}
                                </div>
                              </div>
                              
                              {editingMessageId === message.message_id ? (
                                <div className="space-y-2">
                                  <RichTextEditor
                                    ref={messageEditorRef}
                                    value={messageContent}
                                    onChange={setMessageContent}
                                    height={200}
                                  />
                                  <div className="flex justify-end gap-2 mt-2">
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      onClick={() => setEditingMessageId(null)}
                                    >
                                      Cancel
                                    </Button>
                                    <Button 
                                      size="sm"
                                      onClick={() => handleSaveMessage(message.message_id)}
                                    >
                                      Save Changes
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div 
                                  className="rich-text-content prose max-w-none"
                                  dangerouslySetInnerHTML={{ __html: message.body }}
                                />
                              )}
                            </div>
                          ))}
                      </div>
                    ) : (
                      <div className="text-center py-10 text-gray-500">
                        <MessageSquare className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                        <p>Select a thread to view messages</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : contact?.email_generated ? (
              <Card className="bg-white shadow-sm">
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <MessageCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700 mb-2">No Messages Available</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                      This contact doesn't have any message history yet. You can sync them to Smartlead to start email campaigns.
                    </p>
                    <Button 
                      onClick={() => triggerSmartleadSync()} 
                      disabled={smartleadSyncing}
                      className="mx-auto"
                    >
                      {smartleadSyncing ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Syncing to Smartlead...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Sync to Smartlead
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="bg-white shadow-sm">
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <AlertCircle className="h-12 w-12 mx-auto text-amber-500 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700 mb-2">Email Not Verified</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-2">
                      This contact's email needs to be verified before sending messages.
                    </p>
                    {!contact?.email && (
                      <p className="text-red-500 text-sm">No email address found for this contact.</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="smartlead" className="space-y-8">
            {renderSmartleadContent()}
          </TabsContent>
          
          <TabsContent value="training">
            {loading ? (
              <Card className="bg-white shadow-sm">
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-gray-600">Loading contact information...</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <TrainingDataTab 
                contactId={contactId}
                contact={contact}
                normalizedScrapedData={normalizedScrapedData ?? undefined}
              />
            )}
          </TabsContent>
          
          <TabsContent value="deals">
            <Card className="bg-white shadow-sm">
              <CardContent className="p-8 text-center text-gray-500">
                <p>Deals information will be shown here</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Add Delete Message Confirmation Dialog */}
      <AlertDialog open={Boolean(messageToDelete)} onOpenChange={() => setMessageToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Message</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this message? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setMessageToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteMessage} className="bg-red-600 hover:bg-red-700">Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      </div>
    );
  };

const ActivityItem: React.FC<ActivityProps> = ({ icon, title, description, time }) => (
  <div className="flex items-start space-x-3">
    <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center text-blue-600">
      {icon}
    </div>
    <div className="flex-1">
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
    <div className="text-sm text-gray-500">{time}</div>
  </div>
);

// Helper function to get appropriate icon for activity type
const getActivityIcon = (type: string) => {
  switch (type?.toLowerCase() || '') {
    case 'email':
      return <Mail className="h-4 w-4" />;
    case 'meeting':
      return <Calendar className="h-4 w-4" />;
    case 'call':
      return <Phone className="h-4 w-4" />;
    default:
      return <MessageSquare className="h-4 w-4" />;
  }
};

// Helper function to format dates
const formatDate = (date: string) => {
  if (!date) return '';
  
  try {
    const d = new Date(date);
    const now = new Date();
    const diff = now.getTime() - d.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    return d.toLocaleDateString();
  } catch (e) {
    return date;
  }
};

export default ContactDetail;