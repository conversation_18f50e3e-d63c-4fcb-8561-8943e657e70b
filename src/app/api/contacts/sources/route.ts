import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET() {
  try {
    const query = `
      SELECT source, COUNT(*) AS count
      FROM contacts
      WHERE source IS NOT NULL
        AND source <> ''
      GROUP BY source
      ORDER BY count DESC;
    `

    const result = await pool.query(query)
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch contact sources' },
      { status: 500 }
    )
  }
} 