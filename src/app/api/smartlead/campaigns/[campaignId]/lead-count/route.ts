import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch just the lead count for a specific campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    // Build API URL - we only need 1 record to get the total count
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}&limit=1`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching lead count for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { count: 0 },
        { status: 200 } // Still return 200 with count 0 to avoid errors in UI
      );
    }

    const data = await response.json();
    // Get the total count from the response
    const totalLeads = data.total_leads ? parseInt(data.total_leads, 10) : 0;
    
    return NextResponse.json({ count: totalLeads });
  } catch (error) {
    console.error(`Error fetching lead count for campaign:`, error);
    return NextResponse.json(
      { count: 0 },
      { status: 200 } // Still return 200 with count 0 to avoid errors in UI
    );
  }
} 