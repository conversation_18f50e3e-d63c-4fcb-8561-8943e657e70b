import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    console.log('API route called with ID:', contactId, 'Type:', typeof contactId);

    const contactQuery = `
      SELECT 
        co.contact_id::integer AS contact_id,
        co.title as job_title,
        co.*,
        c.company_id,
        c.company_name,
        c.industry,
        c.company_address,
        c.company_city,
        c.company_state,
        c.company_website,
        c.company_country,
        ced.executive_summary,
        ced.career_timeline,
        ced.notable_activities,
        ced.personal_tidbits,
        ced.conversation_hooks,
        ced.outreach_draft,
        ced.sources,
        csd.profile as searched_profile,
        csd.input_data as searched_input_data,
        csd.tokens_used as searched_tokens_used,
        csd.created_at as searched_date,
        CASE WHEN csd.contact_id IS NOT NULL THEN true ELSE false END as searched,
        CASE WHEN co.email_validated_date IS NOT NULL THEN true ELSE false END as email_generated,
        co.email_validated_date
      FROM contacts co
      LEFT JOIN companies c ON co.company_id = c.company_id
      LEFT JOIN contact_extracted_data ced ON co.contact_id = ced.contact_id
      LEFT JOIN contact_searched_data csd ON co.contact_id = csd.contact_id
      WHERE co.contact_id = $1
    `;

    try {
      console.log('Executing query with contactId:', contactId);
      const contactResult = await pool.query(contactQuery, [contactId]);
      
      if (contactResult.rows.length === 0) {
        console.log('No contact found with ID:', contactId);
        return NextResponse.json(
          { error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Parse potential JSON columns from contact_extracted_data
      const raw = contactResult.rows[0];
      const jsonFields = [
        'career_timeline',
        'notable_activities',
        'personal_tidbits',
        'conversation_hooks',
        'sources',
        'searched_input_data'
      ] as const;

      jsonFields.forEach((field) => {
        const value = (raw as any)[field];
        if (typeof value === 'string') {
          try {
            (raw as any)[field] = JSON.parse(value);
          } catch (parseError) {
            console.error(`Error parsing JSON for field ${field}:`, parseError);
            // keep as-is if not valid JSON
          }
        }
      });

      // Ensure investment_criteria exists and has the correct format
      if (!raw.investment_criteria) {
        raw.investment_criteria = {
          asset_types: [],
          deal_size: { min: 0, max: 0 },
          markets: []
        };
      } else if (typeof raw.investment_criteria === 'string') {
        try {
          raw.investment_criteria = JSON.parse(raw.investment_criteria);
        } catch (parseError) {
          console.error(`Error parsing investment_criteria:`, parseError);
          raw.investment_criteria = {
            asset_types: [],
            deal_size: { min: 0, max: 0 },
            markets: []
          };
        }
      }

      const contactData = {
        ...raw,
        recent_activities: [] // Default to empty array, but don't try to fetch from non-existent table
      };

      return NextResponse.json(contactData);
    } catch (dbError) {
      console.error('Database query error:', dbError);
      return NextResponse.json(
        { error: 'Database query failed', details: (dbError as Error).message }, 
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Route handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message }, 
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    const body = await request.json();
    console.log('PATCH request for contactId:', contactId, 'with body:', body);

    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Update contact basic information if provided
      if (body.first_name || body.last_name || body.email || body.phone_number || body.job_title) {
        const updateFields = [];
        const values = [];
        let valueIndex = 1;
        
        if (body.first_name) {
          updateFields.push(`first_name = $${valueIndex}`);
          values.push(body.first_name);
          valueIndex++;
        }
        
        if (body.last_name) {
          updateFields.push(`last_name = $${valueIndex}`);
          values.push(body.last_name);
          valueIndex++;
        }
        
        if (body.email) {
          updateFields.push(`email = $${valueIndex}`);
          values.push(body.email);
          valueIndex++;
        }
        
        if (body.phone_number) {
          updateFields.push(`phone_number = $${valueIndex}`);
          values.push(body.phone_number);
          valueIndex++;
        }
        
        if (body.job_title) {
          updateFields.push(`title = $${valueIndex}`);
          values.push(body.job_title);
          valueIndex++;
        }
        
        if (updateFields.length > 0) {
          values.push(contactId);
          const updateQuery = `
            UPDATE contacts 
            SET ${updateFields.join(', ')}, updated_at = NOW()
            WHERE contact_id = $${valueIndex}
          `;
          
          await client.query(updateQuery, values);
        }
      }
      
      // Update conversation hooks if provided
      if (body.conversation_hooks) {
        // Check if contact_extracted_data record exists
        const checkQuery = `
          SELECT contact_id FROM contact_extracted_data 
          WHERE contact_id = $1
        `;
        
        const checkResult = await client.query(checkQuery, [contactId]);
        
        if (checkResult.rows.length === 0) {
          // Insert new record if it doesn't exist
          await client.query(
            `INSERT INTO contact_extracted_data (contact_id, conversation_hooks, created_at, updated_at)
             VALUES ($1, $2, NOW(), NOW())`,
            [contactId, JSON.stringify(body.conversation_hooks)]
          );
        } else {
          // Update existing record
          await client.query(
            `UPDATE contact_extracted_data
             SET conversation_hooks = $1, updated_at = NOW()
             WHERE contact_id = $2`,
            [JSON.stringify(body.conversation_hooks), contactId]
          );
        }
      }
      
      await client.query('COMMIT');
      
      return NextResponse.json({ 
        success: true,
        message: 'Contact updated successfully',
        contact_id: contactId
      });
      
    } catch (dbError) {
      await client.query('ROLLBACK');
      console.error('Database update error:', dbError);
      return NextResponse.json(
        { error: 'Failed to update contact', details: (dbError as Error).message },
        { status: 500 }
      );
    } finally {
      client.release();
    }
    
  } catch (error: any) {
    console.error('Route handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
} 