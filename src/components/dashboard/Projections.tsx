"use client"

import React, { useState } from 'react'
import { Assumptions } from './projections/Assumptions'
import { Growth } from './projections/Growth'

interface ProjectionsProps {
  isActive: boolean;
}

export const ProjectionsView: React.FC<ProjectionsProps> = ({ isActive }) => {
  const [activeTab, setActiveTab] = useState('assumptions')
  
  if (!isActive) return null

  return (
    <div className="space-y-6">
      <div>
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('assumptions')}
              className={`${
                activeTab === 'assumptions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
            >
              Projection Model
            </button>
            <button
              onClick={() => setActiveTab('growth')}
              className={`${
                activeTab === 'growth'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium`}
            >
              Revenue Growth
            </button>
          </nav>
        </div>

        <div className="mt-6">
          {activeTab === 'assumptions' && <Assumptions />}
          {activeTab === 'growth' && <Growth />}
        </div>
      </div>
    </div>
  )
}

export default ProjectionsView 