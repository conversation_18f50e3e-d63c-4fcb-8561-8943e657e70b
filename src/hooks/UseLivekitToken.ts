import { useState, useEffect } from 'react';

interface LiveKitConfig {
  token: string;
  serverUrl: string;
}

export function useLivekitToken() {
  const [livekitConfig, setLivekitConfig] = useState<LiveKitConfig | null>(null);

  useEffect(() => {
    async function fetchLivekitToken() {
      try {
        const response = await fetch('/api/livekit-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: 'user-' + Math.random().toString(36).substr(2, 9)
          })
        });

        if (!response.ok) {
          throw new Error('Failed to fetch LiveKit token');
        }

        const data = await response.json();
        setLivekitConfig(data);
      } catch (error) {
        console.error('Error fetching LiveKit token:', error);
      }
    }

    fetchLivekitToken();
  }, []);

  return livekitConfig;
}