import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';
import pLimit from 'p-limit';
import { OpenAIProvider } from '@/lib/llm/OpenAIProvider';
import { LoggerInterface, LogLevel } from '@/lib/llm/BaseLLMProvider';

export const dynamic = 'force-dynamic';

// Global processing state
let isProcessing = false;
let currentProgress = {
  processed: 0,
  total: 0,
  logs: [] as string[],
  errors: [] as { id: number; error: string }[],
  lastUpdate: new Date()
};

// Stats interface
interface ProcessingStats {
  total: number;
  null: number;
  locations: Record<string, number>;
}

// Increase the default timeout for this route
export const maxDuration = 3600; // 1 hour in seconds

// Add a timeout promise helper
const timeout = (ms: number) => new Promise((_, reject) => 
  setTimeout(() => reject(new Error('Operation timed out')), ms)
);

function debugLog(message: string) {
  console.log(message);
  currentProgress.logs.push(logWithTimestamp(message));
}

function logWithTimestamp(message: string) {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] ${message}`;
}

// Create a logger for OpenAI provider
const logger: LoggerInterface = {
  log: (level: LogLevel, message: string) => {
    debugLog(`[OpenAI] [${level.toUpperCase()}] ${message}`);
  }
};

// Initialize OpenAIProvider
const openaiProvider = new OpenAIProvider(logger);

export async function GET(request: Request) {
  const authHeader = request.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET_KEY}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (isProcessing) {
    return NextResponse.json({
      success: false,
      error: 'Location processing already in progress',
      isProcessing,
      ...currentProgress
    });
  }

  isProcessing = true;
  currentProgress = {
    processed: 0,
    total: 0,
    logs: [logWithTimestamp('Starting scheduled location processing...')],
    errors: [],
    lastUpdate: new Date()
  };

  try {
    // Add timeout handling
    await Promise.race([
      processLocations(false, Infinity),
      timeout(3500000) // 58 minutes (slightly less than the route timeout)
    ]);

    isProcessing = false;
    return NextResponse.json({
      success: true,
      message: 'Scheduled location processing completed',
      isProcessing: false,
      ...currentProgress
    });
  } catch (error: any) {
    // Force reset processing state on any error
    isProcessing = false;
    
    // Add error type detection
    const errorMessage = error.message || 'Unknown error';
    const isTimeout = errorMessage.includes('timed out');
    
    currentProgress.logs.push(logWithTimestamp(
      `Error: ${isTimeout ? 'Process timed out' : errorMessage}`
    ));

    return NextResponse.json({
      success: false,
      error: errorMessage,
      isProcessing: false,
      timedOut: isTimeout,
      ...currentProgress
    }, {
      status: isTimeout ? 504 : 500
    });
  } finally {
    // Ensure processing flag is reset even if something goes wrong
    isProcessing = false;
  }
}

export async function POST(request: Request) {
  debugLog('=== POST Request Received ===');

  if (isProcessing) {
    debugLog('Rejected: Already processing');
    return NextResponse.json({
      success: false,
      error: 'Location processing already in progress',
      isProcessing,
      ...currentProgress
    });
  }

  const body = await request.json();
  const { testMode = false, limit = 3 } = body;
  debugLog(`Request params: testMode=${testMode}, limit=${limit}`);

  isProcessing = true;
  currentProgress = {
    processed: 0,
    total: 0,
    logs: [logWithTimestamp('Starting location processing...')],
    errors: [],
    lastUpdate: new Date()
  };

  try {
    debugLog('Starting processLocations');
    await processLocations(testMode, limit);
    debugLog('Completed processLocations');
    isProcessing = false;

    return NextResponse.json({
      success: true,
      message: 'Location processing completed',
      isProcessing: false,
      ...currentProgress
    });
  } catch (error: any) {
    debugLog(`Fatal error in POST handler: ${error.message}`);
    console.error('Stack trace:', error.stack);
    isProcessing = false;
    currentProgress.logs.push(logWithTimestamp(`Fatal error: ${error.message}`));

    return NextResponse.json({
      success: false,
      error: error.message,
      isProcessing: false,
      ...currentProgress
    });
  }
}

async function processLocations(testMode: boolean, maxItems: number) {
  debugLog('=== Process Started ===');

  const stats: ProcessingStats = {
    total: 0,
    null: 0,
    locations: {}
  };

  // Verify OpenAI API key is set
  if (!process.env.OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY is not set');
  }
  debugLog(`OpenAI API Key present: ${!!process.env.OPENAI_API_KEY}`);

  // Test OpenAI connection
  debugLog('Testing OpenAI connection...');
  try {
    const testStart = Date.now();
    const testResponse = await openaiProvider.callLLM([
      { role: 'user', content: 'Say "test"' }
    ], { model: 'gpt-4o-mini' });
    const testDuration = Date.now() - testStart;
    debugLog(`OpenAI test call completed in ${testDuration}ms`);
    debugLog(`Test response: ${testResponse.content}`);
  } catch (error: any) {
    debugLog(`OpenAI test failed: ${error.message}`);
    throw error;
  }

  // Limit parallel API calls
  const concurrencyLimit = pLimit(3);

  try {
    // Use pool.query to get the total count
    const countResult = await pool.query(`
      SELECT COUNT(*) as total
      FROM deal_news
      WHERE 
        processed = true 
        AND location IS NULL 
        AND news_text IS NOT NULL
    `);
    
    const total = parseInt(countResult.rows[0].total);
    const itemsToProcess = testMode ? Math.min(maxItems, total) : total;
    
    currentProgress.total = itemsToProcess;
    debugLog(`Found ${total} items, will process ${itemsToProcess}`);

    // If we have no items to process, exit early
    if (itemsToProcess <= 0) {
      debugLog('No items to process, exiting');
      return;
    }

    // Acquire a client from the pool and keep it for the entire process
    const client = await pool.connect();
    try {
      // Process in batches of 10
      while (currentProgress.processed < itemsToProcess) {
        // Calculate how many we still need to process
        const remaining = itemsToProcess - currentProgress.processed;
        const batchSize = Math.min(remaining, 10);
        
        debugLog(`Processing batch of ${batchSize} items`);
        
        // Get a batch of items
        const { rows } = await client.query(`
          SELECT id, news_text, news_title
          FROM deal_news
          WHERE 
            processed = true 
            AND location IS NULL 
            AND news_text IS NOT NULL
          LIMIT $1
        `, [batchSize]);
        
        if (rows.length === 0) {
          debugLog('No more items found to process');
          break;
        }
        
        // Process each item in parallel with concurrency limit
        const processingTasks = rows.map(row => 
          concurrencyLimit(() => processOneLocation(row, testMode, openaiProvider, stats))
        );
        
        // Wait for all tasks to complete
        const results = await Promise.all(processingTasks);
        
        // Update database with results
        for (const result of results) {
          if (result && !testMode) {
            await client.query(
              `UPDATE deal_news SET location = $1 WHERE id = $2`,
              [result.location, result.id]
            );
          }
          
          // Update progress
          currentProgress.processed++;
          currentProgress.lastUpdate = new Date();
          
          if (result.error) {
            currentProgress.errors.push({
              id: result.id,
              error: result.error
            });
          }
          
          // Check if we've reached the limit in test mode
          if (testMode && currentProgress.processed >= maxItems) {
            debugLog('Reached test mode limit, stopping');
            break;
          }
        }
        
        // Short break between batches to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // Log stats
      debugLog('=== Processing Stats ===');
      debugLog(`Total processed: ${stats.total}`);
      debugLog(`Null locations: ${stats.null}`);
      debugLog('Location distribution:');
      Object.entries(stats.locations)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20)
        .forEach(([location, count]) => {
          debugLog(`  ${location}: ${count}`);
        });
      
    } finally {
      client.release();
      debugLog('Database client released');
    }
    
  } catch (error: any) {
    debugLog(`Processing error: ${error.message}`);
    debugLog(error.stack || 'No stack trace available');
    throw error;
  }
  
  debugLog('=== Process Complete ===');
}

/**
 * Process a single item to extract location information
 */
async function processOneLocation(
  row: any,
  testMode: boolean,
  openaiProvider: OpenAIProvider,
  stats: ProcessingStats
) {
  const resultObj = { 
    id: row.id, 
    location: null as string | null,
    error: null as string | null
  };
  
  try {
    stats.total++;
    
    const text = row.news_text || '';
    const title = row.news_title || '';
    
    // Skip items with very short or no text
    if (text.length < 20) {
      debugLog(`Item ${row.id} has too short text (${text.length}), skipping`);
      stats.null++;
      return resultObj;
    }
    
    // Prepare a simplified version of the text to reduce tokens
    const truncatedText = text.length > 5000 
      ? text.substring(0, 5000) + '... [truncated]' 
      : text;
    
    // Call OpenAI to extract location
    debugLog(`Calling OpenAI API for item ${row.id}`);
    const response = await openaiProvider.callLLM([
      { 
        role: 'system', 
        content: 'You extract location information from real estate news articles.' 
      },
      {
        role: 'user',
        content: `Extract the primary location (city, state, or country) where the real estate deal described in this text took place. If there are multiple locations, prioritize the one that seems most significant. Return ONLY the location name without explanation, analysis, or additional text. If no location can be determined, return "Unknown".

Article title: ${title}

Article text:
${truncatedText}`
      }
    ], {
      model: 'gpt-4o-mini', // Using a smaller, faster model since this is a focused task
      temperature: 0.2, // Lower temperature for more consistent results
    });
    
    // Process the response
    let location: string | null = response.content.trim();
    
    // Normalize the location
    if (!location || location.toLowerCase() === 'unknown' || location.toLowerCase() === 'not specified') {
      location = null;
      stats.null++;
    } else {
      // Remove potential AI formatting like quotes, etc.
      location = location
        .replace(/^["'`]+|["'`]+$/g, '') // Remove quotes at start/end
        .replace(/^The location is\s+/i, '')
        .replace(/^Location:\s+/i, '')
        .trim();
      
      // Count for stats
      stats.locations[location] = (stats.locations[location] || 0) + 1;
    }
    
    debugLog(`Extracted location for item ${row.id}: ${location || 'NULL'}`);
    resultObj.location = location;
    
    return resultObj;
  } catch (error: any) {
    debugLog(`Error processing item ${row.id}: ${error.message}`);
    resultObj.error = error.message;
    return resultObj;
  }
}
