import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// DELETE message by ID
export async function DELETE(
  req: NextRequest,
  context: { params: { messageId: string } }
) {
  const parameters = await context.params;
  const { messageId } = parameters;

  try {
    // Get the message to find the contact_id - Fixed JOIN to include thread_participants
    const messageResult = await pool.query(
      `SELECT m.*, tp.contact_id 
       FROM messages m
       JOIN threads t ON m.thread_id = t.thread_id
       JOIN thread_participants tp ON t.thread_id = tp.thread_id
       WHERE m.message_id = $1
       AND tp.participant_type = 'contact'
       LIMIT 1`,
      [messageId]
    );
    
    if (messageResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }
    
    const contactId = messageResult.rows[0].contact_id;
    
    // Begin transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Delete the message
      await client.query(
        'DELETE FROM messages WHERE message_id = $1',
        [messageId]
      );
      
      // Check if there are any remaining messages for this contact
      // Fixed query to use thread_participants table
      const remainingMessagesResult = await client.query(
        `SELECT COUNT(*) as message_count
         FROM messages m
         JOIN threads t ON m.thread_id = t.thread_id
         JOIN thread_participants tp ON t.thread_id = tp.thread_id
         WHERE tp.contact_id = $1
         AND tp.participant_type = 'contact'`,
        [contactId]
      );
      
      const messageCount = parseInt(remainingMessagesResult.rows[0].message_count, 10);
      
      // If no messages remain, set email_generated to false
      if (messageCount === 0) {
        await client.query(
          `UPDATE contacts
           SET email_generated = false
           WHERE contact_id = $1`,
          [contactId]
        );
      }
      
      // Commit transaction
      await client.query('COMMIT');
      
      return NextResponse.json({
        success: true,
        message: 'Message deleted successfully',
        email_generated_reset: messageCount === 0
      });
    } catch (error) {
      // Rollback transaction on error
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error deleting message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT (edit) a message
export async function PUT(
  req: NextRequest,
  context: { params: { messageId: string } }
) {
  const parameters = await context.params;
  const { messageId } = parameters;

  try {
    const body = await req.json();
    const { subject, body: messageBody, smartlead_campaign_id, metadata } = body;
    
    if (!subject && !messageBody && metadata === undefined) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Build dynamic update query
    const fields = [];
    const values = [];
    let idx = 1;
    
    if (subject !== undefined) {
      fields.push(`subject = $${idx++}`);
      values.push(subject);
    }
    
    if (messageBody !== undefined) {
      fields.push(`body = $${idx++}`);
      values.push(messageBody);
    }
    
    if (smartlead_campaign_id !== undefined) {
      fields.push(`smartlead_campaign_id = $${idx++}`);
      values.push(smartlead_campaign_id);
    }
    
    // Save metadata as JSON field - always add this even if metadata is empty object
    // This ensures the field is always updated rather than sometimes being undefined
    fields.push(`metadata = $${idx++}`);
    values.push(metadata || {});
    
    values.push(messageId);

    const result = await pool.query(
      `UPDATE messages SET ${fields.join(', ')} WHERE message_id = $${idx} RETURNING *`,
      values
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    // NOTE: Removed direct Smartlead sync - this should be handled by the dedicated sync endpoint
    // The frontend components are already using the correct sync endpoint

    return NextResponse.json({
      success: true,
      message: 'Message updated successfully',
      updatedMessage: result.rows[0]
    });
  } catch (error) {
    console.error(`Error updating message ${messageId}:`, error);
    return NextResponse.json(
      { error: `Error updating message: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

// PATCH (edit) a message
export async function PATCH(
  req: NextRequest,
  context: { params: { messageId: string } }
) {
  const parameters = await context.params;
  const { messageId } = parameters;

  try {
    const body = await req.json();
    const { subject, body: messageBody, smartlead_campaign_id } = body;
    if (!subject && !messageBody) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Build dynamic update query
    const fields = [];
    const values = [];
    let idx = 1;
    if (subject !== undefined) {
      fields.push(`subject = $${idx++}`);
      values.push(subject);
    }
    if (messageBody !== undefined) {
      fields.push(`body = $${idx++}`);
      values.push(messageBody);
    }
    if (smartlead_campaign_id !== undefined) {
      fields.push(`smartlead_campaign_id = $${idx++}`);
      values.push(smartlead_campaign_id);
    }
    values.push(messageId);

    const result = await pool.query(
      `UPDATE messages SET ${fields.join(', ')} WHERE message_id = $${idx} RETURNING *`,
      values
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true, 
      message: 'Message updated successfully',
      updatedMessage: result.rows[0]
    });
  } catch (error) {
    console.error(`Error updating message ${messageId}:`, error);
    return NextResponse.json(
      { error: `Error updating message: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 