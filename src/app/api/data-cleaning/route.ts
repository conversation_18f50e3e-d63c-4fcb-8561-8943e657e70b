import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const criteriaFields = [
      'investment_criteria_country',
      'investment_criteria_geographic_region',
      'investment_criteria_state',
      'investment_criteria_city',
      'investment_criteria_deal_size',
      'investment_criteria_property_type',
      'investment_criteria_property_type_subcategory',
      'investment_criteria_asset_type',
      'investment_criteria_loan_type',
      'investment_criteria_loan_type_short_term',
      'investment_criteria_loan_type_long_term',
      'investment_criteria_loan_term_years',
      'investment_criteria_loan_interest_rate_basis',
      'investment_criteria_loan_interest_rate',
      'investment_criteria_loan_to_value',
      'investment_criteria_loan_to_cost',
      'investment_criteria_loan_origination_fee_pct',
      'investment_criteria_loan_exit_fee_pct',
      'investment_criteria_recourse_loan',
      'investment_criteria_loan_dscr',
      'investment_criteria_closing_time',
      'capital_type'
    ]

    const results = await Promise.all(
      criteriaFields.map(async (field) => {
        const query = `
          SELECT DISTINCT ${field}, COUNT(*) as count
          FROM contacts
          WHERE ${field} IS NOT NULL AND ${field} != ''
          GROUP BY ${field}
          ORDER BY count DESC, ${field}
        `
        const result = await pool.query(query)
        return {
          field,
          values: result.rows.map(row => row[field]),
          count: result.rows.length
        }
      })
    )

    return NextResponse.json(results)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: 'Database error' }, { status: 500 })
  }
} 