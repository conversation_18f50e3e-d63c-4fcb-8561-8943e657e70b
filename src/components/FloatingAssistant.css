/* Floating microphone button */
.voice-assistant-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  background: linear-gradient(to bottom, rgb(16, 185, 129), rgb(5, 150, 105)); /* emerald-500 to emerald-600 */
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
  z-index: 1000;
}

.voice-assistant-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
}

/* Voice Assistant Modal */
.voice-assistant-content {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(to bottom right, rgba(236, 253, 245, 0.9), rgba(209, 250, 229, 0.9)); /* from-emerald-50/90 to-emerald-50/50 */
  border-radius: 24px;
  padding: 16px 24px;
  color: #064e3b;  /* emerald-900 */
  min-width: 300px;
  max-width: 400px;
  z-index: 1001;
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  border: 1px solid rgb(167, 243, 208); /* emerald-200 */
  box-shadow: 0 4px 24px rgba(16, 185, 129, 0.2);
}

.voice-assistant-content:hover {
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
}

/* Compact View */
.voice-assistant-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.compact-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assistant-title {
  font-weight: 500;
  font-size: 14px;
  color: #1e3a8a;  /* blue-950 */
}

.timer {
  color: #1e3a8a;  /* blue-950 */
  opacity: 0.7;
  font-size: 14px;
}

/* Expanded View */
.voice-assistant-expanded {
  position: relative;
}

.expanded-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expanded-header h3 {
  font-weight: 500;
  font-size: 16px;
}

.header-controls {
  display: flex;
  gap: 8px;
}

/* Visualization container with glass effect */
.visualization-container {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(167, 243, 208, 0.2); /* emerald-200 with opacity */
  border-radius: 16px;
  margin: 16px 0;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(167, 243, 208, 0.4);
}

.expanded-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

/* Enhanced state indicator */
.state-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgb(16, 185, 129); /* emerald-500 */
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgb(167, 243, 208); /* emerald-200 */
}

.state-icon {
  animation: pulse 2s infinite;
}

.powered-by {
  text-align: center;
  color: rgb(16, 185, 129); /* emerald-500 */
  opacity: 0.8;
  font-size: 12px;
  margin-top: 16px;
}

/* Control Buttons */
.control-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgb(167, 243, 208); /* emerald-200 */
  color: rgb(16, 185, 129); /* emerald-500 */
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

/* Mic Toggle Button */
.mic-toggle-button {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(to bottom, rgb(16, 185, 129), rgb(5, 150, 105)); /* emerald-500 to emerald-600 */
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.mic-toggle-button:hover {
  transform: translateX(-50%) scale(1.05);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.mic-toggle-button.muted {
  background: #374151; /* gray-700 */
}

/* Animations */
@keyframes pulse {
  0% { opacity: 0.4; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1.05); }
  100% { opacity: 0.4; transform: scale(0.95); }
}

/* State-specific styles */
.voice-assistant-content[data-state="listening"] .audio-visualizer {
  --bar-color: rgba(255, 255, 255, 0.9);
}

.voice-assistant-content[data-state="speaking"] .audio-visualizer {
  --bar-color: rgba(255, 255, 255, 0.9);
}

.voice-assistant-content[data-state="thinking"] .audio-visualizer {
  --bar-color: rgba(255, 255, 255, 0.6);
}

/* Subtle glow effect for active states */
.voice-assistant-content[data-state="listening"] .state-indicator {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.voice-assistant-content[data-state="speaking"] .state-indicator {
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.3);
}

/* Add subtle light reflection effect */
.voice-assistant-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 24px 24px 0 0;
  pointer-events: none;
}