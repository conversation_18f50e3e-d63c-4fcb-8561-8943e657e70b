import { LucideIcon } from 'lucide-react';

export type SourceStatus = 'active' | 'pending' | 'error';

export interface Source {
  name: string;
  type: string;
  status: SourceStatus;
  lastChecked: string;
}

export interface NewsItem {
  id: number;
  news_title: string;
  news_date: string;
  news_text: string;
  raw_html: string;
  url: string;
  is_relevant?: boolean;
}

export interface NewsState {
  news: NewsItem[];
  isLoading: boolean;
  selectedNews: NewsItem | null;
  page: number;
  hasMore: boolean;
  totalCount: number;
  lastId?: number;
  error?: string;
  showRawHtml?: boolean;
}

export interface ProcessingResult {
  id: number;
  originalHtml: string;
  cleanedText: string;
  extractedContent: {
    title: string;
    date: string | null;
    body: string;
  };
}

export interface StatusConfig {
  [key: string]: string;
}

export interface StatusIcons {
  [key: string]: LucideIcon;
} 