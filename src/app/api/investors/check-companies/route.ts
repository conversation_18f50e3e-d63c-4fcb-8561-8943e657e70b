import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { companies } = await request.json()

    if (!companies || !Array.isArray(companies)) {
      return NextResponse.json(
        { error: 'Invalid companies array' },
        { status: 400 }
      )
    }

    const client = await pool.connect()
    
    try {
      const existsMap: Record<string, boolean> = {}
      
      if (companies.length > 0) {
        // Create placeholders for the IN clause
        const placeholders = companies.map((_, index) => `$${index + 1}`).join(', ')
        
        const query = `
          SELECT DISTINCT company_name 
          FROM companies 
          WHERE company_name IN (${placeholders})
          AND company_name IS NOT NULL
        `
        
        const result = await client.query(query, companies)
        
        // Initialize all companies as not existing
        companies.forEach(company => {
          existsMap[company] = false
        })
        
        // Mark existing companies as true
        result.rows.forEach(row => {
          existsMap[row.company_name] = true
        })
      }

      return NextResponse.json({
        success: true,
        exists: existsMap
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Company check error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check companies',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 