"use client"

import React, { use<PERSON><PERSON>, use<PERSON>ffe<PERSON> } from 'react'
import { <PERSON>, CardHeader, CardTitle, CardContent } from '../../ui/card'
import { But<PERSON> } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '../../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select'
import { Label } from '../../ui/label'
import { 
  Building, 
  Mail, 
  Target, 
  PenTool,
  CheckCircle2,
  Clock,
  Activity,
  RefreshCw,
  Filter,
  Users,
  Database,
  Zap,
  AlertTriangle,
  AlertCircle,
  TrendingUp,
  BarChart3,
  Sparkles
} from 'lucide-react'
import { ProcessingStats, StageStats } from '../../../types/processing'
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Area
} from 'recharts'
interface TotalCounts {
  total_contacts: number
  total_companies: number
}

interface TimelineData {
  date: string
  contacts_processed: number
  companies_processed: number
  total_processed: number
}

interface FilterState {
  source: string
}

interface StatusValue {
  status_value: string
  count: string
  percentage: string
}

interface StatusGroup {
  name: string
  values: StatusValue[]
}

interface KeyMetric {
  metric: string
  count: string
}

interface DataQualityScores {
  contact_completeness: number
  company_completeness: number
  overall_quality: number
}

interface DataQualityStats {
  contactStatus: StatusGroup[]
  companyStatus: StatusGroup[]
  keyMetrics: KeyMetric[]
  emailFunnel: { stage: string, count: string }[]
  dataQualityScores: DataQualityScores
}

const ProcessingStatusDashboard = () => {
  const [stats, setStats] = useState<ProcessingStats | null>(null)
  const [totals, setTotals] = useState<TotalCounts | null>(null)
  const [timelineData, setTimelineData] = useState<TimelineData[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    source: 'all'
  })
  const [sources, setSources] = useState<{source: string, count: number}[]>([])
  const [statsMetadata, setStatsMetadata] = useState<{
    statsSource: string,
    lastUpdated: string,
    queryInfo?: string
  } | null>(null)
  const [timelineMetadata, setTimelineMetadata] = useState<{
    dataSource?: string,
    lastUpdated?: string,
    queryDetails?: string,
    explanations?: Record<string, string>
  } | null>(null)
  const [dataQualityStats, setDataQualityStats] = useState<DataQualityStats | null>(null)
  const [dataQualityLoading, setDataQualityLoading] = useState(true)
  const [timePeriod, setTimePeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily')
  const [timeSeriesData, setTimeSeriesData] = useState<any[]>([])
  const [analyticsData, setAnalyticsData] = useState<any>(null)
  const [analyticsLoading, setAnalyticsLoading] = useState(false)
  
  // Fetch analytics data when time period changes
  useEffect(() => {
    fetchAnalyticsData()
  }, [timePeriod])
  
  const fetchAnalyticsData = async () => {
    try {
      setAnalyticsLoading(true)
      const response = await fetch(`/api/processing/analytics?period=${timePeriod}`)
      const data = await response.json()
      
             if (data.success) {
         // Process analytics data to convert string numbers to integers
         const processedAnalyticsData = {
           ...data.data,
           stagePerformance: (data.data.stagePerformance || []).map((stage: any) => ({
             ...stage,
             completed: parseInt(stage.completed) || 0,
             failed: parseInt(stage.failed) || 0,
             pending: parseInt(stage.pending) || 0,
             running: parseInt(stage.running) || 0
           })),
           errorAnalysis: (data.data.errorAnalysis || []).map((error: any) => ({
             ...error,
             count: parseInt(error.count) || 0,
             percentage: parseFloat(error.percentage) || 0
           }))
         }
         setAnalyticsData(processedAnalyticsData)
         
         // Convert string numbers to integers for charts
         const processedTimeSeriesData = (data.data.timeSeriesData || []).map((item: any) => ({
           ...item,
           completed: parseInt(item.completed) || 0,
           failed: parseInt(item.failed) || 0,
           pending: parseInt(item.pending) || 0
         }))
         setTimeSeriesData(processedTimeSeriesData)
      } else {
        console.error('Failed to fetch analytics data:', data.error)
        // Fallback to mock data
        generateMockTimeSeriesData()
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error)
      // Fallback to mock data
      generateMockTimeSeriesData()
    } finally {
      setAnalyticsLoading(false)
    }
  }
  
  const generateMockTimeSeriesData = () => {
    const data = []
    const now = new Date()
    const days = timePeriod === 'daily' ? 7 : timePeriod === 'weekly' ? 4 : 12
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now)
      if (timePeriod === 'daily') {
        date.setDate(date.getDate() - i)
      } else if (timePeriod === 'weekly') {
        date.setDate(date.getDate() - (i * 7))
      } else {
        date.setMonth(date.getMonth() - i)
      }
      
      data.push({
        date: date.toISOString(),
        completed: Math.floor(Math.random() * 100) + 50,
        failed: Math.floor(Math.random() * 30) + 10,
        pending: Math.floor(Math.random() * 50) + 20
      })
    }
    setTimeSeriesData(data)
  }

  useEffect(() => {
    fetchData()
    fetchSources()
    fetchDataQualityStats()
    const interval = setInterval(fetchData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters])

  const fetchData = async () => {
    try {
      setRefreshing(true)
      
      // Build filter query string
      const queryParams = new URLSearchParams();
      if (filters.source !== 'all') queryParams.set('source', filters.source);

      // Fetch current stats
      const statsUrl = `/api/processing/stats?${queryParams.toString()}`;
      const statsResponse = await fetch(statsUrl)
      const statsData = await statsResponse.json()
      if (statsData.success) {
        setStats(statsData.data)
        setTotals(statsData.data.totals)
        
        // Set metadata if available
        if (statsData.data.metadata) {
          setStatsMetadata(statsData.data.metadata)
        }
      }

      // Fetch timeline data with filters
      const timelineParams = new URLSearchParams({
        source: filters.source
      })
      
      const timelineResponse = await fetch(`/api/processing/timeline?${timelineParams}`)
      const timelineResult = await timelineResponse.json()
      if (timelineResult.success) {
        setTimelineData(timelineResult.data.timeline)
      }
    } catch (error) {
      console.error('Failed to fetch processing data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const fetchSources = async () => {
    try {
      const response = await fetch('/api/contacts/sources')
      const data = await response.json()
      setSources(Array.isArray(data) ? data : [])
    } catch (error) {
      console.error('Failed to fetch sources:', error)
      setSources([])
    }
  }

  const fetchDataQualityStats = async () => {
    try {
      setDataQualityLoading(true)
      const response = await fetch('/api/data-quality/status')
      const data = await response.json()
      
      if (data.success) {
        setDataQualityStats(data.data)
      } else {
        console.error('Failed to fetch data quality stats:', data.error)
      }
    } catch (error) {
      console.error('Error fetching data quality stats:', error)
    } finally {
      setDataQualityLoading(false)
    }
  }

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      // Auto-refresh data when filters change
      setTimeout(() => fetchData(), 100);
      return newFilters;
    });
  }

  // Helper to format status names for display
  const formatStatusName = (name: string): string => {
    return name
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  const getTotalContacts = () => {
    if (!totals) return 0
    return totals.total_contacts
  }

  const getTotalCompanies = () => {
    if (!totals) return 0
    return totals.total_companies
  }

  const getContactProgressPercentage = () => {
    if (!stats) return 0
    const total = getTotalContacts()
    const completed = stats.contacts.email_sending.completed + stats.contacts.email_generation.completed
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  const getCompanyProgressPercentage = () => {
    if (!stats) return 0
    const total = getTotalCompanies()
    const completed = stats.companies.company_overview.completed
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  // Prepare data for charts - updated for new status system
  const contactProcessingData = stats ? [
    { name: 'Pending Email Verification', value: stats.contacts.email_verification.pending, color: '#EF4444' },
    { name: 'Pending OSINT Research', value: stats.contacts.osint_research.pending, color: '#F59E0B' },
    { name: 'Pending Overview Extraction', value: stats.contacts.overview_extraction.pending, color: '#3B82F6' },
    { name: 'Pending Classification', value: stats.contacts.classification.pending, color: '#6366F1' },
    { name: 'Pending Email Generation', value: stats.contacts.email_generation.pending, color: '#8B5CF6' },
    { name: 'Pending Email Sending', value: stats.contacts.email_sending.pending, color: '#10B981' }
  ] : []

  const companyPipelineData = stats ? [
    { name: 'Pending Website Scraping', value: stats.companies.website_scraping.pending, color: '#EF4444' },
    { name: 'Pending Company Overview', value: stats.companies.company_overview.pending, color: '#F59E0B' }
  ] : []

  // Status breakdown data for detailed charts
  const contactStatusData = stats ? Object.entries(stats.contacts).map(([stage, stageStats]) => ({
    stage: stage.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    pending: stageStats.pending,
    running: stageStats.running,
    completed: stageStats.completed,
    failed: stageStats.failed,
    error: stageStats.error
  })) : []

  const companyStatusData = stats ? Object.entries(stats.companies).map(([stage, stageStats]) => ({
    stage: stage.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    pending: stageStats.pending,
    running: stageStats.running,
    completed: stageStats.completed,
    failed: stageStats.failed,
    error: stageStats.error
  })) : []

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#F59E0B'
      case 'running': return '#3B82F6'
      case 'completed': return '#10B981'
      case 'failed': return '#EF4444'
      case 'error': return '#DC2626'
      default: return '#6B7280'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />
      case 'running': return <RefreshCw className="h-4 w-4 animate-spin" />
      case 'completed': return <CheckCircle2 className="h-4 w-4" />
      case 'failed': return <AlertTriangle className="h-4 w-4" />
      case 'error': return <AlertCircle className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Processing Status Dashboard</h1>
          <p className="text-gray-600 mt-2">Real-time monitoring of processing pipeline status</p>
          {statsMetadata && (
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
              <span>Source: {statsMetadata.statsSource}</span>
              <span>Updated: {new Date(statsMetadata.lastUpdated).toLocaleString()}</span>
              {statsMetadata.queryInfo && <span>{statsMetadata.queryInfo}</span>}
            </div>
          )}
        </div>
        <div className="flex items-center gap-3">
          <Button 
            onClick={fetchData} 
            variant="outline"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Global Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 max-w-md">
            <div>
              <Label htmlFor="source">Data Source</Label>
              <Select value={filters.source} onValueChange={(value) => handleFilterChange('source', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All sources" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {sources.map(source => (
                    <SelectItem key={source.source} value={source.source}>
                      {source.source} ({source.count})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Contacts</p>
                  <p className="text-2xl font-bold">{getTotalContacts().toLocaleString()}</p>
                  <p className="text-sm text-green-600">{getContactProgressPercentage()}% processed</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Companies</p>
                  <p className="text-2xl font-bold">{getTotalCompanies().toLocaleString()}</p>
                  <p className="text-sm text-green-600">{getCompanyProgressPercentage()}% processed</p>
                </div>
                <Building className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Contact Errors</p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.errors?.contact_errors?.length || 0}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Company Errors</p>
                  <p className="text-2xl font-bold text-red-600">
                    {stats.errors?.company_errors?.length || 0}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="detailed">Detailed Status</TabsTrigger>
          <TabsTrigger value="errors">Error Analysis</TabsTrigger>
          <TabsTrigger value="quality">Data Quality</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Processing Pipeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Contact Processing Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats && Object.entries(stats.contacts).map(([stage, stageData], index) => {
                    const stageNames = {
                      email_verification: 'Email Verification',
                      osint_research: 'OSINT Research',
                      overview_extraction: 'Overview Extraction',
                      classification: 'Classification',
                      email_generation: 'Email Generation',
                      email_sending: 'Email Sending'
                    }
                    
                    const stageIcons = {
                      email_verification: <Mail className="h-4 w-4" />,
                      osint_research: <Database className="h-4 w-4" />,
                      overview_extraction: <Target className="h-4 w-4" />,
                      classification: <PenTool className="h-4 w-4" />,
                      email_generation: <CheckCircle2 className="h-4 w-4" />,
                      email_sending: <Zap className="h-4 w-4" />
                    }

                    const total = stageData.total
                    const pending = stageData.pending
                    const completed = stageData.completed
                    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0
                    
                    // Get explanation from metadata if available
                    const explanation = stats.metadata?.explanations?.contacts[stage] || null

                    return (
                      <div key={stage} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {stageIcons[stage as keyof typeof stageIcons]}
                            <h3 className="font-medium">{stageNames[stage as keyof typeof stageNames]}</h3>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">{percentage}% complete</div>
                            <div className="text-xs text-gray-500">{completed}/{total}</div>
                          </div>
                        </div>
                        
                        {/* Progress bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        
                        {/* Status breakdown */}
                        <div className="grid grid-cols-5 gap-2 text-xs">
                          <div className="text-center">
                            <div className="text-yellow-600 font-medium">{stageData.pending}</div>
                            <div className="text-gray-500">Pending</div>
                          </div>
                          <div className="text-center">
                            <div className="text-blue-600 font-medium">{stageData.running}</div>
                            <div className="text-gray-500">Running</div>
                          </div>
                          <div className="text-center">
                            <div className="text-green-600 font-medium">{stageData.completed}</div>
                            <div className="text-gray-500">Done</div>
                          </div>
                          <div className="text-center">
                            <div className="text-red-600 font-medium">{stageData.failed}</div>
                            <div className="text-gray-500">Failed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-red-800 font-medium">{stageData.error}</div>
                            <div className="text-gray-500">Error</div>
                          </div>
                        </div>

                        {explanation && (
                          <div className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                            {explanation}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Company Processing Pipeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Company Processing Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats && Object.entries(stats.companies).map(([stage, stageData], index) => {
                    const stageNames = {
                      website_scraping: 'Website Scraping',
                      company_overview: 'Company Overview'
                    }
                    
                    const stageIcons = {
                      website_scraping: <Database className="h-4 w-4" />,
                      company_overview: <Building className="h-4 w-4" />
                    }

                    const total = stageData.total
                    const pending = stageData.pending
                    const completed = stageData.completed
                    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0
                    
                    // Get explanation from metadata if available
                    const explanation = stats.metadata?.explanations?.companies[stage] || null

                    return (
                      <div key={stage} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {stageIcons[stage as keyof typeof stageIcons]}
                            <h3 className="font-medium">{stageNames[stage as keyof typeof stageNames]}</h3>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">{percentage}% complete</div>
                            <div className="text-xs text-gray-500">{completed}/{total}</div>
                          </div>
                        </div>
                        
                        {/* Progress bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        
                        {/* Status breakdown */}
                        <div className="grid grid-cols-5 gap-2 text-xs">
                          <div className="text-center">
                            <div className="text-yellow-600 font-medium">{stageData.pending}</div>
                            <div className="text-gray-500">Pending</div>
                          </div>
                          <div className="text-center">
                            <div className="text-blue-600 font-medium">{stageData.running}</div>
                            <div className="text-gray-500">Running</div>
                          </div>
                          <div className="text-center">
                            <div className="text-green-600 font-medium">{stageData.completed}</div>
                            <div className="text-gray-500">Done</div>
                          </div>
                          <div className="text-center">
                            <div className="text-red-600 font-medium">{stageData.failed}</div>
                            <div className="text-gray-500">Failed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-red-800 font-medium">{stageData.error}</div>
                            <div className="text-gray-500">Error</div>
                          </div>
                        </div>

                        {explanation && (
                          <div className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                            {explanation}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Detailed Status Tab - Daily Processing Dashboard */}
        <TabsContent value="detailed" className="space-y-6">
          {/* Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Daily Processing Dashboard
                  {analyticsLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
                </CardTitle>
                
                <div className="flex items-center gap-4">
                  {/* Time Period Selector */}
                  <div className="flex gap-2">
                    <Button
                      variant={timePeriod === 'daily' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTimePeriod('daily')}
                    >
                      Daily (7d)
                    </Button>
                    <Button
                      variant={timePeriod === 'weekly' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTimePeriod('weekly')}
                    >
                      Weekly (4w)
                    </Button>
                    <Button
                      variant={timePeriod === 'monthly' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTimePeriod('monthly')}
                    >
                      Monthly (12m)
                    </Button>
                  </div>

                  <Button onClick={fetchAnalyticsData} size="sm" variant="outline">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(() => {
              const getTotalsByStatus = () => {
                if (!analyticsData?.stagePerformance) return { completed: 0, failed: 0, pending: 0, running: 0 }
                
                return analyticsData.stagePerformance.reduce((acc: any, stage: any) => ({
                  completed: acc.completed + (parseInt(stage.completed) || 0),
                  failed: acc.failed + (parseInt(stage.failed) || 0),
                  pending: acc.pending + (parseInt(stage.pending) || 0),
                  running: acc.running + (parseInt(stage.running) || 0)
                }), { completed: 0, failed: 0, pending: 0, running: 0 })
              }
              
              const totals = getTotalsByStatus()
              
              return (
                <>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">{totals.completed.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Completed</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">{totals.running.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Running</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-yellow-600">{totals.pending.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Pending</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-red-600">{totals.failed.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Failed</div>
                    </CardContent>
                  </Card>
                </>
              )
            })()}
          </div>

          {/* Processing Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Processing Trends Over Time
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={timeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return timePeriod === 'daily' 
                        ? date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                        : timePeriod === 'weekly'
                        ? `Week ${Math.ceil(date.getDate() / 7)}`
                        : date.toLocaleDateString('en-US', { month: 'short' });
                    }}
                    fontSize={12}
                  />
                  <YAxis fontSize={12} />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                    formatter={(value: number, name: string) => [
                      value.toLocaleString(), 
                      name.charAt(0).toUpperCase() + name.slice(1)
                    ]}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="completed" 
                    stackId="1" 
                    stroke="#22c55e" 
                    fill="#22c55e" 
                    fillOpacity={0.8}
                    name="completed" 
                  />
                  <Area 
                    type="monotone" 
                    dataKey="pending" 
                    stackId="1" 
                    stroke="#f59e0b" 
                    fill="#f59e0b" 
                    fillOpacity={0.8}
                    name="pending" 
                  />
                  <Area 
                    type="monotone" 
                    dataKey="failed" 
                    stackId="1" 
                    stroke="#ef4444" 
                    fill="#ef4444" 
                    fillOpacity={0.8}
                    name="failed" 
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Stage Processing Pipeline */}
          <Card>
            <CardHeader>
              <CardTitle>Processing Pipeline Status</CardTitle>
              <p className="text-sm text-muted-foreground">
                Current status of each processing stage showing the sequential workflow
              </p>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart 
                  data={analyticsData?.stagePerformance?.map((stage: any) => ({
                    ...stage,
                    name: stage.stage.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
                    completed: parseInt(stage.completed) || 0,
                    running: parseInt(stage.running) || 0,
                    pending: parseInt(stage.pending) || 0,
                    failed: parseInt(stage.failed) || 0
                  })) || []} 
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" fontSize={12} />
                  <YAxis 
                    type="category" 
                    dataKey="name"
                    width={120}
                    fontSize={12}
                  />
                  <Tooltip 
                    formatter={(value: number, name: string) => [
                      value.toLocaleString(), 
                      name.charAt(0).toUpperCase() + name.slice(1)
                    ]}
                  />
                  <Bar dataKey="completed" stackId="a" fill="#22c55e" name="completed" />
                  <Bar dataKey="running" stackId="a" fill="#3b82f6" name="running" />
                  <Bar dataKey="pending" stackId="a" fill="#f59e0b" name="pending" />
                  <Bar dataKey="failed" stackId="a" fill="#ef4444" name="failed" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Stage Details Table */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Stage Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Stage</th>
                      <th className="text-center p-3">Completed</th>
                      <th className="text-center p-3">Running</th>
                      <th className="text-center p-3">Pending</th>
                      <th className="text-center p-3">Failed</th>
                      <th className="text-center p-3">Total</th>
                      <th className="text-center p-3">Success Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(() => {
                      const stageConfig = [
                        { key: 'email_verification', name: 'Email Verification', icon: '📧', order: 1 },
                        { key: 'osint', name: 'OSINT Research', icon: '🔍', order: 2 },
                        { key: 'overview_extraction', name: 'Overview Extraction', icon: '📄', order: 3 },
                        { key: 'classification', name: 'Classification', icon: '🏷️', order: 4 },
                        { key: 'email_generation', name: 'Email Generation', icon: '✍️', order: 5 },
                        { key: 'email_sending', name: 'Email Sending', icon: '📤', order: 6 }
                      ]

                      return (analyticsData?.stagePerformance || [])
                        .map((stage: any) => {
                          const config = stageConfig.find(c => c.key === stage.stage)
                          const completed = parseInt(stage.completed) || 0
                          const failed = parseInt(stage.failed) || 0
                          const pending = parseInt(stage.pending) || 0
                          const running = parseInt(stage.running) || 0
                          const total = completed + failed + pending + running
                          const successRate = total > 0 ? (completed / total) * 100 : 0

                          return {
                            ...stage,
                            completed,
                            failed,
                            pending,
                            running,
                            total,
                            successRate,
                            name: config?.name || stage.stage,
                            icon: config?.icon || '📋',
                            order: config?.order || 999
                          }
                        })
                        .sort((a: any, b: any) => a.order - b.order)
                        .map((stage: any, index: number) => (
                          <tr key={stage.stage} className="border-b hover:bg-gray-50">
                            <td className="p-3">
                              <div className="flex items-center gap-3">
                                <div className="text-2xl">{stage.icon}</div>
                                <div>
                                  <div className="font-medium">{stage.name}</div>
                                  <div className="text-xs text-gray-500">Stage {index + 1}</div>
                                </div>
                              </div>
                            </td>
                            <td className="text-center p-3">
                              <span className="inline-block px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                {stage.completed.toLocaleString()}
                              </span>
                            </td>
                            <td className="text-center p-3">
                              <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                {stage.running.toLocaleString()}
                              </span>
                            </td>
                            <td className="text-center p-3">
                              <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                                {stage.pending.toLocaleString()}
                              </span>
                            </td>
                            <td className="text-center p-3">
                              <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                                {stage.failed.toLocaleString()}
                              </span>
                            </td>
                            <td className="text-center p-3 font-medium">{stage.total.toLocaleString()}</td>
                            <td className="text-center p-3">
                              <span className={`font-medium ${
                                stage.successRate >= 80 ? 'text-green-600' : 
                                stage.successRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {stage.successRate.toFixed(1)}%
                              </span>
                            </td>
                          </tr>
                        ))
                    })()}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Throughput Metrics */}
          {analyticsData?.throughput && (
            <Card>
              <CardHeader>
                <CardTitle>Today's Throughput</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {(analyticsData.throughput.contacts_today || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Contacts Today</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {(analyticsData.throughput.avg_per_hour || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Avg/Hour</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {(analyticsData.throughput.peak_hour || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Peak Hour</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {(analyticsData.throughput.avg_hourly || 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Avg Hourly</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>



        {/* Error Analysis Tab */}
        <TabsContent value="errors" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Errors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Recent Contact Errors
                  <Badge variant="destructive">{stats?.errors?.contact_errors?.length || 0}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {stats?.errors?.contact_errors?.map((error, index) => (
                    <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">
                          Contact {error.entity_id} - {error.stage.replace(/_/g, ' ')}
                        </span>
                        <span className="text-xs text-gray-500">
                          {error.retry_count && `Retries: ${error.retry_count}`}
                        </span>
                      </div>
                      <p className="text-sm text-red-700">{error.error_message}</p>
                      {error.occurred_at && (
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(error.occurred_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )) || <p className="text-gray-500">No contact errors found</p>}
                </div>
              </CardContent>
            </Card>

            {/* Company Errors */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Recent Company Errors
                  <Badge variant="destructive">{stats?.errors?.company_errors?.length || 0}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {stats?.errors?.company_errors?.map((error, index) => (
                    <div key={index} className="p-3 border border-red-200 rounded-lg bg-red-50">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">
                          Company {error.entity_id} - {error.stage.replace(/_/g, ' ')}
                        </span>
                        <span className="text-xs text-gray-500">
                          {error.retry_count && `Retries: ${error.retry_count}`}
                        </span>
                      </div>
                      <p className="text-sm text-red-700">{error.error_message}</p>
                      {error.occurred_at && (
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(error.occurred_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  )) || <p className="text-gray-500">No company errors found</p>}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Data Quality Tab - keeping existing functionality */}
        <TabsContent value="quality" className="space-y-4">
          {!dataQualityLoading ? (
            dataQualityStats ? (
              <>
                <Tabs defaultValue="contacts" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="contacts">Contact Status</TabsTrigger>
                    <TabsTrigger value="companies">Company Status</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="contacts" className="space-y-4">
                    {dataQualityStats.contactStatus.map((statusGroup) => (
                      <div key={statusGroup.name} className="border rounded-md p-4">
                        <h3 className="font-medium mb-3">{formatStatusName(statusGroup.name)}</h3>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {statusGroup.values.map((value, idx) => (
                                <tr key={idx}>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{value.status_value || 'null'}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{parseInt(value.count).toLocaleString()}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{value.percentage}%</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="companies" className="space-y-4">
                    {dataQualityStats.companyStatus.map((statusGroup) => (
                      <div key={statusGroup.name} className="border rounded-md p-4">
                        <h3 className="font-medium mb-3">{formatStatusName(statusGroup.name)}</h3>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {statusGroup.values.map((value, idx) => (
                                <tr key={idx}>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{value.status_value || 'null'}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{parseInt(value.count).toLocaleString()}</td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{value.percentage}%</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ))}
                  </TabsContent>
                </Tabs>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-700 mb-2">Could not load data quality stats</h3>
                <p className="text-gray-500 max-w-md">
                  There was an issue loading the data quality statistics. Please try refreshing the page.
                </p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={fetchDataQualityStats}
                >
                  Retry
                </Button>
              </div>
            )
          ) : (
            <div className="flex items-center justify-center h-64">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ProcessingStatusDashboard 