export const CONTACT_OVERVIEW_SYSTEM_PROMPT = `You are an elite open-source intelligence (OSINT) analyst helping me write a hyper-personalized outreach.

Below is everything I already know about a contact, including any OSINT research data previously gathered.
Use public-web sources (news, SEC, Crunchbase, LinkedIn, social, press releases, marriage announcements—anything) to uncover *new, verifiable details*.
Focus on career moves, deals, investments, publications, speaking gigs, awards, side projects, and personal milestones that make good conversation hooks.

Analyze the provided contact information and OSINT research data to compile a detailed professional profile for real estate and investment industry relationship building.`

export const CONTACT_OVERVIEW_USER_TEMPLATE = `### Known data
First_Name: {{first_name}}
Last_Name: {{last_name}}
Primary_Email: {{email}}
Company: {{company}}
Job_Title: {{job_title}}
LinkedIn_Profile: {{linkedin_url}}
Company_Website: {{company_website}}
Country / Region: {{country}} / {{geographic_region}}
Phone_Number: {{phone_number}}
Industry: {{industry}}
Extra_Attributes: {{extra_attrs}}

### OSINT Research Data
{{osint_research}}

### Deliverables (JSON ONLY)
{
  "executive_summary":  "<5-sentence bio based on OSINT and contact data>",
  "career_timeline":    [ "<YYYY> – <role/company>", "…" ],
  "notable_activities": [
    {
      "title":   "<deal / publication / board seat / podcast>",
      "date":    "<YYYY-MM>",
      "snippet": "<1-line description from OSINT data>",
      "url":     "<source from OSINT>"
    }
  ],
  "personal_tidbits":   [ "<education>", "<hobbies>", "<philanthropy>", "…" ],
  "conversation_hooks": [ "<hook #1 based on OSINT findings>", "<hook #2>", "<hook #3>" ],
  "outreach_draft":     "Hi {{first_name}}, … (≤150 words, informal but professional, incorporating research insights)",
  "sources":            [ "<url1 from OSINT>", "<url2>", "…" ]
}

Rules:
• Base analysis primarily on provided OSINT research data
• If OSINT data is limited, clearly note this and work with available contact information
• If a field is unknown, omit it (don't write null)
• Cite every concrete fact in sources
• Focus on real estate and investment industry connections
• Return *one valid JSON object* and nothing else`

// Template function for backward compatibility
export const CONTACT_OVERVIEW_USER_TEMPLATE_FUNCTION = (contact: {
  full_name?: string
  first_name?: string
  last_name?: string
  email: string
  title?: string
  company_name?: string
  company_website?: string
  linkedin_url?: string
  industry?: string
  contact_country?: string
  extra_attrs?: any
}, osintResearch?: Record<string, unknown> | null) => {
  // Extract first and last name
  const nameParts = contact.full_name?.split(' ') || []
  const firstName = nameParts[0] || contact.first_name || ''
  const lastName = nameParts.slice(1).join(' ') || contact.last_name || ''
  
  // Format OSINT research data
  let osintDataText = 'No OSINT research available'
  if (osintResearch && osintResearch.profile) {
    try {
      const profileData = typeof osintResearch.profile === 'string' 
        ? JSON.parse(osintResearch.profile as string)
        : osintResearch.profile
      
      osintDataText = JSON.stringify(profileData, null, 2)
    } catch (error) {
      osintDataText = String(osintResearch.profile) || 'OSINT data format error'
    }
  }

  return CONTACT_OVERVIEW_USER_TEMPLATE
    .replace(/\{\{first_name\}\}/g, firstName)
    .replace(/\{\{last_name\}\}/g, lastName)
    .replace(/\{\{email\}\}/g, contact.email)
    .replace(/\{\{company\}\}/g, contact.company_name || 'Not specified')
    .replace(/\{\{job_title\}\}/g, contact.title || 'Not specified')
    .replace(/\{\{linkedin_url\}\}/g, contact.linkedin_url || 'Not specified')
    .replace(/\{\{company_website\}\}/g, contact.company_website || 'Not specified')
    .replace(/\{\{country\}\}/g, contact.contact_country || 'Not specified')
    .replace(/\{\{geographic_region\}\}/g, contact.contact_country || 'Not specified')
    .replace(/\{\{phone_number\}\}/g, 'Not specified')
    .replace(/\{\{industry\}\}/g, contact.industry || 'Not specified')
    .replace(/\{\{extra_attrs\}\}/g, contact.extra_attrs ? JSON.stringify(contact.extra_attrs) : 'None')
    .replace(/\{\{osint_research\}\}/g, osintDataText)
} 