import { NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET() {
  try {
    // For now, we'll use a default user_id since authentication is not set up
    // This should be replaced with proper authentication later
    const userId = 'default_user';

    // Fetch aggregate metrics
    const aggregateMetricsQuery = `
      SELECT 
        COUNT(*) FILTER (WHERE status = 'sent') as sent,
        COUNT(*) FILTER (WHERE status = 'opened') as opened,
        COUNT(*) FILTER (WHERE status = 'hard_bounce') as hard_bounce,
        COUNT(*) FILTER (WHERE status = 'soft_bounce') as soft_bounce,
        COUNT(*) FILTER (WHERE status = 'replied') as replied
      FROM campaign_emails
      WHERE user_id = $1
        AND created_at > NOW() - INTERVAL '30 days'
    `;

    const aggregateMetricsResult = await pool.query(aggregateMetricsQuery, [userId]);

    // Fetch time series data
    const timeSeriesQuery = `
      WITH hours AS (
        SELECT generate_series(
          date_trunc('hour', NOW() - INTERVAL '24 hours'),
          date_trunc('hour', NOW()),
          '1 hour'::interval
        ) as hour
      )
      SELECT 
        to_char(hours.hour, 'HH24:00') as time,
        COUNT(*) FILTER (WHERE status = 'sent') as sent,
        COUNT(*) FILTER (WHERE status = 'opened') as opened,
        COUNT(*) FILTER (WHERE status = 'replied') as replied
      FROM hours
      LEFT JOIN campaign_emails ON 
        date_trunc('hour', campaign_emails.created_at) = hours.hour
        AND campaign_emails.user_id = $1
      GROUP BY hours.hour
      ORDER BY hours.hour
    `;

    const timeSeriesResult = await pool.query(timeSeriesQuery, [userId]);

    const metrics = aggregateMetricsResult.rows[0];
    const timeSeriesData = timeSeriesResult.rows;

    return NextResponse.json({
      sent: parseInt(metrics.sent) || 0,
      opened: parseInt(metrics.opened) || 0,
      hardBounce: parseInt(metrics.hard_bounce) || 0,
      softBounce: parseInt(metrics.soft_bounce) || 0,
      replied: parseInt(metrics.replied) || 0,
      timeSeriesData
    });

  } catch (error) {
    console.error('Error fetching campaign metrics:', error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 