import { useState, useEffect } from 'react';
import { defaultAssumptions } from '@/config/assumptions';

export const useProjectionsData = () => {
  const [loading, setLoading] = useState(true);
  const [assumptions, setAssumptions] = useState({
    outreach: {
      leadsPerMonth: 600,
      responseRate: 0.25,
      timeToRespond: 5
    },
    initialOutreach: defaultAssumptions.initialOutreach,
    rapportBuilding: defaultAssumptions.rapportBuilding,
    underwriting: defaultAssumptions.underwriting,
    matchmaking: defaultAssumptions.matchmaking,
    closing: defaultAssumptions.closing,
    teamGrowth: defaultAssumptions.teamGrowth
  });

  useEffect(() => {
    fetchAssumptions();
  }, []);

  const fetchAssumptions = async () => {
    try {
      setLoading(true);
      let response = await fetch('/api/projections/assumptions');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // If data is empty, initialize defaults
      if (!data || Object.keys(data).length === 0) {
        const initResponse = await fetch('/api/projections/assumptions/initialize', {
          method: 'POST'
        });
        if (!initResponse.ok) {
          throw new Error(`Failed to initialize assumptions: ${initResponse.status}`);
        }
        response = await fetch('/api/projections/assumptions');
        if (!response.ok) {
          throw new Error(`Failed to fetch initialized assumptions: ${response.status}`);
        }
        const initializedData = await response.json();
        // Merge with defaults in case any keys are missing
        const merged = {
          outreach: { ...defaultAssumptions.outreach, ...initializedData.outreach },
          initialOutreach: { ...defaultAssumptions.initialOutreach, ...initializedData.initialOutreach },
          rapportBuilding: { ...defaultAssumptions.rapportBuilding, ...initializedData.rapportBuilding },
          underwriting: { ...defaultAssumptions.underwriting, ...initializedData.underwriting },
          matchmaking: { ...defaultAssumptions.matchmaking, ...initializedData.matchmaking },
          closing: { ...defaultAssumptions.closing, ...initializedData.closing },
          teamGrowth: { ...defaultAssumptions.teamGrowth, ...initializedData.teamGrowth }
        };
        setAssumptions(merged);
      } else {
        // Merge the fetched structure with defaults
        const merged = {
          outreach: { ...defaultAssumptions.outreach, ...data.outreach },
          initialOutreach: { ...defaultAssumptions.initialOutreach, ...data.initialOutreach },
          rapportBuilding: { ...defaultAssumptions.rapportBuilding, ...data.rapportBuilding },
          underwriting: { ...defaultAssumptions.underwriting, ...data.underwriting },
          matchmaking: { ...defaultAssumptions.matchmaking, ...data.matchmaking },
          closing: { ...defaultAssumptions.closing, ...data.closing },
          teamGrowth: { ...defaultAssumptions.teamGrowth, ...data.teamGrowth }
        };
        setAssumptions(merged);
      }
    } catch (error) {
      console.error('Error fetching assumptions:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateFunnelMetrics = (baseAssumptions, scenarioMultiplier = 1) => {
    if (loading) return [];
    const metrics = [];
    let leadsRemaining = baseAssumptions.outreach.leadsPerMonth * scenarioMultiplier;

    // First stage: Outreach to First Response
    const firstResponses = leadsRemaining * baseAssumptions.outreach.responseRate;
    metrics.push({
      stage: "Outreach to First Response",
      starting: leadsRemaining,
      converted: firstResponses,
      timeInDays: baseAssumptions.outreach.timeToRespond
    });
    leadsRemaining = firstResponses;

    // Second stage: First Response to Discussion
    const discussionLeads = leadsRemaining * baseAssumptions.initialOutreach.responseRate;
    metrics.push({
      stage: "First Response to Discussion",
      starting: leadsRemaining,
      converted: discussionLeads,
      timeInDays: baseAssumptions.initialOutreach.timeToRespond
    });
    leadsRemaining = discussionLeads;

    // Third stage: Discussion to Zoom
    const zoomLeads = leadsRemaining * baseAssumptions.rapportBuilding.conversionRate;
    metrics.push({
      stage: "Discussion to Zoom",
      starting: leadsRemaining,
      converted: zoomLeads,
      timeInDays: baseAssumptions.rapportBuilding.timeToComplete
    });
    leadsRemaining = zoomLeads;

    // Fourth stage: Zoom to Underwriting
    const underwrittenLeads = leadsRemaining * baseAssumptions.underwriting.conversionRate;
    metrics.push({
      stage: "Zoom to Underwriting",
      starting: leadsRemaining,
      converted: underwrittenLeads,
      timeInDays: baseAssumptions.underwriting.timeToComplete
    });
    leadsRemaining = underwrittenLeads;

    // Fifth stage: Underwriting to Matches
    const matchedLeads = leadsRemaining * baseAssumptions.matchmaking.conversionRate;
    metrics.push({
      stage: "Underwriting to Matches",
      starting: leadsRemaining,
      converted: matchedLeads,
      timeInDays: baseAssumptions.matchmaking.timeToMatch
    });
    leadsRemaining = matchedLeads;

    // Final stage: Match to Closing
    const closedDeals = leadsRemaining * baseAssumptions.closing.conversionRate;
    metrics.push({
      stage: "Match to Closing",
      starting: leadsRemaining,
      converted: closedDeals,
      timeInDays: baseAssumptions.closing.timeToClose,
      revenue: closedDeals * baseAssumptions.closing.avgDealSize * baseAssumptions.closing.feePercentage
    });
    
    return metrics;
  };

  const updateAssumption = async (category, field, value) => {
    try {
      const response = await fetch('/api/projections/assumptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ category, field, value })
      });
      if (!response.ok) throw new Error('Failed to update assumption');
      setAssumptions(prev => ({
        ...prev,
        [category]: {
          ...prev[category],
          [field]: Number(value)
        }
      }));
    } catch (error) {
      console.error('Error updating assumption:', error);
    }
  };

  const resetToDefaults = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/projections/assumptions/initialize', {
        method: 'POST',
      });
      if (!response.ok) throw new Error('Failed to reset assumptions');
      
      // Refetch the assumptions after reset
      await fetchAssumptions();
    } catch (error) {
      console.error('Error resetting assumptions:', error);
    } finally {
      setLoading(false);
    }
  };

  const metrics = calculateFunnelMetrics(assumptions);

  return {
    assumptions,
    metrics,
    updateAssumption,
    calculateFunnelMetrics,
    loading,
    resetToDefaults
  };
}; 