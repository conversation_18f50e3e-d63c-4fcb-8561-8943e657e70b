import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch campaign sequence for a specific campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;

    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/sequences?api_key=${SMARTLEAD_API_KEY}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching sequence for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch campaign sequence: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error fetching campaign sequence:`, error);
    return NextResponse.json(
      { error: `Error fetching campaign sequence: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 