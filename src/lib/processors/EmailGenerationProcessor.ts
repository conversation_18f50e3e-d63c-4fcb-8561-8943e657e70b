import { BaseProcessor, EntityData, ProcessorOptions } from './BaseProcessor'
import { ContactProcessingState } from '../../types/processing'
import { EMAIL_GENERATION_SYSTEM_PROMPT, EMAIL_GENERATION_USER_TEMPLATE } from '../prompts/email-generation'
import OpenAI from 'openai'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'

interface ContactData extends EntityData {
  contact_id: number
  first_name: string
  last_name: string
  title?: string
  email: string
  linkedin_url?: string
  company_id?: number
  company_name?: string
  company_website?: string
  industry?: string
  contact_country?: string
  processing_state: ContactProcessingState
  email_generated?: boolean
}

interface GeneratedEmail {
  subject: string
  body: string
  tone: 'professional' | 'casual' | 'formal'
  personalization_score: number
  key_elements: string[]
}

export class EmailGenerationProcessor extends BaseProcessor {
  private openai: OpenAI
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    super('EmailGeneration', options)
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Create LLM provider with OpenAI as primary and Perplexity as fallback
    // Email generation typically works better with OpenAI/GPT models
    this.llmProvider = LLMFactory.createWithFallback(
      'openai',
      'perplexity',
      loggerAdapter,
      process.env.OPENAI_API_KEY,
      process.env.PERPLEXITY_API_KEY,
      { 
        temperature: 0.7,
        maxTokens: 1000,
        model: 'gpt-4.1' // Using latest GPT model for email generation
      }
    );
  }

  async getUnprocessedEntities(): Promise<ContactData[]> {
    // Get contacts that have been classified but haven't had emails generated
    const contacts = await this.getContactsForEmailGeneration(
      this.options.limit,
      this.options.singleId
    )

    return contacts.map((contact: Record<string, unknown>) => ({
      id: contact.contact_id as number,
      contact_id: contact.contact_id as number,
      first_name: contact.first_name as string,
      last_name: contact.last_name as string,
      title: contact.title as string,
      email: contact.email as string,
      linkedin_url: contact.linkedin_url as string,
      company_id: contact.company_id as number,
      company_name: contact.company_name as string,
      company_website: contact.company_website as string,
      industry: contact.industry as string,
      contact_country: contact.contact_country as string,
      processing_state: contact.processing_state as ContactProcessingState
    }))
  }

  getProcessingStateCondition(): string {
    return `processing_state = 'classification_completed'`
  }

  async processEntity(entity: ContactData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Generating email for contact: ${entity.first_name} ${entity.last_name} (${entity.email})`)

      // Set status to running
      await this.updateContactEmailGenerationStatus(entity.contact_id, 'running')

      // Get all context data for the contact
      const contactContext = await this.getContactContext(entity.contact_id)
      
      if (!contactContext) {
        return { success: false, error: 'Insufficient contact data for email generation' }
      }

      // Generate personalized email
      const generatedEmail = await this.generatePersonalizedEmail(entity, contactContext)
      
      if (!generatedEmail) {
        return { success: false, error: 'Failed to generate email' }
      }

      // Store generated email
      await this.storeGeneratedEmail(entity.contact_id, generatedEmail)

      this.log('info', `Successfully generated email for contact ${entity.contact_id}`)

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error generating email for contact ${entity.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // Email generation completed successfully
      await this.updateContactEmailGenerationStatus(entityId, 'completed')
    } else {
      // Email generation failed
      await this.updateContactEmailGenerationStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Get all context data for email generation
   */
  private async getContactContext(contactId: number): Promise<any> {
    try {
      // Get contact classification
      const classificationSql = `
        SELECT classification, classification_reasoning, classification_score
        FROM contact_classifications
        WHERE contact_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `
      const classificationResult = await this.query(classificationSql, [contactId])
      const classification = classificationResult[0]

      // Get OSINT research data
      const osintSql = `
        SELECT profile, background_summary, professional_focus
        FROM contact_searched_data
        WHERE contact_id = $1
      `
      const osintResult = await this.query(osintSql, [contactId])
      const osintData = osintResult[0]

      // Get conversation hooks
      const hooksSql = `
        SELECT hook_text
        FROM contact_hooks
        WHERE contact_id = $1
        ORDER BY created_at DESC
        LIMIT 5
      `
      const hooksResult = await this.query(hooksSql, [contactId])
      const hooks = hooksResult.map(row => row.hook_text)

      // Get achievements
      const achievementsSql = `
        SELECT achievement
        FROM contact_achievements
        WHERE contact_id = $1
        ORDER BY created_at DESC
        LIMIT 3
      `
      const achievementsResult = await this.query(achievementsSql, [contactId])
      const achievements = achievementsResult.map(row => row.achievement)

      // Get company overview if available
      const companyOverviewSql = `
        SELECT ced.companytype, ced.businessmodel, ced.investmentfocus, ced.targetmarkets
        FROM company_extracted_data ced
        JOIN contacts c ON c.company_id = ced.company_id
        WHERE c.contact_id = $1
      `
      const companyResult = await this.query(companyOverviewSql, [contactId])
      const companyOverview = companyResult[0]

      return {
        classification,
        osintData,
        hooks,
        achievements,
        companyOverview
      }
    } catch (error) {
      this.log('error', `Error fetching contact context for ${contactId}: ${error}`)
      return null
    }
  }

  /**
   * Generate personalized email using AI
   */
  private async generatePersonalizedEmail(
    contact: ContactData,
    context: any
  ): Promise<GeneratedEmail | null> {
    try {
      const messages = this.buildEmailGenerationMessages(contact, context)

      // Use the LLM provider with fallback support
      const response = await this.rateLimitedCall(async () => {
        return await this.llmProvider.callLLM(messages);
      }, 3000) // 3 second delay between API calls

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Generated email using ${response.provider} (${response.model})`);
      
      return this.parseEmailResponse(response.content)
    } catch (error) {
      this.log('error', `Error calling LLM email generation API: ${error}`)
      return null
    }
  }

  /**
   * Build messages for email generation
   */
  private buildEmailGenerationMessages(
    contact: ContactData,
    context: any
  ): LLMMessage[] {
    // Determine sender context (this would typically come from user/company settings)
    const senderInfo = {
      name: process.env.SENDER_NAME || 'Your Name',
      company: process.env.SENDER_COMPANY || 'Your Company',
      title: process.env.SENDER_TITLE || 'Business Development'
    }

    // Build contact summary
    let contactSummary = `**Contact:** ${contact.first_name} ${contact.last_name}`
    if (contact.title) contactSummary += `\n**Title:** ${contact.title}`
    if (contact.company_name) contactSummary += `\n**Company:** ${contact.company_name}`
    if (contact.industry) contactSummary += `\n**Industry:** ${contact.industry}`

    // Build context summary
    let contextSummary = ''
    
    if (context.classification) {
      contextSummary += `**Classification:** ${context.classification.classification}\n`
      if (context.classification.classification_reasoning) {
        contextSummary += `**Why:** ${context.classification.classification_reasoning}\n`
      }
    }

    if (context.osintData?.professional_focus) {
      contextSummary += `**Professional Focus:** ${context.osintData.professional_focus}\n`
    }

    if (context.hooks && context.hooks.length > 0) {
      contextSummary += `**Conversation Hooks:** ${context.hooks.slice(0, 3).join(', ')}\n`
    }

    if (context.achievements && context.achievements.length > 0) {
      contextSummary += `**Key Achievements:** ${context.achievements.slice(0, 2).join(', ')}\n`
    }

    if (context.companyOverview) {
      if (context.companyOverview.companytype) {
        contextSummary += `**Company Type:** ${context.companyOverview.companytype}\n`
      }
      if (context.companyOverview.investmentfocus) {
        try {
          const focus = JSON.parse(context.companyOverview.investmentfocus)
          if (Array.isArray(focus) && focus.length > 0) {
            contextSummary += `**Investment Focus:** ${focus.slice(0, 2).join(', ')}\n`
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }

    const userPrompt = EMAIL_GENERATION_USER_TEMPLATE(contactSummary, senderInfo, contextSummary)

    return [
      { role: 'system', content: EMAIL_GENERATION_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  /**
   * Parse email generation response
   */
  private parseEmailResponse(content: string): GeneratedEmail | null {
    try {
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const result = JSON.parse(jsonMatch[0])
      
      return {
        subject: result.subject || '',
        body: result.body || '',
        tone: result.tone || 'professional',
        personalization_score: result.personalization_score || 5,
        key_elements: Array.isArray(result.key_elements) ? result.key_elements : []
      }
    } catch (error) {
      this.log('error', `Error parsing email response: ${error}`)
      return null
    }
  }

  /**
   * Store generated email IN MESSAGES TABLE
   */
  private async storeGeneratedEmail(contactId: number, email: GeneratedEmail): Promise<void> {
    const sql = `
      INSERT INTO messages (
        contact_id, subject, body, tone, personalization_score, 
        key_elements, status, generated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, 'draft', NOW())
      ON CONFLICT (contact_id)
      DO UPDATE SET 
        subject = EXCLUDED.subject,
        body = EXCLUDED.body,
        tone = EXCLUDED.tone,
        personalization_score = EXCLUDED.personalization_score,
        key_elements = EXCLUDED.key_elements,
        status = 'draft',
        generated_at = EXCLUDED.generated_at
    `
    
    await this.query(sql, [
      contactId,
      email.subject,
      email.body,
      email.tone,
      email.personalization_score,
      JSON.stringify(email.key_elements)
    ])
    
    this.log('debug', `Stored generated email for contact ${contactId}`)
  }

} 