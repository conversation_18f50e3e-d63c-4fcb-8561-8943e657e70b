import OpenAI from 'openai'
import { NextResponse } from 'next/server'
import puppeteer from 'puppeteer'

export const runtime = 'nodejs'

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: Request) {
  let browser = null
  
  try {
    const { url } = await request.json()
    console.log('Received URL:', url)

    if (!url) {
      return NextResponse.json({ error: 'Missing URL' }, { status: 400 })
    }

    console.log('Launching browser...')
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    console.log('Creating new page...')
    const page = await browser.newPage()

    console.log('Navigating to URL...')
    await page.goto(url, {
      waitUntil: 'networkidle0',
      timeout: 30000,
    })

    console.log('Extracting content...')
    const content = await page.evaluate(() => {
      // Remove unwanted elements
      const selectorsToRemove = [
        'script',
        'style',
        'noscript',
        'iframe',
        'nav',
        'footer',
        'header'
      ]
      selectorsToRemove.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => el.remove())
      })

      // Get main content
      const mainContent = document.querySelector('main, article, #main, #content, .main, .content')
      const body = document.body

      // Prefer main content if available, otherwise use body
      const content = (mainContent || body).textContent || ''
      
      // Clean up the text
      return content
        .replace(/\s+/g, ' ')
        .trim()
        .slice(0, 8000)
    })

    console.log('Content length:', content.length)

    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key is not configured')
    }

    console.log('Sending to GPT...')
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a business analyst who extracts and summarizes company information. Respond with ONLY the JSON object, no markdown formatting or additional text.'
        },
        {
          role: 'user',
          content: `Extract the following information from this website content and return it as a JSON object:
            {
              "company_name": "Company's full name",
              "industry": "Primary industry or sector",
              "summary": "2-3 sentence overview of what the company does",
              "main_activities": ["List of main business activities"],
              "focus_areas": ["Key areas of focus or specialization"],
              "company_website": "${url}"
            }

            Website content:
            ${content}`
        }
      ],
      temperature: 0.3,
    })

    const result: string | null = completion.choices[0].message.content
    
    if (!result) {
      throw new Error('No response from GPT')
    }

    // Clean up the response and parse JSON
    let cleanResult = result
      .replace(/```json\n?/, '') // Remove ```json
      .replace(/```\n?/, '')     // Remove closing ```
      .trim()                    // Remove whitespace
    
    try {
      const companyData = JSON.parse(cleanResult)
      console.log('Successfully processed website')
      return NextResponse.json(companyData)
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError)
      console.log('Raw result:', result)
      console.log('Cleaned result:', cleanResult)
      throw new Error('Failed to parse GPT response')
    }

  } catch (error: any) {
    console.error('Detailed error:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    
    return NextResponse.json(
      { 
        error: 'Failed to process website', 
        details: error.message,
        stack: error.stack 
      },
      { status: 500 }
    )
  } finally {
    if (browser) {
      console.log('Closing browser...')
      await browser.close()
    }
  }
} 