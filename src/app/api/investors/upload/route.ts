import { pool } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import <PERSON> from 'papaparse'

interface CSVRow {
  'Company': string
  'Company Website': string
  'First Name': string
  'Last Name': string
  'Job Title': string
  'Email': string
  'LinkedIn URL': string
  'Company LinkedIn': string
  'Company Address': string
  'City': string
  'State': string
  'Zip': string
  'Country': string
  'Company Phone': string
  'Industry': string
  'Capital Type': string
  'Phone Number': string
  'Linked-In Profile': string
  '(Investment Criteria) Country': string
  '(Investment Criteria) Geographic Region': string
  '(Investment Criteria) State': string
  '(Investment Criteria) City': string
  '(Investment Criteria) Deal Size': string
  '(Investment Criteria) Property Type': string
  '(Investment Criteria) Property Type (Subcategory)': string
  '(Investment Criteria) Asset Type': string
  '(Investment Criteria) Loan Type': string
  '(Investment Criteria) Loan Type (Subcategory Short Term)': string
  '(Investment Criteria) Loan Type (Subcategory Long Term)': string
  '(Investment Criteria) Loan Term (Years)': string
  '(Investment Criteria) Loan Interest Rate Based Off SOFR/WSJ/Prime': string
  '(Investment Criteria) Loan Interest Rate': string
  '(Investment Criteria) Loan To Value': string
  '(Investment Criteria) Loan To Cost': string
  '(Investment Criteria) Loan Origination Fee (%)': string
  '(Investment Criteria) Loan Exit Fee (%)': string
  '(Investment Criteria) Recourse Loan': string
  '(Investment Criteria) Loan DSCR': string
  '(Investment Criteria) Closing Time': string
  '(Investment Criteria) Tear Sheet': string
  'Notes': string
  'Note': string
  [key: string]: string // For additional columns
}

interface ImportStats {
  companies: {
    total: number
    added: number
    skipped: number
    error: number
    missing_name: number
  }
  contacts: {
    total: number
    added: number
    skipped: number
    error: number
    no_email_linkedin: number
  }
}

function formatWebsite(website: string): string {
  if (!website) return ''
  if (!website.startsWith('http://') && !website.startsWith('https://')) {
    return `https://${website}`
  }
  return website
}

function buildContactExtraAttrs(row: CSVRow): string | null {
  // Define which columns map to dedicated database fields
  const mappedColumns = new Set([
    // Company fields (stored in companies table)
    'Company', 'Company Website', 'Company LinkedIn', 'Company Address',
    'City', 'State', 'Zip', 'Country', 'Company Phone', 'Industry',
    
    // Contact fields (stored in dedicated contact columns)
    'Email', 'Job Title', 'Last Name', 'First Name', 'LinkedIn URL', 'Linked-In Profile',
    'Phone Number', 'Capital Type', 'Notes', 'Note',
    
    // Investment criteria with dedicated columns
    '(Investment Criteria) Country', '(Investment Criteria) State', 
    '(Investment Criteria) City', '(Investment Criteria) Deal Size',
    '(Investment Criteria) Property Type', '(Investment Criteria) Asset Type',
    '(Investment Criteria) Loan Type'
  ])

  const extraAttrs: Record<string, string> = {}
  
  Object.entries(row).forEach(([key, value]) => {
    if (!mappedColumns.has(key) && value && value.trim()) {
      extraAttrs[key] = value.trim()
    }
  })

  return Object.keys(extraAttrs).length > 0 ? JSON.stringify(extraAttrs) : null
}

function buildCompanyExtraAttrs(row: CSVRow): string | null {
  // For companies, only store fields that don't have dedicated company columns
  const usedColumns = new Set([
    'Company', 'Company Website', 'Company LinkedIn', 'Company Address',
    'City', 'State', 'Zip', 'Country', 'Company Phone', 'Industry'
  ])

  const extraAttrs: Record<string, string> = {}
  
  Object.entries(row).forEach(([key, value]) => {
    if (!usedColumns.has(key) && value && value.trim()) {
      // Store all non-company fields in company extra_attrs for reference
      extraAttrs[key] = value.trim()
    }
  })

  return Object.keys(extraAttrs).length > 0 ? JSON.stringify(extraAttrs) : null
}

export async function POST(request: NextRequest) {
  try {
    // Initialize logs array first
    const logs: string[] = []
    
    // Check if this is a JSON request (from preview) or FormData (direct file upload)
    const contentType = request.headers.get('content-type')
    let csvData: CSVRow[]
    let source: string

    if (contentType?.includes('application/json')) {
      // Handle JSON request from preview component
      const { data, source: requestSource, skippedCount } = await request.json()
      
      if (!data || !Array.isArray(data)) {
        return NextResponse.json(
          { error: 'Invalid data array provided' },
          { status: 400 }
        )
      }
      
      csvData = data
      source = requestSource || `Equity Investors Import ${new Date().toISOString().split('T')[0]}`
      
      // Add skip information to logs if provided
      if (skippedCount && skippedCount > 0) {
        logs.push(`Frontend pre-filtering: Skipped ${skippedCount} existing entries before upload`)
      }
    } else {
      // Handle FormData request (original file upload)
      const formData = await request.formData()
      const file = formData.get('file') as File
      
      if (!file) {
        return NextResponse.json(
          { error: 'No file provided' },
          { status: 400 }
        )
      }

      // Read file content
      const fileContent = await file.text()
      
      // Parse CSV
      const parseResult = Papa.parse<CSVRow>(fileContent, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim()
      })

      if (parseResult.errors.length > 0) {
        console.error('CSV parsing errors:', parseResult.errors)
        return NextResponse.json(
          { error: 'Failed to parse CSV file', details: parseResult.errors },
          { status: 400 }
        )
      }
      
      csvData = parseResult.data
      source = `Equity Investors Import ${new Date().toISOString().split('T')[0]}`
    }

    const stats: ImportStats = {
      companies: {
        total: 0,
        added: 0,
        skipped: 0,
        error: 0,
        missing_name: 0
      },
      contacts: {
        total: 0,
        added: 0,
        skipped: 0,
        error: 0,
        no_email_linkedin: 0
      }
    }

    // Process each row
    for (const row of csvData) {
      const client = await pool.connect()
      
      try {
        await client.query('BEGIN')

        // Extract company data
        const companyName = row['Company']?.trim() || ''
        const website = formatWebsite(row['Company Website']?.trim() || '')
        const companyLinkedin = row['Company LinkedIn']?.trim() || ''
        const companyAddress = row['Company Address']?.trim() || ''
        const companyCity = row['City']?.trim() || ''
        const companyState = row['State']?.trim() || ''
        const companyZip = row['Zip']?.trim() || ''
        const companyCountry = row['Country']?.trim() || ''
        const companyPhone = row['Company Phone']?.trim() || ''
        const industry = row['Industry']?.trim() || ''
        const companyExtraJson = buildCompanyExtraAttrs(row)

        stats.companies.total++

        // Skip if company name is missing
        if (!companyName) {
          logs.push('Warning: Skipping row with missing company name')
          stats.companies.missing_name++
          await client.query('COMMIT')
          continue
        }

        // Check if company exists
        const companyResult = await client.query(
          'SELECT company_id FROM companies WHERE company_name = $1',
          [companyName]
        )

        let companyId: number

        if (companyResult.rows.length > 0) {
          companyId = companyResult.rows[0].company_id
          logs.push(`Company '${companyName}' already exists with ID ${companyId}`)
          stats.companies.skipped++
        } else {
          // Insert new company
          const insertCompanyResult = await client.query(
            `INSERT INTO companies (
              company_name, company_website, company_linkedin, company_address,
              company_city, company_state, company_zip, company_country,
              company_phone, industry, source, extra_attrs,
              created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12::jsonb, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING company_id`,
            [
              companyName, website, companyLinkedin, companyAddress,
              companyCity, companyState, companyZip, companyCountry,
              companyPhone, industry, source, companyExtraJson
            ]
          )
          
          companyId = insertCompanyResult.rows[0].company_id
          logs.push(`Added company '${companyName}' with ID ${companyId}`)
          stats.companies.added++
        }

        // Extract contact data
        const firstName = row['First Name']?.trim() || ''
        const lastName = row['Last Name']?.trim() || ''
        const title = row['Job Title']?.trim() || ''
        const email = row['Email']?.trim() || ''
        const linkedinUrl = (row['LinkedIn URL'] || row['Linked-In Profile'])?.trim() || ''
        const phoneNumber = row['Phone Number']?.trim() || ''
        const capitalType = row['Capital Type']?.trim() || ''
        const notes = (row['Notes'] || row['Note'])?.trim() || ''
        
        // Investment criteria fields
        const investmentCountry = row['(Investment Criteria) Country']?.trim() || ''
        const investmentState = row['(Investment Criteria) State']?.trim() || ''
        const investmentCity = row['(Investment Criteria) City']?.trim() || ''
        const dealSize = row['(Investment Criteria) Deal Size']?.trim() || ''
        const propertyType = row['(Investment Criteria) Property Type']?.trim() || ''
        const assetType = row['(Investment Criteria) Asset Type']?.trim() || ''
        const loanType = row['(Investment Criteria) Loan Type']?.trim() || ''
        
        const contactExtraJson = buildContactExtraAttrs(row)

        // Process contact if name is provided
        const fullName = `${firstName} ${lastName}`.trim()
        
        if (fullName) {
          stats.contacts.total++

          const contactEmail = email && email.length >= 3 ? email : null
          const contactLinkedin = linkedinUrl && linkedinUrl.length >= 3 ? linkedinUrl : null
          const contactPhone = phoneNumber && phoneNumber.length >= 3 ? phoneNumber : null

          // Check if contact exists
          const contactResult = await client.query(
            'SELECT contact_id FROM contacts WHERE full_name = $1 AND company_id = $2',
            [fullName, companyId]
          )

          if (contactResult.rows.length > 0) {
            const contactId = contactResult.rows[0].contact_id
            logs.push(`Contact '${fullName}' already exists with ID ${contactId}`)
            stats.contacts.skipped++
          } else {
            try {
              // Insert new contact with all mapped fields
              const insertContactResult = await client.query(
                `INSERT INTO contacts (
                  company_id, first_name, last_name, full_name, title, email,
                  linkedin_url, phone_number, capital_type, notes,
                  investment_criteria_country, investment_criteria_state, investment_criteria_city,
                  investment_criteria_deal_size, investment_criteria_property_type, 
                  investment_criteria_asset_type, investment_criteria_loan_type,
                  extra_attrs, source, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18::jsonb, $19, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                RETURNING contact_id`,
                [
                  companyId, firstName, lastName, fullName, title, contactEmail,
                  contactLinkedin, contactPhone, capitalType, notes,
                  investmentCountry, investmentState, investmentCity,
                  dealSize, propertyType, assetType, loanType,
                  contactExtraJson, source
                ]
              )
              
              const contactId = insertContactResult.rows[0].contact_id
              logs.push(`Added contact '${fullName}' with ID ${contactId}`)
              stats.contacts.added++
            } catch (contactError) {
              console.error(`Error adding contact '${fullName}':`, contactError)
              logs.push(`Error adding contact '${fullName}': ${contactError}`)
              stats.contacts.error++
              // Continue processing other contacts
            }
          }
        }

        await client.query('COMMIT')

      } catch (error) {
        await client.query('ROLLBACK')
        console.error(`Transaction error for company '${row['Company']}':`, error)
        logs.push(`Transaction error for company '${row['Company']}': ${error}`)
        stats.companies.error++
      } finally {
        client.release()
      }
    }

    // Calculate totals for validation
    const companiesAccounted = stats.companies.added + stats.companies.skipped + 
                              stats.companies.error + stats.companies.missing_name
    const contactsAccounted = stats.contacts.added + stats.contacts.skipped + stats.contacts.error

    const summary = {
      success: true,
      stats,
      validation: {
        companies: {
          total: stats.companies.total,
          accounted: companiesAccounted,
          matches: companiesAccounted === stats.companies.total
        },
        contacts: {
          total: stats.contacts.total,
          accounted: contactsAccounted,  
          matches: contactsAccounted === stats.contacts.total
        }
      },
      logs: logs.slice(-50) // Return last 50 log entries
    }

    return NextResponse.json(summary)

  } catch (error) {
    console.error('Upload processing error:', error)
    return NextResponse.json(
      { error: 'Failed to process CSV upload', details: error },
      { status: 500 }
    )
  }
} 