"use client"

import React, { useEffect, useState } from 'react'
import { <PERSON>, Card<PERSON>eader, CardContent } from '@/components/ui/card'
import { Database, Plus, Edit, Trash2, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface CriteriaData {
  field: string;
  values: string[];
  count: number;
}

interface CleanValue {
  id: number;
  field: string;
  value: string;
  created_at: string;
}

interface PopulationResult {
  success: boolean;
  totalProcessed: number;
  successfulInserts: number;
  errors: Array<{
    field: string;
    value: string;
    error: string;
  }>;
}

export const DataCleaning: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const { toast } = useToast()
  const [criteriaData, setCriteriaData] = useState<CriteriaData[]>([])
  const [cleanValues, setCleanValues] = useState<Record<string, CleanValue[]>>({})
  const [loading, setLoading] = useState(true)
  const [newValues, setNewValues] = useState<Record<string, string>>({})
  const [isPopulating, setIsPopulating] = useState(false)
  const [populationErrors, setPopulationErrors] = useState<PopulationResult['errors']>([])

  const investmentCriteriaFields = [
    { field: 'investment_criteria_deal_size', label: 'Deal Sizes' },
    { field: 'investment_criteria_property_type', label: 'Property Types' },
    { field: 'investment_criteria_property_type_subcategory', label: 'Property Subtypes' },
    { field: 'investment_criteria_asset_type', label: 'Asset Types' },
    { field: 'investment_criteria_loan_type', label: 'Loan Types' },
    { field: 'investment_criteria_loan_type_short_term', label: 'Short Term Loan Types' },
    { field: 'investment_criteria_loan_type_long_term', label: 'Long Term Loan Types' },
    { field: 'investment_criteria_loan_term_years', label: 'Loan Terms' },
    { field: 'investment_criteria_loan_interest_rate_basis', label: 'Interest Rate Bases' },
    { field: 'investment_criteria_loan_interest_rate', label: 'Interest Rates' },
    { field: 'investment_criteria_loan_to_value', label: 'Loan to Value' },
    { field: 'investment_criteria_loan_to_cost', label: 'Loan to Cost' },
    { field: 'investment_criteria_loan_origination_fee_pct', label: 'Origination Fees' },
    { field: 'investment_criteria_loan_exit_fee_pct', label: 'Exit Fees' },
    { field: 'investment_criteria_recourse_loan', label: 'Recourse Types' },
    { field: 'investment_criteria_loan_dscr', label: 'DSCR' },
    { field: 'investment_criteria_closing_time', label: 'Closing Times' },
    { field: 'capital_type', label: 'Capital Types' }
  ]

  const geographicFields = [
    { field: 'investment_criteria_country', label: 'Countries' },
    { field: 'investment_criteria_geographic_region', label: 'Geographic Regions' },
    { field: 'investment_criteria_state', label: 'States' },
    { field: 'investment_criteria_city', label: 'Cities' },
  ]

  useEffect(() => {
    if (isActive) {
      fetchCriteriaData()
      fetchCleanValues()
    }
  }, [isActive])

  const fetchCriteriaData = async () => {
    try {
      const response = await fetch('/api/data-cleaning')
      const data = await response.json()
      setCriteriaData(data)
    } catch (error) {
      console.error('Failed to fetch criteria data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCleanValues = async () => {
    try {
      const response = await fetch('/api/data-cleaning/clean-values')
      const data = await response.json()
      setCleanValues(data)
    } catch (error) {
      console.error('Failed to fetch clean values:', error)
    }
  }

  const handleAddCleanValue = async (field: string) => {
    try {
      const value = newValues[field]
      if (!value?.trim()) {
        toast({
          title: 'Error',
          description: 'Please enter a value',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/data-cleaning/clean-values', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          field,
          value: value.trim(),
        }),
      })

      if (!response.ok) throw new Error('Failed to add clean value')

      await fetchCleanValues()
      setNewValues(prev => ({ ...prev, [field]: '' }))
      toast({
        title: 'Success',
        description: 'Clean value added successfully',
      })
    } catch (error) {
      console.error('Failed to add clean value:', error)
      toast({
        title: 'Error',
        description: 'Failed to add clean value',
        variant: 'destructive',
      })
    }
  }

  const handleDeleteCleanValue = async (id: number) => {
    try {
      const response = await fetch(`/api/data-cleaning/clean-values/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) throw new Error('Failed to delete clean value')

      await fetchCleanValues()
      toast({
        title: 'Success',
        description: 'Clean value deleted successfully',
      })
    } catch (error) {
      console.error('Failed to delete clean value:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete clean value',
        variant: 'destructive',
      })
    }
  }

  const populateTables = async () => {
    setIsPopulating(true)
    setPopulationErrors([])
    
    try {
      const response = await fetch('/api/data-cleaning/populate-tables', {
        method: 'POST'
      })
      
      if (!response.ok) throw new Error('Failed to populate tables')
      
      const result: PopulationResult = await response.json()
      
      if (result.success) {
        toast({
          title: "Success",
          description: `Successfully populated ${result.successfulInserts} of ${result.totalProcessed} values`,
        })
      } else {
        toast({
          title: "Partial Success",
          description: `Populated ${result.successfulInserts} values with some errors`,
          variant: "destructive"
        })
      }

      if (result.errors.length > 0) {
        setPopulationErrors(result.errors)
      }
    } catch (error) {
      console.error('Error populating tables:', error)
      toast({
        title: "Error",
        description: "Failed to populate reference tables",
        variant: "destructive"
      })
    } finally {
      setIsPopulating(false)
    }
  }

  if (!isActive) return null
  if (loading) return <div>Loading...</div>

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Database className="h-6 w-6" />
            Data Cleaning Dashboard
          </h1>
          <Button 
            onClick={populateTables}
            className="bg-green-600 hover:bg-green-700"
            disabled={isPopulating}
          >
            <Database className="h-4 w-4 mr-2" />
            {isPopulating ? 'Populating...' : 'Populate Reference Tables'}
          </Button>
        </div>
        <p className="text-gray-600 mt-2">
          Review and analyze distinct values in investment criteria fields
        </p>
      </div>

      {/* Display Population Errors */}
      {populationErrors.length > 0 && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Population Errors</AlertTitle>
          <AlertDescription>
            <div className="mt-2 max-h-40 overflow-y-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left">Field</th>
                    <th className="text-left">Value</th>
                    <th className="text-left">Error</th>
                  </tr>
                </thead>
                <tbody>
                  {populationErrors.map((error, index) => (
                    <tr key={index} className="border-t">
                      <td className="py-2">{error.field}</td>
                      <td className="py-2">{error.value}</td>
                      <td className="py-2 text-red-500">{error.error}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Investment Criteria Section */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">Investment Criteria</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {criteriaData
            .filter(criteria => 
              investmentCriteriaFields.some(field => field.field === criteria.field)
            )
            .map((criteria) => (
              <Card key={criteria.field} className="shadow-sm">
                <CardHeader className="border-b">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">
                      {investmentCriteriaFields.find(f => f.field === criteria.field)?.label || criteria.field}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-500">
                    {criteria.count} raw values | {cleanValues[criteria.field]?.length || 0} clean values
                  </p>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {/* Add Clean Value Section */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-500">Add Clean Value</h4>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter clean value"
                          value={newValues[criteria.field] || ''}
                          onChange={(e) => setNewValues(prev => ({
                            ...prev,
                            [criteria.field]: e.target.value
                          }))}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleAddCleanValue(criteria.field)
                            }
                          }}
                        />
                        <Button 
                          size="sm"
                          onClick={() => handleAddCleanValue(criteria.field)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>
                    </div>

                    {/* Raw Values Section */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Raw Values</h4>
                      <div className="max-h-40 overflow-y-auto">
                        <table className="w-full">
                          <tbody>
                            {criteria.values.map((value, index) => (
                              <tr key={index} className="border-t">
                                <td className="py-2 text-sm">{value || '<empty>'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Clean Values Section */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Clean Values</h4>
                      <div className="max-h-40 overflow-y-auto">
                        <table className="w-full">
                          <tbody>
                            {cleanValues[criteria.field]?.map((cleanValue) => (
                              <tr key={cleanValue.id} className="border-t">
                                <td className="py-2 text-sm">{cleanValue.value}</td>
                                <td className="py-2 text-right">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleDeleteCleanValue(cleanValue.id)}
                                  >
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </div>

      {/* Geographic Parameters Section */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Geographic Parameters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {criteriaData
            .filter(criteria => 
              geographicFields.some(field => field.field === criteria.field)
            )
            .map((criteria) => (
              <Card key={criteria.field} className="shadow-sm">
                <CardHeader className="border-b">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">
                      {geographicFields.find(f => f.field === criteria.field)?.label || criteria.field}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-500">
                    {criteria.count} raw values | {cleanValues[criteria.field]?.length || 0} clean values
                  </p>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {/* Add Clean Value Section */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-500">Add Clean Value</h4>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter clean value"
                          value={newValues[criteria.field] || ''}
                          onChange={(e) => setNewValues(prev => ({
                            ...prev,
                            [criteria.field]: e.target.value
                          }))}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleAddCleanValue(criteria.field)
                            }
                          }}
                        />
                        <Button 
                          size="sm"
                          onClick={() => handleAddCleanValue(criteria.field)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>
                    </div>

                    {/* Raw Values Section */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Raw Values</h4>
                      <div className="max-h-40 overflow-y-auto">
                        <table className="w-full">
                          <tbody>
                            {criteria.values.map((value, index) => (
                              <tr key={index} className="border-t">
                                <td className="py-2 text-sm">{value || '<empty>'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Clean Values Section */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Clean Values</h4>
                      <div className="max-h-40 overflow-y-auto">
                        <table className="w-full">
                          <tbody>
                            {cleanValues[criteria.field]?.map((cleanValue) => (
                              <tr key={cleanValue.id} className="border-t">
                                <td className="py-2 text-sm">{cleanValue.value}</td>
                                <td className="py-2 text-right">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleDeleteCleanValue(cleanValue.id)}
                                  >
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      </div>
    </div>
  )
} 