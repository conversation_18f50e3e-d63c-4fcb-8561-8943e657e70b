export const CONTACT_CLASSIFICATION_SYSTEM_PROMPT = `You are a commercial-real-estate analyst.  
Task: Read the supplied contact & company data and decide whether the contact is primarily  
    • CAPITAL_SOURCE   → provides equity or debt (LP, GP, family office, fund, REIT, bank, lender, allocator)  
    • SPONSOR          → seeks capital for its own developments / acquisitions (developer, owner-operator, operator, JV sponsor)  

Decision rules  
1. Base the call on job title, company description, recent deals, and mission statements.  
2. If the contact clearly plays both roles, choose the one that represents their core business or >50% of revenue.  
3. If signals are weak or conflicting, return "Unknown".  
4. Output strictly in the JSON schema below—nothing else.  

Output schema  
{
  "lead_type": "Capital Source" | "Sponsor" | "Third-Party" | "Unknown",
  "confidence": 0.0-1.0,          // gut-score of certainty
  "rationale":  "≤25 words summarising the key evidence"
}`

export const CONTACT_CLASSIFICATION_USER_TEMPLATE = `I need to classify this real estate contact:

Name: {{first_name}} {{last_name}}
Email: {{email}}
Company: {{company}}
Job Title: {{job_title}}
LinkedIn: {{linkedin_url}}
Company Website: {{company_website}}
Industry: {{industry}}
Country: {{country}}

OSINT Profile:
{{osint_profile_text}}

Company Overview:
{{company_overview_text}}

Please classify this contact as either a CAPITAL_SOURCE, SPONSOR, or UNKNOWN, following the decision rules.`

// Template function for backward compatibility
export const CONTACT_CLASSIFICATION_USER_TEMPLATE_FUNCTION = (contact: {
  first_name: string
  last_name: string
  title?: string
  company_name?: string
  industry?: string
  contact_country?: string
  email: string
  linkedin_url?: string
  company_website?: string
}, companyContext?: string, osintData?: {
  profile?: string
  professional_focus?: string
}) => {
  return CONTACT_CLASSIFICATION_USER_TEMPLATE
    .replace(/\{\{first_name\}\}/g, contact.first_name)
    .replace(/\{\{last_name\}\}/g, contact.last_name)
    .replace(/\{\{email\}\}/g, contact.email)
    .replace(/\{\{company\}\}/g, contact.company_name || 'Not specified')
    .replace(/\{\{job_title\}\}/g, contact.title || 'Not specified')
    .replace(/\{\{linkedin_url\}\}/g, contact.linkedin_url || 'Not specified')
    .replace(/\{\{company_website\}\}/g, contact.company_website || 'Not specified')
    .replace(/\{\{industry\}\}/g, contact.industry || 'Not specified')
    .replace(/\{\{country\}\}/g, contact.contact_country || 'Not specified')
    .replace(/\{\{osint_profile_text\}\}/g, osintData?.profile || 'No OSINT profile available')
    .replace(/\{\{company_overview_text\}\}/g, companyContext || 'No company overview available')
} 