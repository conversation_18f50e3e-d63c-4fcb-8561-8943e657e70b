import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '100')
    const search = searchParams.get('search') || ''
    const offset = (page - 1) * limit

    const query = `
      SELECT 
        p.person_id,
        p.first_name,
        p.last_name,
        p.email,
        p.phone_number,
        p.job_title,
        p.capital_type,
        p.contact_source,
        p.person_linkedin,
        p.status,
        c.company_name,
        COUNT(*) OVER() as total_count
      FROM persons p
      LEFT JOIN companies c ON p.company_id = c.company_id
      WHERE 
        (
          LOWER(p.first_name) LIKE LOWER($1) OR
          LOWER(p.last_name) LIKE LOWER($1) OR
          LOWER(c.company_name) LIKE LOWER($1) OR
          LOWER(p.job_title) LIKE LOWER($1)
        )
      ORDER BY p.last_name ASC, p.first_name ASC
      LIMIT $2 OFFSET $3
    `
    const result = await pool.query(query, [`%${search}%`, limit, offset])
    
    const totalCount = result.rows[0]?.total_count || 0
    return NextResponse.json({
      persons: result.rows,
      total: totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch people' }, 
      { status: 500 }
    )
  }
} 