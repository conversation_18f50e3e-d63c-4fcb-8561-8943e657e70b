import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useProjectionsData } from '@/hooks/useProjectionsData';

const scenarios = {
  base: { name: 'Base Case', multiplier: 1 },
  conservative: { name: 'Conservative', multiplier: 0.7 },
  aggressive: { name: 'Aggressive', multiplier: 1.3 }
};

export const Scenarios = () => {
  const [selectedScenario, setSelectedScenario] = useState('base');
  const { assumptions, calculateFunnelMetrics, loading } = useProjectionsData();

  if (loading) {
    return <div>Loading scenarios...</div>;
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Scenario Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Select value={selectedScenario} onValueChange={setSelectedScenario}>
              <SelectTrigger>
                <SelectValue placeholder="Select scenario" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(scenarios).map(([key, scenario]) => (
                  <SelectItem key={key} value={key}>
                    {scenario.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={calculateFunnelMetrics(assumptions, scenarios[selectedScenario].multiplier)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="stage" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="starting" stroke="#2563eb" name="Starting" />
                  <Line type="monotone" dataKey="converted" stroke="#16a34a" name="Converted" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Object.entries(scenarios).map(([key, scenario]) => {
          const scenarioMetrics = calculateFunnelMetrics(assumptions, scenario.multiplier);
          if (!scenarioMetrics.length) {
            // Fallback if metrics haven't been calculated for any reason.
            return (
              <Card key={key}>
                <CardHeader>
                  <CardTitle>{scenario.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div>Calculating metrics...</div>
                </CardContent>
              </Card>
            );
          }
          const finalStage = scenarioMetrics[scenarioMetrics.length - 1];
          return (
            <Card key={key}>
              <CardHeader>
                <CardTitle>{scenario.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Deals Closed</span>
                    <span className="font-medium">{Math.round(finalStage.converted)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Revenue</span>
                    <span className="font-medium">${Math.round(finalStage.revenue).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Conversion Rate</span>
                    <span className="font-medium">
                      {Math.round((finalStage.converted / scenarioMetrics[0].starting) * 100)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}; 