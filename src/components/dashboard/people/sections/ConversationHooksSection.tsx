"use client"

import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Edit, Trash2, CheckCircle, RefreshCw } from 'lucide-react';
import { Contact } from '../types';

interface ConversationHooksSectionProps {
  contact: Contact;
  contactId: string | number;
}

const ConversationHooksSection: React.FC<ConversationHooksSectionProps> = ({ contact, contactId }) => {
  const [isEditingHooks, setIsEditingHooks] = useState(false);
  const [conversationHooks, setConversationHooks] = useState<string[]>([]);
  const [newHook, setNewHook] = useState('');
  const [savingHooks, setSavingHooks] = useState(false);
  
  // Initialize conversation hooks from contact data
  useEffect(() => {
    if (contact && contact.conversation_hooks) {
      const hooks = Array.isArray(contact.conversation_hooks) 
        ? contact.conversation_hooks.map(hook => typeof hook === 'string' ? hook : hook.hook || hook.description || JSON.stringify(hook)) 
        : [];
      setConversationHooks(hooks);
    }
  }, [contact]);
  
  // Add a new hook
  const addConversationHook = () => {
    if (newHook.trim() === '') return;
    setConversationHooks([...conversationHooks, newHook.trim()]);
    setNewHook('');
  };
  
  // Remove a hook
  const removeConversationHook = (index: number) => {
    setConversationHooks(conversationHooks.filter((_, i) => i !== index));
  };
  
  // Save conversation hooks
  const saveConversationHooks = async () => {
    if (!contactId) return;
    
    setSavingHooks(true);
    try {
      const response = await fetch(`/api/contacts/${contactId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation_hooks: conversationHooks
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update conversation hooks: ${response.status}`);
      }
      
      toast.success('Conversation hooks updated successfully');
      setIsEditingHooks(false);
      
      // Update contact object if available
      if (contact && typeof contact === 'object') {
        contact.conversation_hooks = conversationHooks;
      }
      
    } catch (error) {
      console.error('Error updating conversation hooks:', error);
      toast.error(`Failed to update conversation hooks: ${(error as Error).message}`);
    } finally {
      setSavingHooks(false);
    }
  };
  
  return (
    <Card className="bg-white shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-medium">Conversation Hooks</CardTitle>
          {!isEditingHooks ? (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setIsEditingHooks(true)}
              className="flex items-center"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Hooks
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button 
                size="sm" 
                variant="default"
                onClick={saveConversationHooks}
                disabled={savingHooks}
                className="flex items-center"
              >
                {savingHooks ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Save
              </Button>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => {
                  setIsEditingHooks(false);
                  // Reset to original hooks if cancel
                  if (contact && contact.conversation_hooks) {
                    const hooks = Array.isArray(contact.conversation_hooks) 
                      ? contact.conversation_hooks.map(hook => typeof hook === 'string' ? hook : hook.hook || hook.description || JSON.stringify(hook)) 
                      : [];
                    setConversationHooks(hooks);
                  }
                }}
                className="flex items-center"
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditingHooks ? (
          <div className="space-y-3">
            {conversationHooks.map((hook, index) => (
              <div key={index} className="flex items-start gap-2">
                <Textarea 
                  value={hook}
                  onChange={(e) => {
                    const updatedHooks = [...conversationHooks];
                    updatedHooks[index] = e.target.value;
                    setConversationHooks(updatedHooks);
                  }}
                  className="flex-1"
                />
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => removeConversationHook(index)}
                  className="text-red-500"
                >
                  <Trash2 className="h-5 w-5" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2 mt-3">
              <Input
                value={newHook}
                onChange={(e) => setNewHook(e.target.value)}
                placeholder="Add a new conversation hook..."
                className="flex-1"
              />
              <Button 
                onClick={addConversationHook} 
                disabled={!newHook.trim()}
              >
                Add Hook
              </Button>
            </div>
          </div>
        ) : (
          <ul className="space-y-2 pl-6 list-disc">
            {conversationHooks.length > 0 ? (
              conversationHooks.map((hook, index) => (
                <li key={index}>{hook}</li>
              ))
            ) : (
              <p className="text-gray-500 italic">No conversation hooks available</p>
            )}
          </ul>
        )}
      </CardContent>
    </Card>
  );
};

export default ConversationHooksSection; 