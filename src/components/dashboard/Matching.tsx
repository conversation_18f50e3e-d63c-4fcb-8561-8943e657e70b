"use client"

import React, { useEffect, useState } from 'react'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card'
import { GitMerge, Check, X, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from "@/hooks/use-toast"

interface MatchingData {
  id: number;
  field: string;
  raw_value: string;
  suggested_clean_value_id: number;
  suggested_clean_value: string;
  current_clean_value_id: number | null;
  current_clean_value: string | null;
  confidence_score: number;
  is_verified: boolean;
}

interface CleanValue {
  id: number;
  value: string;
}

export const Matching: React.FC<{ isActive: boolean }> = ({ isActive }) => {
  const { toast } = useToast()
  const [matchingData, setMatchingData] = useState<MatchingData[]>([])
  const [cleanValues, setCleanValues] = useState<Record<string, CleanValue[]>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    console.log('Matching component mounted, isActive:', isActive)
    if (isActive) {
      fetchMatchingData()
      fetchCleanValues()
    }
  }, [isActive])

  const fetchMatchingData = async () => {
    try {
      console.log('Fetching matching data...')
      const response = await fetch('/api/matching')
      const data = await response.json()
      console.log('Received matching data:', data)
      setMatchingData(data)
    } catch (error) {
      console.error('Failed to fetch matching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCleanValues = async () => {
    try {
      const response = await fetch('/api/data-cleaning/clean-values')
      const data = await response.json()
      setCleanValues(data)
    } catch (error) {
      console.error('Failed to fetch clean values:', error)
    }
  }

  const handleVerifyMatch = async (id: number, cleanValueId: number, verified: boolean) => {
    try {
      const response = await fetch('/api/matching/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          clean_value_id: cleanValueId,
          is_verified: verified,
        }),
      })

      if (!response.ok) throw new Error('Failed to verify match')

      await fetchMatchingData()
      toast({
        title: 'Success',
        description: 'Match verification updated',
      })
    } catch (error) {
      console.error('Failed to verify match:', error)
      toast({
        title: 'Error',
        description: 'Failed to update match verification',
        variant: 'destructive',
      })
    }
  }

  const handlePopulate = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/matching/populate', {
        method: 'POST',
      })
      if (!response.ok) throw new Error('Failed to populate matches')
      
      await fetchMatchingData() // Refresh the data
      toast({
        title: 'Success',
        description: 'Successfully populated matching data',
      })
    } catch (error) {
      console.error('Failed to populate matches:', error)
      toast({
        title: 'Error',
        description: 'Failed to populate matching data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  if (!isActive) {
    console.log('Component not active, returning null')
    return null
  }
  if (loading) {
    console.log('Component loading...')
    return <div>Loading...</div>
  }

  console.log('Rendering matching data:', matchingData)

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <GitMerge className="h-6 w-6" />
            Value Matching Dashboard
          </h1>
          <Button onClick={handlePopulate} disabled={loading}>
            Populate Matching Data
          </Button>
        </div>
        <p className="text-gray-600 mt-2">
          Review and verify matches between raw values and clean values
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {matchingData.map((match) => (
          <Card key={match.id} className="shadow-sm">
            <CardHeader className="border-b">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">{match.field}</h3>
                <div className="flex items-center gap-2">
                  {match.is_verified ? (
                    <span className="text-sm text-green-600 flex items-center gap-1">
                      <Check className="h-4 w-4" />
                      Verified
                    </span>
                  ) : (
                    <span className="text-sm text-yellow-600 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      Pending Verification
                    </span>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Raw Value</label>
                  <div className="mt-1 text-base">{match.raw_value}</div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Suggested Clean Value</label>
                  <div className="mt-1 text-base">{match.suggested_clean_value}</div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">Current Clean Value</label>
                  <Select
                    value={match.current_clean_value_id?.toString() || ''}
                    onValueChange={(value) => handleVerifyMatch(match.id, parseInt(value), true)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select clean value" />
                    </SelectTrigger>
                    <SelectContent>
                      {cleanValues[match.field]?.map(cleanValue => (
                        <SelectItem key={cleanValue.id} value={cleanValue.id.toString()}>
                          {cleanValue.value}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end gap-2">
                  <div className="text-sm text-gray-500">
                    Confidence: {(match.confidence_score * 100).toFixed(1)}%
                  </div>
                  {match.is_verified && (
                    <Button 
                      variant="outline"
                      onClick={() => handleVerifyMatch(match.id, match.current_clean_value_id!, false)}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Unverify
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}