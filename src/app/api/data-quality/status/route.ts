import { NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET() {
  try {
    const client = await pool.connect();
    
    try {
      // Contact status metrics
      const contactStatusQueries = [
        {
          name: 'email_status',
          query: `
            SELECT email_status as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contacts WHERE email_status IS NOT NULL), 2) as percentage
            FROM contacts 
            WHERE email_status IS NOT NULL
            GROUP BY email_status
            ORDER BY count DESC
          `
        },
        {
          name: 'smartlead_status',
          query: `
            SELECT COALESCE(smartlead_status, 'DRAFT') as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contacts WHERE email_generated = true), 2) as percentage
            FROM contacts 
            WHERE email_generated = true
            GROUP BY smartlead_status
            ORDER BY count DESC
          `
        },
        {
          name: 'classification_status',
          query: `
            SELECT classification_status as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contacts WHERE classification_status IS NOT NULL), 2) as percentage
            FROM contacts 
            WHERE classification_status IS NOT NULL
            GROUP BY classification_status
            ORDER BY count DESC
          `
        },
        {
          name: 'osint_status',
          query: `
            SELECT osint_status as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM contacts WHERE osint_status IS NOT NULL), 2) as percentage
            FROM contacts 
            WHERE osint_status IS NOT NULL
            GROUP BY osint_status
            ORDER BY count DESC
          `
        }
      ];

      // Company status metrics
      const companyStatusQueries = [
        {
          name: 'website_scraping_status',
          query: `
            SELECT website_scraping_status as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM companies WHERE website_scraping_status IS NOT NULL), 2) as percentage
            FROM companies
            WHERE website_scraping_status IS NOT NULL
            GROUP BY website_scraping_status
            ORDER BY count DESC
          `
        },
        {
          name: 'company_overview_status',
          query: `
            SELECT company_overview_status as status_value, COUNT(*) as count,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM companies WHERE company_overview_status IS NOT NULL), 2) as percentage
            FROM companies
            WHERE company_overview_status IS NOT NULL
            GROUP BY company_overview_status
            ORDER BY count DESC
          `
        }
      ];

      // Key metrics
      const keyMetricsQuery = `
        SELECT 
          'Total Contacts' as metric,
          COUNT(*) as count
        FROM contacts
        
        UNION ALL
        
        SELECT 
          'Contacts with Email' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE email IS NOT NULL AND email != ''
        
        UNION ALL
        
        SELECT 
          'Verified Emails' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE email_status = 'Verified'
        
        UNION ALL
        
        SELECT 
          'Failed Email Verification' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE email_status IN ('Failed', 'Invalid')
        
        UNION ALL
        
        SELECT 
          'With Generated Email Content' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE email_generated = true
        
        UNION ALL
        
        SELECT 
          'In Smartlead' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_lead_id IS NOT NULL AND smartlead_lead_id != ''
        
        UNION ALL
        
        SELECT 
          'Emails Sent' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status IS NOT NULL AND smartlead_status != ''
        
        UNION ALL
        
        SELECT 
          'OSINT Researched' as metric,
          COUNT(*) as count
        FROM contacts
        WHERE searched = true
        
        UNION ALL
        
        SELECT 
          'Total Companies' as metric,
          COUNT(*) as count
        FROM companies
        
        UNION ALL
        
        SELECT 
          'Companies with Website' as metric,
          COUNT(*) as count
        FROM companies
        WHERE company_website IS NOT NULL AND company_website != ''
        
        UNION ALL
        
        SELECT 
          'Companies Scraped' as metric,
          COUNT(*) as count
        FROM companies
        WHERE processed = true
      `;

      // Email funnel metrics
      const emailFunnelQuery = `
        SELECT 
          'draft' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE email_generated = true AND (smartlead_lead_id IS NULL OR smartlead_lead_id = '')
        
        UNION ALL
        
        SELECT 
          'sent' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'ACTIVE' OR smartlead_status = 'ADDED' OR smartlead_status = 'SENT'
        
        UNION ALL
        
        SELECT 
          'delivered' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'DELIVERED'
        
        UNION ALL
        
        SELECT 
          'opened' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'OPENED'
        
        UNION ALL
        
        SELECT 
          'clicked' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'CLICKED'
        
        UNION ALL
        
        SELECT 
          'replied' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'REPLIED'
        
        UNION ALL
        
        SELECT 
          'bounced' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'BOUNCED'
        
        UNION ALL
        
        SELECT 
          'unsubscribed' as stage,
          COUNT(*) as count
        FROM contacts
        WHERE smartlead_status = 'UNSUBSCRIBED'
      `;

      // Get all status metrics in parallel
      const contactStatusResults = await Promise.all(
        contactStatusQueries.map(async (query) => {
          const result = await client.query(query.query);
          return {
            name: query.name,
            values: result.rows
          };
        })
      );
      
      const companyStatusResults = await Promise.all(
        companyStatusQueries.map(async (query) => {
          const result = await client.query(query.query);
          return {
            name: query.name,
            values: result.rows
          };
        })
      );

      // Get key metrics
      const keyMetricsResult = await client.query(keyMetricsQuery);
      
      // Get email funnel metrics
      const emailFunnelResult = await client.query(emailFunnelQuery);
      
      // Calculate data quality scores
      const dataQualityScores = calculateDataQualityScores(
        keyMetricsResult.rows,
        contactStatusResults,
        companyStatusResults
      );

      return NextResponse.json({
        success: true,
        data: {
          contactStatus: contactStatusResults,
          companyStatus: companyStatusResults,
          keyMetrics: keyMetricsResult.rows,
          emailFunnel: emailFunnelResult.rows,
          dataQualityScores
        },
        metadata: {
          generatedAt: new Date().toISOString()
        }
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Data quality API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Function to calculate data quality scores
function calculateDataQualityScores(
  keyMetrics: any[],
  contactStatus: any[],
  companyStatus: any[]
) {
  // Helper to find value in keyMetrics
  const getMetricValue = (metricName: string): number => {
    const metric = keyMetrics.find(m => m.metric === metricName);
    return metric ? parseInt(metric.count) : 0;
  };

  // Calculate completeness scores
  const totalContacts = getMetricValue('Total Contacts');
  const contactsWithEmail = getMetricValue('Contacts with Email');
  const verifiedEmails = getMetricValue('Verified Emails');
  const withGeneratedEmail = getMetricValue('With Generated Email Content');
  const emailsSent = getMetricValue('Emails Sent');
  const osintResearched = getMetricValue('OSINT Researched');
  
  const emailCompleteness = totalContacts > 0 ? (contactsWithEmail / totalContacts) * 100 : 0;
  const verificationCompleteness = contactsWithEmail > 0 ? (verifiedEmails / contactsWithEmail) * 100 : 0;
  const contentGenerationCompleteness = verifiedEmails > 0 ? (withGeneratedEmail / verifiedEmails) * 100 : 0;
  const emailSendingCompleteness = withGeneratedEmail > 0 ? (emailsSent / withGeneratedEmail) * 100 : 0;
  const researchCompleteness = contactsWithEmail > 0 ? (osintResearched / contactsWithEmail) * 100 : 0;

  // Calculate company completeness
  const totalCompanies = getMetricValue('Total Companies');
  const companiesWithWebsite = getMetricValue('Companies with Website');
  const companiesScraped = getMetricValue('Companies Scraped');

  const websiteCompleteness = totalCompanies > 0 ? (companiesWithWebsite / totalCompanies) * 100 : 0;
  const scrapingCompleteness = companiesWithWebsite > 0 ? (companiesScraped / companiesWithWebsite) * 100 : 0;

  // Overall data quality score (weighted average)
  const overallContactScore = (
    emailCompleteness * 0.2 +
    verificationCompleteness * 0.3 +
    contentGenerationCompleteness * 0.2 +
    emailSendingCompleteness * 0.2 +
    researchCompleteness * 0.1
  ) / 100;

  const overallCompanyScore = (
    websiteCompleteness * 0.5 +
    scrapingCompleteness * 0.5
  ) / 100;

  return {
    contact: {
      emailCompleteness: parseFloat(emailCompleteness.toFixed(2)),
      verificationCompleteness: parseFloat(verificationCompleteness.toFixed(2)),
      contentGenerationCompleteness: parseFloat(contentGenerationCompleteness.toFixed(2)),
      emailSendingCompleteness: parseFloat(emailSendingCompleteness.toFixed(2)),
      researchCompleteness: parseFloat(researchCompleteness.toFixed(2)),
      overallScore: parseFloat(overallContactScore.toFixed(2))
    },
    company: {
      websiteCompleteness: parseFloat(websiteCompleteness.toFixed(2)),
      scrapingCompleteness: parseFloat(scrapingCompleteness.toFixed(2)),
      overallScore: parseFloat(overallCompanyScore.toFixed(2))
    }
  };
} 