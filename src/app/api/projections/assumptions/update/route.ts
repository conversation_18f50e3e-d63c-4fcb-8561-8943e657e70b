import { pool } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const { category, field, value } = await request.json()

    // Input validation
    if (!category || !field || value === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const query = `
      UPDATE projections_assumptions 
      SET value = $3, updated_at = CURRENT_TIMESTAMP
      WHERE category = $1 AND field = $2
      RETURNING *
    `

    const result = await pool.query(query, [category, field, value])

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Assumption not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: 'Failed to update assumption' }, 
      { status: 500 }
    )
  }
} 