"use client"

import React from 'react';
import { 
  CheckCircle, 
  Clock, 
  ChevronRight,
  Building2,
  Target,
  Database,
  Users,
  LucideIcon
} from 'lucide-react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

interface CardSectionProps {
  children: React.ReactNode;
}

interface QuestionItem {
  text: string;
  hint?: string;
  completed: boolean;
}

interface Question {
  text: string;
  items: QuestionItem[];
}

interface Section {
  title: string;
  icon: LucideIcon;
  questions: Question[];
}

interface QuestionSectionProps extends Section {}

const Card: React.FC<CardProps> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<CardSectionProps> = ({ children }) => (
  <div className="border-b border-gray-200 p-4 sm:p-6">{children}</div>
);

const CardContent: React.FC<CardSectionProps> = ({ children }) => (
  <div className="p-4 sm:p-6">{children}</div>
);

const QuestionSection: React.FC<QuestionSectionProps> = ({ title, icon: Icon, questions }) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Icon className="h-5 w-5 text-gray-400" />
          <h3 className="text-base font-semibold text-gray-900">{title}</h3>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {questions.map((question, idx) => (
            <div key={idx} className="border-b border-gray-100 last:border-0 pb-4 last:pb-0">
              <h4 className="font-medium text-gray-900 mb-3">{question.text}</h4>
              <div className="space-y-3">
                {question.items.map((item, itemIdx) => (
                  <div key={itemIdx} className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      {item.completed ? (
                        <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                      ) : (
                        <div className="h-5 w-5 rounded-full bg-gray-100 flex items-center justify-center">
                          <Clock className="h-4 w-4 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-gray-900">{item.text}</p>
                      {item.hint && (
                        <p className="text-sm text-gray-500 mt-1">{item.hint}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const Assessment: React.FC = () => {
  const sections: Section[] = [
    {
      title: "Industry Analysis",
      icon: Building2,
      questions: [
        {
          text: "Market Context & Position",
          items: [
            {
              text: "What are the key trends affecting the capital advisory industry?",
              hint: "Focus on technology adoption, regulatory changes, and market dynamics",
              completed: true
            },
            {
              text: "What differentiates Anax in the market?",
              hint: "Consider both traditional and tech-enabled competitors",
              completed: true
            },
            {
              text: "What are the primary market opportunities identified?",
              hint: "Include both immediate and long-term opportunities",
              completed: false
            }
          ]
        },
        {
          text: "Technology Landscape",
          items: [
            {
              text: "What is your current technology stack and its limitations?",
              completed: true
            },
            {
              text: "What are competitors doing with AI/ML?",
              hint: "Focus on successful implementations and failures",
              completed: false
            }
          ]
        }
      ]
    },
    {
      title: "Data Infrastructure",
      icon: Database,
      questions: [
        {
          text: "Current Data Assets",
          items: [
            {
              text: "What are your primary data sources and how are they managed?",
              hint: "Include both internal and external data sources",
              completed: true
            },
            {
              text: "What are the main data quality challenges?",
              completed: false
            },
            {
              text: "What additional data sources would be valuable?",
              completed: false
            }
          ]
        }
      ]
    },
    {
      title: "Business Objectives",
      icon: Target,
      questions: [
        {
          text: "Strategic Goals & KPIs",
          items: [
            {
              text: "What are your primary business objectives for the next 12-24 months?",
              completed: true
            },
            {
              text: "How do you measure success? What are your key metrics?",
              completed: false
            },
            {
              text: "What operational challenges need to be addressed?",
              completed: false
            }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Vision Alignment Workshop</h2>
          <p className="mt-1 text-sm text-gray-500">
            Assessment questionnaire to establish implementation priorities and approach
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900">45% Complete</div>
            <div className="text-sm text-gray-500">12 of 27 items</div>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="h-2 w-full bg-gray-100 rounded-full">
        <div className="h-2 bg-blue-600 rounded-full" style={{ width: '45%' }} />
      </div>

      {/* Question sections */}
      {sections.map((section, idx) => (
        <QuestionSection key={idx} {...section} />
      ))}

      {/* Action buttons */}
      <div className="flex justify-end gap-4">
        <button 
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onClick={() => console.log('Saving progress...')}
        >
          Save Progress
        </button>
        <button 
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
          onClick={() => console.log('Completing assessment...')}
        >
          Complete Assessment
        </button>
      </div>
    </div>
  );
};

export { Assessment };
export default Assessment; 