import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';

interface FunnelMetric {
  stage: string;
  starting: number;
  converted: number;
  timeInDays: number;
  revenue?: number;
}

interface FunnelMetricsCardProps {
  metrics: FunnelMetric[];
}

export const FunnelMetricsCard = ({ metrics }: FunnelMetricsCardProps) => (
  <Card>
    <CardHeader>
      <CardTitle>Sales Funnel Metrics</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {metrics.map((stage) => (
          <div key={stage.stage} className="border-b pb-4">
            <h4 className="font-medium">{stage.stage}</h4>
            <div className="grid grid-cols-3 gap-4 mt-2">
              <div>
                <p className="text-sm text-gray-500">Starting</p>
                <p className="font-medium">{Math.round(stage.starting)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Converted</p>
                <p className="font-medium">{Math.round(stage.converted)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Time (Days)</p>
                <p className="font-medium">{stage.timeInDays}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
); 