"use client"

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Globe, Activity, MapPin } from 'lucide-react';
import { ScrapedData } from '../types';

interface ExtractedDataSectionProps {
  normalizedScrapedData?: ScrapedData;
}

const ExtractedDataSection: React.FC<ExtractedDataSectionProps> = ({ normalizedScrapedData }) => {
  const profile = normalizedScrapedData?.companyProfile;
  if (!profile) return null;
  return (
    <Card className="border border-red-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-gradient-to-r from-white to-red-50/30">
      <CardHeader className="pb-2 bg-gradient-to-r from-red-50 to-red-100/50">
        <CardTitle className="text-lg font-medium text-red-800 flex items-center gap-2">
          <Globe className="h-5 w-5 text-red-500" />
          Company Profile
        </CardTitle>
      </CardHeader>
      <CardContent className="p-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {profile.companyWebsite && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Company Website:</span>
              <span className="ml-2 text-gray-800">{profile.companyWebsite}</span>
            </div>
          )}
          {profile.companyName && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Company Name:</span>
              <span className="ml-2 text-gray-800">{profile.companyName}</span>
            </div>
          )}
          {profile.companyType && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Company Type:</span>
              <span className="ml-2 text-gray-800">{profile.companyType}</span>
            </div>
          )}
          {profile.aum && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">AUM:</span>
              <span className="ml-2 text-gray-800">{profile.aum}</span>
            </div>
          )}
          {profile.fundSize && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Fund Size:</span>
              <span className="ml-2 text-gray-800">{profile.fundSize}</span>
            </div>
          )}
          {profile.headquarters && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Headquarters:</span>
              <span className="ml-2 text-gray-800">{profile.headquarters}</span>
            </div>
          )}
          {profile.numberOfEmployees && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Employees:</span>
              <span className="ml-2 text-gray-800">{profile.numberOfEmployees}</span>
            </div>
          )}
          {profile.foundedYear && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Founded:</span>
              <span className="ml-2 text-gray-800">{profile.foundedYear}</span>
            </div>
          )}
          {profile.numberOfProperties && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Properties:</span>
              <span className="ml-2 text-gray-800">{profile.numberOfProperties}</span>
            </div>
          )}
          {profile.numberOfOffices && (
            <div className="flex items-center p-2 rounded-md hover:bg-gray-50 transition-colors">
              <span className="font-medium text-gray-700 min-w-32">Offices:</span>
              <span className="ml-2 text-gray-800">{profile.numberOfOffices}</span>
            </div>
          )}
        </div>
        {profile.businessModel && (
          <div className="mt-5 pt-4 border-t border-red-100">
            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
              <Activity className="h-4 w-4 mr-2 text-red-500" />
              Business Model:
            </h4>
            <p className="text-gray-800 bg-red-50 p-4 rounded-md border-l-4 border-red-300">
              {profile.businessModel}
            </p>
          </div>
        )}
        {profile.officeLocations && profile.officeLocations.length > 0 && (
          <div className="mt-5 pt-4 border-t border-red-100">
            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-red-500" />
              Office Locations:
            </h4>
            <div className="flex flex-wrap gap-2">
              {profile.officeLocations.map((location, idx) => (
                <Badge key={idx} className="bg-gray-100/80 text-gray-800 hover:bg-gray-200 transition-colors py-1 px-2.5">
                  {location}
                </Badge>
              ))}
            </div>
          </div>
        )}
        {profile.investmentFocus && profile.investmentFocus.length > 0 && (
          <div className="mt-5 pt-4 border-t border-red-100">
            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
              <Activity className="h-4 w-4 mr-2 text-red-500" />
              Investment Focus:
            </h4>
            <div className="flex flex-wrap gap-2">
              {profile.investmentFocus.map((focus, idx) => (
                <Badge key={idx} className="bg-blue-100/70 text-blue-800 hover:bg-blue-200 transition-colors py-1 px-2.5">{focus}</Badge>
              ))}
            </div>
          </div>
        )}
        {profile.geographicFocus && profile.geographicFocus.length > 0 && (
          <div className="mt-5 pt-4 border-t border-red-100">
            <h4 className="font-medium text-gray-700 mb-2 flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-red-500" />
              Geographic Focus:
            </h4>
            <div className="flex flex-wrap gap-2">
              {profile.geographicFocus.map((geo, idx) => (
                <Badge key={idx} className="bg-green-100/70 text-green-800 hover:bg-green-200 transition-colors py-1 px-2.5">{geo}</Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ExtractedDataSection; 