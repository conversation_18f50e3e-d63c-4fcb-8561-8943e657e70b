import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const result = await pool.query(`
      SELECT 
        id,
        name,
        category,
        default_value,
        data_type,
        description,
        created_at,
        updated_at
      FROM assumptions 
      ORDER BY category, name
    `)

    return NextResponse.json({ assumptions: result.rows })
  } catch (error) {
    console.error('Error fetching assumptions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch assumptions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, category, default_value, data_type, description } = body

    if (!name || !category || !data_type) {
      return NextResponse.json(
        { error: 'Missing required fields: name, category, data_type' },
        { status: 400 }
      )
    }

    const result = await pool.query(`
      INSERT INTO assumptions (name, category, default_value, data_type, description)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, category, default_value, data_type, description])

    return NextResponse.json({ assumption: result.rows[0] }, { status: 201 })
  } catch (error) {
    console.error('Error creating assumption:', error)
    return NextResponse.json(
      { error: 'Failed to create assumption' },
      { status: 500 }
    )
  }
} 