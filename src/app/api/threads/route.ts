import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    console.log('API: Received threads request:', req.url);
    
    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || '';
    const contactId = url.searchParams.get('contact_id');
    const contactEmail = url.searchParams.get('contact_email');
    const status = url.searchParams.get('status');
    const sort = url.searchParams.get('sort') || 'updated_at';
    const direction = url.searchParams.get('direction') || 'desc';
    
    // Log the parsed parameters
    console.log('API: Parsed parameters:', { 
      page, limit, search, contactId, contactEmail, status, sort, direction 
    });
    
    // Validate sort field to prevent SQL injection
    const validSortFields = ['created_at', 'updated_at', 'subject', 'status', 'message_count', 'participant_count'];
    const sortField = validSortFields.includes(sort) ? sort : 'updated_at';
    const sortDirection = direction.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    const offset = (page - 1) * limit;
    
    // Build the conditions for the SQL query
    const conditions = [];
    const params = [];
    let paramIndex = 1;
    
    // Add search condition if provided
    if (search) {
      conditions.push(`(t.subject ILIKE $${paramIndex})`);
      params.push(`%${search}%`);
      paramIndex++;
    }
    
    // Add contact filter if provided
    if (contactId) {
      conditions.push(`EXISTS (
        SELECT 1 FROM thread_participants tp 
        WHERE tp.thread_id = t.thread_id 
        AND tp.contact_id = $${paramIndex}
      )`);
      params.push(contactId);
      paramIndex++;
    }
    
    // Add contact email filter if provided (partial match, case-insensitive)
    if (contactEmail) {
      conditions.push(`EXISTS (
        SELECT 1 FROM thread_participants tp2
        JOIN contacts c ON c.contact_id = tp2.contact_id
        WHERE tp2.thread_id = t.thread_id
          AND c.email ILIKE $${paramIndex}
      )`);
      params.push(`%${contactEmail}%`);
      paramIndex++;
    }
    
    // Add status filter if provided
    if (status && status !== 'all') {
      conditions.push(`t.status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // Log the constructed SQL conditions
    console.log('API: SQL conditions:', { 
      whereClause, 
      params 
    });
    
    // First, get the total count for pagination
    const countQuery = `
      SELECT COUNT(DISTINCT t.thread_id) as total
      FROM threads t
      ${whereClause}
    `;
    
    const countResult = await pool.query(countQuery, params);
    const totalCount = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalCount / limit);
    
    console.log('API: Count result:', { totalCount, totalPages });
    
    // Determine if we're sorting by a calculated field or a direct table field
    let orderByClause;
    if (sortField === 'message_count' || sortField === 'participant_count') {
      // For calculated fields, reference the alias directly
      orderByClause = `${sortField} ${sortDirection}`;
    } else {
      // For fields in the threads table, use t.field_name
      orderByClause = `t.${sortField} ${sortDirection}`;
    }
    
    // Then get the actual data
    const dataQuery = `
      SELECT 
        t.thread_id,
        t.subject,
        t.status,
        t.created_at,
        t.updated_at,
        t.metadata,
        COUNT(DISTINCT m.message_id) as message_count,
        COUNT(DISTINCT tp.contact_id) as participant_count,
        ARRAY(
          SELECT DISTINCT tp2.email
          FROM thread_participants tp2
          WHERE tp2.thread_id = t.thread_id
        ) as participant_emails
      FROM threads t
      LEFT JOIN messages m ON t.thread_id = m.thread_id
      LEFT JOIN thread_participants tp ON t.thread_id = tp.thread_id
      ${whereClause}
      GROUP BY t.thread_id
      ORDER BY ${orderByClause}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    const dataParams = [...params, limit, offset];
    
    console.log('API: Running data query with params:', dataParams);
    
    try {
      const dataResult = await pool.query(dataQuery, dataParams);
      console.log('API: Got thread results:', dataResult.rows.length);
      
      return NextResponse.json({
        threads: dataResult.rows,
        totalCount,
        totalPages,
        currentPage: page
      });
    } catch (dbError) {
      console.error('API: Database error executing thread query:', dbError);
      return NextResponse.json(
        { 
          error: `Database error: ${(dbError as Error).message}`,
          query: dataQuery,
          params: dataParams
        }, 
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('API: Error fetching threads:', error);
    return NextResponse.json(
      { 
        error: `Error fetching threads: ${(error as Error).message}`,
        stack: (error as Error).stack
      }, 
      { status: 500 }
    );
  }
} 