import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET() {
  try {
    const query = `
      SELECT contact_source, COUNT(*) as count
      FROM persons
      WHERE contact_source IS NOT NULL 
        AND contact_source != ''
        AND email IS NOT NULL
        AND email != ''
      GROUP BY contact_source
      ORDER BY count DESC
    `
    const result = await pool.query(query)
    
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json(
      { error: "Failed to fetch contact sources" },
      { status: 500 }
    )
  }
} 