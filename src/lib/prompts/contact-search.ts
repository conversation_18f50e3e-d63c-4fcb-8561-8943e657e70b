export const CONTACT_SEARCH_SYSTEM_PROMPT = `You are an elite open-source-intelligence (OSINT) analyst for ANAX Capital Advisory.

Task
• Gather ALL publicly verifiable information on the contact below—career, deals, investment focus, speaking gigs, press quotes, education, hobbies, anything useful.  
• Think of it like scraping a company website: capture everything, don't curate.  
• Concise sentences, bullet lists welcome.  
• Embed source URLs inline in parentheses immediately after each fact.  
• Return ONE JSON object with ONE key: "profile". Nothing else.`

export const CONTACT_SEARCH_USER_TEMPLATE = `### Known data
First_Name        : {{first_name}}
Last_Name         : {{last_name}}
Primary_Email     : {{email}}
Company           : {{company}}
Job_Title         : {{job_title}}
LinkedIn_Profile  : {{linkedin_url}}
Company_Website   : {{company_website}}
Country / Region  : {{country}} / {{geographic_region}}
Phone_Number      : {{phone_number}}
Industry          : {{industry}}
Extra_Attributes  : {{extra_attrs}}

### Deliverable (JSON ONLY)
{
  "profile": "<everything you find, 500–1000 words, with inline URLs for each fact>"
}

Rules
• Do NOT invent data; include a URL for every statement.  
• No other keys, no commentary outside JSON.`

// Template function for backward compatibility
export const CONTACT_SEARCH_USER_TEMPLATE_FUNCTION = (contact: {
  first_name?: string
  last_name?: string
  email: string
  title?: string
  company_name?: string
  company_website?: string
  linkedin_url?: string
  industry?: string
  contact_country?: string
  phone_number?: string
  extra_attrs?: any
}, companyContext?: string) => {
  return CONTACT_SEARCH_USER_TEMPLATE
    .replace(/\{\{first_name\}\}/g, contact.first_name || 'Not specified')
    .replace(/\{\{last_name\}\}/g, contact.last_name || 'Not specified')
    .replace(/\{\{email\}\}/g, contact.email)
    .replace(/\{\{company\}\}/g, contact.company_name || 'Not specified')
    .replace(/\{\{job_title\}\}/g, contact.title || 'Not specified')
    .replace(/\{\{linkedin_url\}\}/g, contact.linkedin_url || 'Not specified')
    .replace(/\{\{company_website\}\}/g, contact.company_website || companyContext || 'Not specified')
    .replace(/\{\{country\}\}/g, contact.contact_country || 'Not specified')
    .replace(/\{\{geographic_region\}\}/g, contact.contact_country || 'Not specified')
    .replace(/\{\{phone_number\}\}/g, contact.phone_number || 'Not specified')
    .replace(/\{\{industry\}\}/g, contact.industry || 'Not specified')
    .replace(/\{\{extra_attrs\}\}/g, contact.extra_attrs ? JSON.stringify(contact.extra_attrs) : 'None')
} 