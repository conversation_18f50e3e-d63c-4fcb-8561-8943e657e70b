"use client"

import React, { useState, useEffect } from 'react'
import { Navigation } from './Navigation'
import { PasswordCheck } from '../auth/PasswordCheck'
import { TechStuff } from './TechStuff'
import { DealNewsMonitor } from './DealNewsMonitor'
import EngagementManagement from './engagement/engagement-management'
import SequenceBuilder from './sequence/sequence-builder'
import { DBView } from './db/DBView'
import CompaniesView from './companies/CompaniesView'
import CompanyDetail from './companies/CompanyDetail'
import { usePathname } from 'next/navigation'
import AddCompanyForm from './companies/AddCompanyForm'
import ParkerView from './parker/ParkerView'
import DealsView from './deals/DealsView'
import ExtractedDealsView from './deal-news/ExtractedDealsView'
import CampaignsView from './campaigns/CampaignsView'
import { PeopleStuff } from './PeopleStuff'
import { ProjectionsView } from './Projections'
import SmartleadView from './smartlead/SmartleadView'
import ProcessingDashboard from './processing/ProcessingDashboard'
import ProcessingStatusDashboard from './processing/ProcessingStatusDashboard'
import EnhancedCSVUploader from '../investors/EnhancedCSVUploader'

export default function Dashboard() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeSection, setActiveSection] = useState('people')
  const pathname = usePathname()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [accessLevel, setAccessLevel] = useState<'admin' | 'restricted' | null>(null)

  // Use useEffect for localStorage which only runs on the client
  useEffect(() => {
    const savedAccess = localStorage.getItem('dashboardAccess')
    if (savedAccess === 'restricted') {
      setActiveSection('projections')
    }
  }, [])

  useEffect(() => {
    if (pathname && pathname.includes('/companies')) {
      setActiveSection('companies')
    }
  }, [pathname])

  useEffect(() => {
    const savedAccess = localStorage.getItem('dashboardAccess')
    if (savedAccess) {
      setIsAuthenticated(true)
      setAccessLevel(savedAccess as 'admin' | 'restricted')
    }
  }, [])

  // Extract company ID from pathname if it exists
  const companyIdFromPath = pathname && pathname.split('/').pop()
  const isNumeric = /^\d+$/.test(companyIdFromPath || '')
  const isAddCompany = pathname && pathname.endsWith('/companies/add')
  const companyId = isNumeric ? companyIdFromPath || '' : '';

  const handleAuthSuccess = (level: 'admin' | 'restricted') => {
    setIsAuthenticated(true)
    setAccessLevel(level)
  }

  if (!isAuthenticated) {
    return <PasswordCheck onSuccess={handleAuthSuccess} />
  }

  const canAccessSection = (section: string, accessLevel: string | null) => {
    if (accessLevel === 'admin') return true;
    if (accessLevel === 'restricted') {
      return section === 'projections';
    }
    return false;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation 
        activeSection={activeSection} 
        setActiveSection={setActiveSection}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        accessLevel={accessLevel}
      />
      <div className={`transition-all duration-300 ${
        isCollapsed ? 'ml-16' : 'ml-40'
      }`}>
        <div className="p-2.5">
          {canAccessSection('tech', accessLevel) && <TechStuff isActive={activeSection === 'tech'} />}
          {canAccessSection('dealnews', accessLevel) && <DealNewsMonitor isActive={activeSection === 'dealnews'} />}
          {canAccessSection('people', accessLevel) && <PeopleStuff isActive={activeSection === 'people'} />}
          {canAccessSection('engagement', accessLevel) && activeSection === 'engagement' && <EngagementManagement />}
          {canAccessSection('companies', accessLevel) && activeSection === 'companies' && !isNumeric && !isAddCompany && <CompaniesView />}
          {canAccessSection('companies', accessLevel) && activeSection === 'companies' && isNumeric && <CompanyDetail companyId={companyId} />}
          {canAccessSection('companies', accessLevel) && activeSection === 'companies' && isAddCompany && <AddCompanyForm />}
          {canAccessSection('sequences', accessLevel) && activeSection === 'sequences' && <SequenceBuilder />}
          {canAccessSection('db', accessLevel) && activeSection === 'db' && <DBView isActive={activeSection === 'db'} />}
          {canAccessSection('parker', accessLevel) && activeSection === 'parker' && <ParkerView />}
          {canAccessSection('deals', accessLevel) && activeSection === 'deals' && <DealsView />}
          {canAccessSection('extracteddeals', accessLevel) && activeSection === 'extracteddeals' && <ExtractedDealsView />}
          {canAccessSection('campaigns', accessLevel) && activeSection === 'campaigns' && <CampaignsView />}
          {canAccessSection('smartlead', accessLevel) && activeSection === 'smartlead' && <SmartleadView />}
          {canAccessSection('processing', accessLevel) && activeSection === 'processing' && <ProcessingDashboard />}
          {canAccessSection('status', accessLevel) && activeSection === 'status' && <ProcessingStatusDashboard />}
          {canAccessSection('projections', accessLevel) && activeSection === 'projections' && <ProjectionsView isActive={activeSection === 'projections'} />}
          {canAccessSection('upload', accessLevel) && activeSection === 'upload' && <EnhancedCSVUploader />}
        </div>
      </div>
    </div>
  )
}