export const COMPANY_OVERVIEW_SYSTEM_PROMPT = `You are FactScraper-GP<PERSON>, an AI assistant specialized in extracting comprehensive company information using both provided website content AND real-time web research. Your task is to extract EVERY POSSIBLE DETAIL about real estate investment companies and return them in a structured JSON format.

**CRITICAL INSTRUCTIONS:**
- You MUST return a single valid JSON object that conforms to the extraction schema below.
- Return ONLY the JSON object. Do NOT include any commentary, markdown, code blocks, or extra text.
- DO NOT include any thinking tags like <think></think> or reasoning sections.
- DO NOT include any explanatory text before or after the JSON.
- Make sure all property names in the JSON are properly quoted with double quotes.
- Ensure the JSON is properly formatted with proper use of braces, commas, and quotes.
- Escape any special characters in string values properly.
- If information for a field isn't available, use null for that field.
- For monetary values, use the format: "$X.XX million" or "$X.XX billion" (e.g. "$12.5 million").
- For percentages, include the percent sign (e.g. "8.5%").
- For lists, return empty arrays [] when no items are found.
- When multiple possibilities exist, include ALL relevant items.
- For categorical fields, only use allowed values as specified.
- DO NOT make up information or use placeholders.
- STRICTLY enforce character-length limits by truncating excess characters (use an ellipsis … if truncation occurs) so the final JSON never exceeds the specified limits.
- BE EXTREMELY THOROUGH - extract every detail you can find, even if it seems minor.
- Look for information in ALL sections: about pages, team pages, investment pages, portfolio pages, news, etc.
- Extract financial metrics, deal information, team details, investment criteria, and operational details.
- USE WEB SEARCH to find additional information not available in the provided website content.
- Search for recent news, press releases, SEC filings, industry reports, and other public information about the company.
- Cross-reference multiple sources to ensure accuracy and completeness.

**RESPONSE FORMAT: Start your response immediately with { and end with }. Nothing else.**

**If you do not follow these instructions, your output will be rejected.**`

export const COMPANY_OVERVIEW_USER_TEMPLATE = `Extract EVERY POSSIBLE detail about the company using:
1. The provided website-scraped text below
2. REAL-TIME WEB SEARCH for additional information about {{COMPANY_NAME}}

SEARCH FOR:
- Recent news and press releases about {{COMPANY_NAME}}
- SEC filings and regulatory documents
- Industry reports mentioning the company
- Recent deals, acquisitions, or investments
- Financial performance and fund information
- Executive team updates and leadership changes
- Portfolio companies and properties
- Investment criteria and strategy updates
- Partnership announcements
- Any other relevant public information

Be extremely thorough and extract information from all available sources. Format the output according to the comprehensive extraction schema below.

COMPREHENSIVE EXTRACTION SCHEMA:
{
  "companyProfile": {
    "companyName": string,      // Extracted company name as it appears on the website (max 255 chars)
    "companyType": string,      // Allowed values: {{COMPANY_TYPES}} (max 255 chars)
    "businessModel": string,    // Detailed description of how the company operates and makes money (2-3 sentences)
    "fundSize": string,         // e.g. "$500 million", "$2.3 billion", null if unknown (max 100 chars)
    "aum": string,              // Assets under management, e.g. "$1.2 billion" (max 100 chars)
    "numberOfProperties": number, // Number of properties in portfolio if mentioned
    "headquarters": string,     // City, State format preferred (max 255 chars)
    "numberOfOffices": number,  // Total number of offices if mentioned
    "officeLocations": [string], // List of office locations besides HQ 
    "foundedYear": number,      // e.g. 1995, null if unknown
    "numberOfEmployees": string, // Range or specific number if mentioned (max 100 chars)
    "investmentFocus": [string], // COMPREHENSIVE list of investment focus areas - extract ALL mentioned
    "geographicFocus": [string]  // COMPREHENSIVE list of geographic focus areas - extract ALL mentioned
  },
  "executiveTeam": [
    {
      "first_name": string,      // First name of executive
      "last_name": string,       // Last name of executive
      "full_name": string,       // Full name of executive
      "title": string,           // Job title
      "headline": string,        // Headline or summary if available
      "seniority": string,       // Seniority level if available
      "email": string,           // Work email if available, null if not
      "personal_email": string,  // Personal email if available, null if not
      "email_status": string,    // Email status if available
      "linkedin_url": string,    // LinkedIn profile URL if available
      "contact_city": string,    // City if available
      "contact_state": string,   // State if available
      "contact_country": string, // Country if available
      "phone": string,           // Phone if available, null if not
      "bio": string,             // Brief biography if available, null if not
      "category": string         // Category if available, use values from: {{CONTACT_CATEGORIES}} if applicable
    }
  ],
  "recentDeals": [
    {
      "property": string,   // Property name or description
      "location": string,   // Location of the property
      "dealType": string,   // Allowed values: ["Acquisition", "Disposition", "Financing", "Development", "Joint Venture", "Other"]
      "amount": string,     // Dollar amount if available
      "date": string,       // Date in any format, recent is preferred
      "propertyType": string, // Type of property involved in the deal
      "squareFeet": string, // Square footage if available
      "units": number       // Number of units if applicable (for multifamily, etc.)
    }
  ],
  "investmentStrategy": {
    "mission": string,       // Company's mission statement if available
    "approach": string,      // Detailed description of investment approach/philosophy
    "targetReturn": string,  // Target returns mentioned (e.g. "15-18% IRR") (max 100 chars)
    "propertyTypes": [string], // COMPREHENSIVE list of property types (e.g. "Multifamily", "Office", "Retail", "Industrial", "Hospitality", "Mixed Use", "Single Family", "Land", "Healthcare", "Special Use")
    "strategies": [string],  // COMPREHENSIVE list of strategies (e.g. "Core", "Core Plus", "Value-Add", "Opportunistic", "Distressed", "Rescue Capital")
    "assetClasses": [string], // COMPREHENSIVE list of asset classes (e.g. "Class A", "Class B", "Class C")
    "valueCreation": [string] // COMPREHENSIVE list of value creation strategies
  },
  "investmentCriteria": {
    "targetMarkets": [string], // COMPREHENSIVE list of specific markets company targets
    "dealSize": string,       // Range of deal sizes (e.g. "$5-50 million") (max 100 chars)
    "minimumDealSize": string, // Minimum deal size (e.g. "$5 million") (max 100 chars)
    "maximumDealSize": string, // Maximum deal size (e.g. "$50 million") (max 100 chars)
    "holdPeriod": string,     // Typical hold period (e.g. "3-5 years") (max 100 chars)
    "riskProfile": string,     // Description of risk appetite/profile (max 100 chars)
    "propertyTypes": [string], // COMPREHENSIVE list of property types the company invests in
    "propertySubcategories": [string], // COMPREHENSIVE list of property subcategories
    "assetTypes": [string],    // COMPREHENSIVE list of asset types the company invests in
    "loanTypes": [string]      // COMPREHENSIVE list of loan types offered/preferred
  },
  "capitalSources": [string], // COMPREHENSIVE list of capital sources (e.g. "Institutional Investors", "High Net Worth", "Family Offices", "Pension Funds", "Insurance Companies", "Sovereign Wealth Funds", "Private Equity", "Debt Funds")
  "financialProducts": [string], // COMPREHENSIVE list of financial products offered
  "trackRecord": {
    "totalTransactions": string, // Total number/value of transactions completed (max 100 chars)
    "totalSquareFeet": string,   // Total square footage acquired/developed/managed (max 100 chars)
    "totalUnits": string,        // Total units acquired/developed/managed (max 100 chars)
    "historicalReturns": string, // Historical return metrics if mentioned (max 100 chars)
    "portfolioValue": string     // Current portfolio value if mentioned (max 100 chars)
  },
  "partnerships": [string], // COMPREHENSIVE list of partnerships and joint ventures
  "contactInfo": {
    "website": string,     // Company website URL (max 255 chars)
    "mainPhone": string,   // Main company phone number (max 100 chars)
    "mainEmail": string,   // Main company email address (max 255 chars)
    "socialMedia": {       // Social media profiles - extract ALL found
      "linkedin": string,
      "twitter": string,
      "facebook": string,
      "instagram": string,
      "youtube": string
    }
  }
}

**EXTRACTION GUIDELINES:**
1. **Be Extremely Thorough**: Read through ALL content carefully and extract every detail
2. **Look Everywhere**: Check about pages, team pages, investment pages, portfolio pages, news, case studies, etc.
3. **Extract All Lists**: For any array fields, include ALL items you find, not just a few examples
4. **Financial Details**: Look for any mention of fund sizes, AUM, deal sizes, returns, etc.
5. **Investment Criteria**: Extract ALL investment preferences, criteria, and requirements
6. **Team Information**: Extract ALL team members with as much detail as possible
7. **Deal Information**: Look for any recent transactions, acquisitions, developments
8. **Geographic Information**: Extract ALL markets, regions, cities mentioned
9. **Property Information**: Extract ALL property types, asset classes, strategies mentioned
10. **Partnership Information**: Look for any mentioned partners, joint ventures, relationships

Website Data:
{{WEBSITE_TEXT}}

Website URL:
{{WEBSITE_URL}}

Company Name:
{{COMPANY_NAME}}

Allowed Values:
{
  "PROPERTY_TYPES": "Multifamily, Office, Retail, Industrial, Hospitality, Mixed Use, Single Family, Land, Healthcare, Special Use",
  "PROPERTY_SUBCATEGORIES": "Apartment, Condo, Office Building, Shopping Center, Warehouse, Hotel, Resort, Medical Office, Self-Storage",
  "ASSET_TYPES": "Core, Core Plus, Value-Add, Opportunistic, Distressed, Rescue Capital",
  "LOAN_TYPES": "Bridge, Construction, Permanent, Acquisition, Refinance, Senior Tranche, Senior Subordinated, Conduit Lending",
  "COMPANY_TYPES": "Real Estate Developer, Private Equity, Investment Firm, REIT, Investment Manager, Family Office, Asset Manager, Property Manager",
  "CONTACT_CATEGORIES": "Capital Source, Sponsor, Third-Party, Unknown"
}

**REMEMBER: Your response MUST be a single valid JSON object and nothing else. Do NOT include markdown, code blocks, or any explanation. BE EXTREMELY THOROUGH in your extraction - every detail matters!**`

// Template function for backward compatibility
export const COMPANY_OVERVIEW_USER_TEMPLATE_FUNCTION = (company: {
  company_name: string
  company_website: string
  industry?: string
}, websiteText: string) => {
  return COMPANY_OVERVIEW_USER_TEMPLATE
    .replace(/\{\{WEBSITE_TEXT\}\}/g, websiteText)
    .replace(/\{\{WEBSITE_URL\}\}/g, company.company_website)
    .replace(/\{\{COMPANY_NAME\}\}/g, company.company_name)
    .replace(/\{\{COMPANY_TYPES\}\}/g, '"Real Estate Developer", "Private Equity", "Investment Firm", "REIT", "Investment Manager", "Family Office", "Asset Manager", "Property Manager"')
    .replace(/\{\{CONTACT_CATEGORIES\}\}/g, '"Capital Source", "Sponsor", "Third-Party", "Unknown"')
    .replace(/\{\{PROPERTY_TYPES\}\}/g, '"Multifamily", "Office", "Retail", "Industrial", "Hospitality", "Mixed Use", "Single Family", "Land", "Healthcare", "Special Use"')
    .replace(/\{\{PROPERTY_SUBCATEGORIES\}\}/g, '"Apartment", "Condo", "Office Building", "Shopping Center", "Warehouse", "Hotel", "Resort", "Medical Office", "Self-Storage"')
    .replace(/\{\{ASSET_TYPES\}\}/g, '"Core", "Core Plus", "Value-Add", "Opportunistic", "Distressed", "Rescue Capital"')
    .replace(/\{\{LOAN_TYPES\}\}/g, '"Bridge", "Construction", "Permanent", "Acquisition", "Refinance", "Senior Tranche", "Senior Subordinated", "Conduit Lending"')
} 