"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  Filter,
  Users,
  Building2,
  Mail,
  CheckCircle,
  XCircle,
  Clock,
  PlayCircle,
  RefreshCw
} from 'lucide-react';
import { ContactWithProcessing, CompanyWithProcessing, ProcessingStage } from '@/types/processing';

interface EntityBrowserProps {
  onTriggerProcessing: (stage: ProcessingStage, entityType: 'contact' | 'company', batchSize?: number) => void;
}

export default function EntityBrowser({ onTriggerProcessing }: EntityBrowserProps) {
  const [contacts, setContacts] = useState<ContactWithProcessing[]>([]);
  const [companies, setCompanies] = useState<CompanyWithProcessing[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<number[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);

  useEffect(() => {
    fetchEntities();
  }, []);

  const fetchEntities = async () => {
    setLoading(true);
    try {
      // Fetch contacts
      const contactsResponse = await fetch('/api/processing/entities?type=contact&limit=100');
      const contactsResult = await contactsResponse.json();
      
      if (contactsResult.success) {
        setContacts(contactsResult.data);
      }

      // Fetch companies
      const companiesResponse = await fetch('/api/processing/entities?type=company&limit=100');
      const companiesResult = await companiesResponse.json();
      
      if (companiesResult.success) {
        setCompanies(companiesResult.data);
      }
    } catch (error) {
      console.error('Failed to fetch entities:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStateColor = (state: string) => {
    const stateColors: Record<string, string> = {
      email_unverified: 'bg-red-100 text-red-800',
      email_verified: 'bg-blue-100 text-blue-800',
      osint_pending: 'bg-yellow-100 text-yellow-800',
      osint_completed: 'bg-green-100 text-green-800',
      classification_pending: 'bg-orange-100 text-orange-800',
      classification_completed: 'bg-purple-100 text-purple-800',
      email_generation_pending: 'bg-pink-100 text-pink-800',
      email_generated: 'bg-indigo-100 text-indigo-800',
      email_sent: 'bg-green-100 text-green-800',
      website_unprocessed: 'bg-red-100 text-red-800',
      website_scraped: 'bg-blue-100 text-blue-800',
      overview_pending: 'bg-yellow-100 text-yellow-800',
      overview_completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      error: 'bg-red-100 text-red-800'
    };
    return stateColors[state] || 'bg-gray-100 text-gray-800';
  };

  const getStateIcon = (state: string) => {
    if (state.includes('pending') || state.includes('unprocessed')) {
      return <Clock className="h-4 w-4" />;
    }
    if (state.includes('completed') || state.includes('verified') || state.includes('sent')) {
      return <CheckCircle className="h-4 w-4" />;
    }
    if (state.includes('failed') || state.includes('error')) {
      return <XCircle className="h-4 w-4" />;
    }
    return <Clock className="h-4 w-4" />;
  };

  const filteredContacts = contacts.filter(contact =>
    contact.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.company_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredCompanies = companies.filter(company =>
    company.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.company_website?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleContactSelect = (contactId: number) => {
    setSelectedContacts(prev => 
      prev.includes(contactId) 
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const handleCompanySelect = (companyId: number) => {
    setSelectedCompanies(prev => 
      prev.includes(companyId) 
        ? prev.filter(id => id !== companyId)
        : [...prev, companyId]
    );
  };

  const processSelectedContacts = (stage: ProcessingStage) => {
    if (selectedContacts.length > 0) {
      onTriggerProcessing(stage, 'contact', selectedContacts.length);
      setSelectedContacts([]);
    }
  };

  const processSelectedCompanies = (stage: ProcessingStage) => {
    if (selectedCompanies.length > 0) {
      onTriggerProcessing(stage, 'company', selectedCompanies.length);
      setSelectedCompanies([]);
    }
  };

  // Mock data for demonstration
  const mockContacts: ContactWithProcessing[] = [
    {
      contact_id: 1,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      company_name: 'Acme Corp',
      current_state: 'email_verified',
      next_stage: 'contact_search',
      can_advance: true
    },
    {
      contact_id: 2,
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      company_name: 'Test Co',
      current_state: 'osint_completed',
      next_stage: 'contact_classification',
      can_advance: true
    }
  ];

  const mockCompanies: CompanyWithProcessing[] = [
    {
      company_id: 1,
      company_name: 'Acme Corp',
      company_website: 'https://acme.com',
      current_state: 'website_scraped',
      next_stage: 'company_overview',
      can_advance: true,
      contact_count: 5
    },
    {
      company_id: 2,
      company_name: 'Test Co',
      company_website: 'https://test.co',
      current_state: 'overview_completed',
      next_stage: null,
      can_advance: false,
      contact_count: 3
    }
  ];

  const displayContacts = contacts.length > 0 ? filteredContacts : mockContacts;
  const displayCompanies = companies.length > 0 ? filteredCompanies : mockCompanies;

  return (
    <div className="space-y-6">
      {/* Search and Controls */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Entity Browser</CardTitle>
              <CardDescription>Browse and manage contacts and companies in the processing pipeline</CardDescription>
            </div>
            <Button onClick={fetchEntities} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
          
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search contacts, companies, emails..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Tabs defaultValue="contacts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="contacts">
            <Users className="h-4 w-4 mr-2" />
            Contacts ({displayContacts.length})
          </TabsTrigger>
          <TabsTrigger value="companies">
            <Building2 className="h-4 w-4 mr-2" />
            Companies ({displayCompanies.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="contacts">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Contacts</CardTitle>
                <div className="flex space-x-2">
                  {selectedContacts.length > 0 && (
                    <>
                      <Button 
                        size="sm" 
                        onClick={() => processSelectedContacts('email_validation')}
                        disabled={!selectedContacts.length}
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Validate Emails ({selectedContacts.length})
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => processSelectedContacts('contact_search')}
                        disabled={!selectedContacts.length}
                      >
                        <Search className="h-4 w-4 mr-2" />
                        OSINT Search ({selectedContacts.length})
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading contacts...</span>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <input 
                          type="checkbox" 
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedContacts(displayContacts.map(c => c.contact_id));
                            } else {
                              setSelectedContacts([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Current State</TableHead>
                      <TableHead>Next Stage</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {displayContacts.map((contact) => (
                      <TableRow key={contact.contact_id}>
                        <TableCell>
                          <input 
                            type="checkbox" 
                            checked={selectedContacts.includes(contact.contact_id)}
                            onChange={() => handleContactSelect(contact.contact_id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {contact.first_name} {contact.last_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {contact.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{contact.company_name}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStateIcon(contact.current_state)}
                            <Badge className={getStateColor(contact.current_state)}>
                              {contact.current_state.replace('_', ' ')}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {contact.next_stage ? (
                            <Badge variant="outline">
                              {contact.next_stage.replace('_', ' ')}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">Complete</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {contact.can_advance && contact.next_stage && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => onTriggerProcessing(contact.next_stage!, 'contact', 1)}
                            >
                              <PlayCircle className="h-4 w-4 mr-1" />
                              Process
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="companies">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Companies</CardTitle>
                <div className="flex space-x-2">
                  {selectedCompanies.length > 0 && (
                    <>
                      <Button 
                        size="sm" 
                        onClick={() => processSelectedCompanies('company_overview')}
                        disabled={!selectedCompanies.length}
                      >
                        <Building2 className="h-4 w-4 mr-2" />
                        Scrape Websites ({selectedCompanies.length})
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => processSelectedCompanies('company_overview')}
                        disabled={!selectedCompanies.length}
                      >
                        <Search className="h-4 w-4 mr-2" />
                        Extract Overviews ({selectedCompanies.length})
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading companies...</span>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <input 
                          type="checkbox" 
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedCompanies(displayCompanies.map(c => c.company_id));
                            } else {
                              setSelectedCompanies([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Website</TableHead>
                      <TableHead>Contacts</TableHead>
                      <TableHead>Current State</TableHead>
                      <TableHead>Next Stage</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {displayCompanies.map((company) => (
                      <TableRow key={company.company_id}>
                        <TableCell>
                          <input 
                            type="checkbox" 
                            checked={selectedCompanies.includes(company.company_id)}
                            onChange={() => handleCompanySelect(company.company_id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{company.company_name}</div>
                        </TableCell>
                        <TableCell>
                          <a 
                            href={company.company_website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {company.company_website}
                          </a>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{company.contact_count}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStateIcon(company.current_state)}
                            <Badge className={getStateColor(company.current_state)}>
                              {company.current_state.replace('_', ' ')}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {company.next_stage ? (
                            <Badge variant="outline">
                              {company.next_stage.replace('_', ' ')}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">Complete</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {company.can_advance && company.next_stage && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => onTriggerProcessing(company.next_stage!, 'company', 1)}
                            >
                              <PlayCircle className="h-4 w-4 mr-1" />
                              Process
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 