import { BaseLLMProvider, LLMRequestOptions, LoggerInterface } from './BaseLLMProvider';
import { OpenAIProvider } from './OpenAIProvider';
import { PerplexityProvider } from './PerplexityProvider';

export type LLMProviderType = 'openai' | 'perplexity';

/**
 * Factory class for creating LLM providers
 */
export class LLMFactory {
  /**
   * Create an LLM provider based on the specified type
   */
  public static createProvider(
    type: LLMProviderType,
    logger: LoggerInterface,
    apiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): BaseLLMProvider {
    switch (type) {
      case 'openai':
        return new OpenAIProvider(logger, apiKey, defaultOptions);
      case 'perplexity':
        return new PerplexityProvider(logger, apiKey, defaultOptions);
      default:
        throw new Error(`Unsupported LLM provider type: ${type}`);
    }
  }

  /**
   * Create an LLM provider with fallback capability
   * If the primary provider fails, the fallback will be used
   */
  public static createWithFallback(
    primaryType: LLMProviderType,
    fallbackType: LLMProviderType,
    logger: LoggerInterface,
    primaryApiKey?: string,
    fallbackApiKey?: string,
    defaultOptions?: LLMRequestOptions
  ): FallbackLLMProvider {
    const primary = this.createProvider(primaryType, logger, primaryApiKey, defaultOptions);
    const fallback = this.createProvider(fallbackType, logger, fallbackApiKey, defaultOptions);
    
    return new FallbackLLMProvider(primary, fallback, logger);
  }
}

/**
 * LLM provider with fallback capability
 * If the primary provider fails, the fallback will be used
 */
export class FallbackLLMProvider extends BaseLLMProvider {
  private primaryProvider: BaseLLMProvider;
  private fallbackProvider: BaseLLMProvider;

  constructor(
    primaryProvider: BaseLLMProvider,
    fallbackProvider: BaseLLMProvider,
    logger: LoggerInterface
  ) {
    super(`Fallback(${primaryProvider.getName()}->${fallbackProvider.getName()})`, logger);
    this.primaryProvider = primaryProvider;
    this.fallbackProvider = fallbackProvider;
  }

  /**
   * Call the primary provider, falling back to the secondary if needed
   */
  public async callLLM(messages: any[], options?: LLMRequestOptions) {
    try {
      // Try the primary provider first
      return await this.primaryProvider.callLLM(messages, options);
    } catch (primaryError) {
      // Log the primary error
      this.logger.log('warn', `Primary LLM provider (${this.primaryProvider.getName()}) failed: ${primaryError}. Falling back to ${this.fallbackProvider.getName()}`);
      
      try {
        // Try the fallback provider
        return await this.fallbackProvider.callLLM(messages, options);
      } catch (fallbackError) {
        // If both fail, log and throw
        this.logger.log('error', `Fallback LLM provider (${this.fallbackProvider.getName()}) also failed: ${fallbackError}`);
        throw new Error(`Both LLM providers failed. Primary error: ${primaryError}. Fallback error: ${fallbackError}`);
      }
    }
  }
} 