'use client';

import React, { useState, createContext, useContext, useEffect } from 'react';
import { 
  LiveKitRoom, 
  useVoiceAssistant, 
  BarVisualizer, 
  RoomAudioRenderer,
  useLocalParticipant
} from '@livekit/components-react';
import { Room, ConnectionState } from 'livekit-client';
import '@livekit/components-styles';
import { ChevronDown, ChevronUp, X, Mic, MicOff, Loader } from 'lucide-react';
import './FloatingAssistant.css';

interface VoiceAssistantContextType {
  isOpen: boolean;
  openAssistant: () => void;
  closeAssistant: () => void;
}

interface FloatingAssistantProps {
  token: string;
  serverUrl: string;
  showFloatingButton?: boolean;
}

interface VoiceAssistantState {
  state: 'idle' | 'listening' | 'speaking' | 'thinking';
  audioTrack?: MediaStreamTrack;
}

const VoiceAssistantContext = createContext<VoiceAssistantContextType | undefined>(undefined);

export function VoiceAssistantProvider({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  const value = {
    isOpen,
    openAssistant: () => setIsOpen(true),
    closeAssistant: () => setIsOpen(false)
  };

  return (
    <VoiceAssistantContext.Provider value={value}>
      {children}
    </VoiceAssistantContext.Provider>
  );
}

export function useVoiceAssistantContext() {
  const context = useContext(VoiceAssistantContext);
  if (!context) {
    throw new Error('useVoiceAssistantContext must be used within a VoiceAssistantContext.Provider');
  }
  return context;
}

function VoiceAssistantUI() {
  const { state, audioTrack } = useVoiceAssistant();
  const [isExpanded, setIsExpanded] = useState(false);
  const { closeAssistant } = useVoiceAssistantContext();
  const [timer, setTimer] = useState(0);
  const { localParticipant } = useLocalParticipant();
  const [isConnecting, setIsConnecting] = useState(true);
  const [isMicrophoneEnabled, setIsMicrophoneEnabled] = useState(false);

  useEffect(() => {
    if (localParticipant) {
      setIsConnecting(false);
      // Enable microphone after connection is established
      const enableMic = async () => {
        try {
          await localParticipant.setMicrophoneEnabled(true);
          setIsMicrophoneEnabled(true);
        } catch (error) {
          console.error('Failed to enable microphone:', error);
        }
      };
      enableMic();
    }
  }, [localParticipant]);

  const toggleMicrophone = async () => {
    if (localParticipant) {
      try {
        const newState = !isMicrophoneEnabled;
        await localParticipant.setMicrophoneEnabled(newState);
        setIsMicrophoneEnabled(newState);
      } catch (error) {
        console.error('Failed to toggle microphone:', error);
      }
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if ((state === 'listening' || state === 'speaking') && isMicrophoneEnabled) {
      interval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 1000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [state, isMicrophoneEnabled]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isConnecting) {
    return (
      <div className="voice-assistant-content">
        <div className="flex items-center justify-center p-4">
          <Loader className="animate-spin text-white mr-2" size={20} />
          <span className="text-white">Connecting...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`voice-assistant-content ${isExpanded ? 'expanded' : ''}`} data-state={state}>
      {isExpanded ? (
        <div className="voice-assistant-expanded">
          <div className="expanded-header">
            <h3>Gift Registry Assistant</h3>
            <div className="header-controls">
              <button 
                className="control-button" 
                onClick={() => setIsExpanded(false)}
              >
                <ChevronDown />
              </button>
              <button 
                className="control-button"
                onClick={closeAssistant}
              >
                <X />
              </button>
            </div>
          </div>
          <div className="visualization-container">
            {isMicrophoneEnabled ? (
              <BarVisualizer 
                state={state}
                trackRef={audioTrack}
                barCount={5}
                className="audio-visualizer"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <MicOff className="w-8 h-8 text-gray-400" />
              </div>
            )}
          </div>
          <div className="expanded-footer">
            <div className="state-indicator">
              {isMicrophoneEnabled ? (
                <>
                  {state === 'listening' && (
                    <>
                      <span className="state-icon">⚪️</span>
                      listening...
                    </>
                  )}
                  {state === 'speaking' && (
                    <>
                      <span className="state-icon">🔵</span>
                      speaking...
                    </>
                  )}
                  {state === 'thinking' && (
                    <>
                      <span className="state-icon">💭</span>
                      thinking...
                    </>
                  )}
                </>
              ) : (
                <>
                  <span className="state-icon">🔴</span>
                  microphone off
                </>
              )}
            </div>
            <div className="timer">{formatTime(timer)}</div>
          </div>
          <button
            onClick={toggleMicrophone}
            className={`mic-toggle-button ${!isMicrophoneEnabled ? 'muted' : ''}`}
          >
            {isMicrophoneEnabled ? <Mic /> : <MicOff />}
          </button>
          <div className="powered-by">Powered by PeopleCo</div>
        </div>
      ) : (
        <div className="voice-assistant-compact">
          <div className="compact-content">
            <span className="assistant-title">PeopleCo Onboarding Assistant</span>
            <span className="timer">{formatTime(timer)}</span>
          </div>
          <div className="assistant-controls">
            <button 
              className="control-button expand" 
              onClick={() => setIsExpanded(true)}
            >
              <ChevronUp />
            </button>
            <button 
              className="control-button close"
              onClick={closeAssistant}
            >
              <X />
            </button>
          </div>
        </div>
      )}
      <RoomAudioRenderer />
    </div>
  );
}

export function FloatingAssistant({ token, serverUrl, showFloatingButton = true }: FloatingAssistantProps) {
  const { isOpen, openAssistant } = useVoiceAssistantContext();
  const [room, setRoom] = useState<Room | null>(null);

  const handleRoomConnected = (connectedRoom: Room) => {
    setRoom(connectedRoom);
    console.log('Connected to room');
  };

  return (
    <>
      {showFloatingButton && (
        <button
          onClick={openAssistant}
          className="voice-assistant-button"
          aria-label="Open Voice Assistant"
        >
          <Mic size={24} />
        </button>
      )}

      {isOpen && (
        <LiveKitRoom
          token={token}
          serverUrl={serverUrl}
          connect={true}
          audio={true}
          video={false}
          onConnected={handleRoomConnected}
          data-lk-theme="default"
        >
          <VoiceAssistantUI />
        </LiveKitRoom>
      )}
    </>
  );
} 