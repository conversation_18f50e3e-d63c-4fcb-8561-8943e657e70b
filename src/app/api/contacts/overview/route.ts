import { NextRequest, NextResponse } from 'next/server'
import { pool } from '../../../../lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const status = searchParams.get('status') // 'all', 'searched', 'extracted'

    let whereConditions = ['c.email_status = \'Verified\'']
    
    if (status === 'searched') {
      whereConditions.push('c.searched = true')
    } else if (status === 'extracted') {
      whereConditions.push('c.extracted = true')
    }

    const sql = `
      SELECT 
        c.contact_id,
        c.full_name,
        c.first_name,
        c.last_name,
        c.title,
        c.email,
        c.linkedin_url,
        c.company_id,
        co.company_name,
        co.company_website,
        co.industry,
        c.contact_country,
        c.email_status,
        c.searched,
        c.extracted,
        c.email_generated,
        c.created_at,
        c.updated_at
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.company_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY 
        CASE 
          WHEN c.searched = true AND c.extracted = false THEN 1
          WHEN c.searched = true AND c.extracted = true THEN 2
          ELSE 3
        END,
        c.updated_at DESC
      LIMIT $1 OFFSET $2
    `

    const result = await pool.query(sql, [limit, offset])

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total
      FROM contacts c
      WHERE ${whereConditions.join(' AND ')}
    `
    const countResult = await pool.query(countSql)
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: {
        contacts: result.rows,
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    })

  } catch (error) {
    console.error('Error fetching contacts for overview:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 