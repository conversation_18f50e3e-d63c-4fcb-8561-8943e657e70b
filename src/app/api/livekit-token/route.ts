import { AccessToken } from 'livekit-server-sdk';
import { NextRequest, NextResponse } from 'next/server';

async function generateToken(identity: string) {
  if (!process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET || !process.env.LIVEKIT_URL) {
    throw new Error('Missing LiveKit configuration');
  }

  const at = new AccessToken(
    process.env.LIVEKIT_API_KEY,
    process.env.LIVEKIT_API_SECRET,
    {
      identity,
      ttl: 60 * 60,
      name: identity
    }
  );

  at.addGrant({
    room: 'assistant-room', // Use consistent room name for the assistant
    roomJoin: true,
    canPublish: true,
    canSubscribe: true,
    canPublishData: true
  });

  return at.toJwt();
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const identity = body.userId || body.identity;

    if (!identity) {
      return NextResponse.json({
        error: 'Missing userId or identity'
      }, { status: 400 });
    }

    const token = await generateToken(identity);

    // Return exactly what FloatingAssistant expects
    return NextResponse.json({
      token,
      serverUrl: process.env.LIVEKIT_URL
    });

  } catch (error) {
    console.error('[LiveKit Token Error]:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    }, { status: 500 });
  }
}

// For testing purposes
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const identity = searchParams.get('identity');

    if (!identity) {
      return NextResponse.json({
        error: 'Missing identity parameter'
      }, { status: 400 });
    }

    const token = await generateToken(identity);

    return NextResponse.json({
      token,
      serverUrl: process.env.LIVEKIT_URL
    });

  } catch (error) {
    console.error('[LiveKit Token Error]:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    }, { status: 500 });
  }
}
