import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Search, Loader2, CheckCircle, X, ExternalLink, Building2, MapPin, Users, Calendar, DollarSign, Target } from 'lucide-react';
import { toast } from "sonner";
import { debounce } from 'lodash';
import Link from 'next/link';

interface AddContactProps {
  onBack: () => void;
}

interface CompanySuggestion {
  company_id?: number;
  company_name: string;
  company_website?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  // Extracted data for enhanced auto-fill
  extracted_data?: {
    companytype?: string;
    businessmodel?: string;
    fundsize?: string;
    aum?: string;
    headquarters?: string;
    foundedyear?: number;
    numberofemployees?: string;
    investmentfocus?: string[];
    geographicfocus?: string[];
    dealsize?: string;
    minimumdealsize?: string;
    maximumdealsize?: string;
    investment_criteria_property_types?: string[];
    investment_criteria_asset_types?: string[];
    investment_criteria_loan_types?: string[];
    investment_criteria_property_subcategories?: string[];
    riskprofile?: string;
    targetmarkets?: string[];
    strategies?: string[];
    propertytypes?: string[];
    assetclasses?: string[];
    valuecreation?: string[];
    holdperiod?: string;
    targetreturn?: string;
    approach?: string;
  };
}

const contactCategories = [
  { value: "investor", label: "Investor" },
  { value: "sponsor", label: "Sponsor" },
  { value: "third party", label: "Third Party" }
];

// Add this interface for suggestions type
interface SuggestionData {
  [key: string]: string;
}

const AddContact: React.FC<AddContactProps> = ({ onBack }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [companySuggestions, setCompanySuggestions] = useState<CompanySuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanySuggestion | null>(null);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  const companyInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  
  const [formData, setFormData] = useState({
    // Personal Information
    first_name: '',
    last_name: '',
    email: '',
    personal_email: '',
    title: '',
    linkedin_url: '',
    phone_number: '',
    
    // Company Information (enhanced)
    company_name: '',
    company_website: '',
    industry: '',
    company_address: '',
    company_city: '',
    company_state: '',
    company_country: '',
    
    // Contact Location
    contact_city: '',
    contact_state: '',
    contact_country: '',
    region: '',
    
    // Categories
    capital_type: '',
    contact_category: '',
    
    // Contact Investment Criteria (editable)
    investment_criteria_country: '',
    investment_criteria_state: '',
    investment_criteria_city: '',
    investment_criteria_property_type: '',
    investment_criteria_asset_type: '',
    investment_criteria_loan_type: '',
    investment_criteria_deal_size: '',
    
    // Notes
    notes: ''
  });

  // Click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (companyInputRef.current && !companyInputRef.current.contains(event.target as Node)) {
        // Check if clicking inside the search results area
        const searchResults = document.querySelector('[data-search-results]');
        if (searchResults && searchResults.contains(event.target as Node)) {
          return; // Don't close if clicking inside search results
        }
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Enhanced validation function with comprehensive rules
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    // Required field validations
    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required';
    } else if (formData.first_name.trim().length < 2) {
      errors.first_name = 'First name must be at least 2 characters';
    } else if (formData.first_name.trim().length > 50) {
      errors.first_name = 'First name must be less than 50 characters';
    } else if (!/^[a-zA-Z\s'-]+$/.test(formData.first_name.trim())) {
      errors.first_name = 'First name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    if (!formData.last_name.trim()) {
      errors.last_name = 'Last name is required';
    } else if (formData.last_name.trim().length < 2) {
      errors.last_name = 'Last name must be at least 2 characters';
    } else if (formData.last_name.trim().length > 50) {
      errors.last_name = 'Last name must be less than 50 characters';
    } else if (!/^[a-zA-Z\s'-]+$/.test(formData.last_name.trim())) {
      errors.last_name = 'Last name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    if (!formData.company_name.trim()) {
      errors.company_name = 'Company name is required';
    } else if (formData.company_name.trim().length < 2) {
      errors.company_name = 'Company name must be at least 2 characters';
    } else if (formData.company_name.trim().length > 100) {
      errors.company_name = 'Company name must be less than 100 characters';
    }
    
    if (!formData.company_website.trim()) {
      errors.company_website = 'Company website is required';
    } else {
      const urlPattern = /^https?:\/\/(?:[-\w.])+(?:\.[a-zA-Z]{2,})+(?:\/[^?\s]*)?(?:\?[^#\s]*)?(?:#[^\s]*)?$/;
      if (!urlPattern.test(formData.company_website.trim())) {
        errors.company_website = 'Please enter a valid website URL (e.g., https://example.com)';
      }
    }
    
    // Email validations
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    
    if (formData.email.trim()) {
      if (!emailPattern.test(formData.email.trim())) {
        errors.email = 'Please enter a valid business email address';
      } else if (formData.email.trim().length > 100) {
        errors.email = 'Email address must be less than 100 characters';
      }
    }
    
    if (formData.personal_email.trim()) {
      if (!emailPattern.test(formData.personal_email.trim())) {
        errors.personal_email = 'Please enter a valid personal email address';
      } else if (formData.personal_email.trim().length > 100) {
        errors.personal_email = 'Email address must be less than 100 characters';
      }
      // Check if personal email is same as business email
      if (formData.email.trim() && formData.personal_email.trim().toLowerCase() === formData.email.trim().toLowerCase()) {
        errors.personal_email = 'Personal email should be different from business email';
      }
    }
    
    // Optional field validations (only validate if provided)
    if (formData.title.trim() && formData.title.trim().length > 100) {
      errors.title = 'Job title must be less than 100 characters';
    }
    
    if (formData.linkedin_url.trim()) {
      const linkedinPattern = /^https?:\/\/(?:www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/;
      if (!linkedinPattern.test(formData.linkedin_url.trim())) {
        errors.linkedin_url = 'Please enter a valid LinkedIn profile URL (e.g., https://linkedin.com/in/username)';
      }
    }
    
    if (formData.phone_number.trim()) {
      // Allow various phone number formats
      const phonePattern = /^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)\.]{10,20}$/;
      const cleanPhone = formData.phone_number.replace(/[\s\-\(\)\.]/g, '');
      if (!phonePattern.test(cleanPhone) || cleanPhone.length < 10 || cleanPhone.length > 15) {
        errors.phone_number = 'Please enter a valid phone number (10-15 digits)';
      }
    }
    
    // Company location validations
    if (formData.company_address.trim() && formData.company_address.trim().length > 200) {
      errors.company_address = 'Company address must be less than 200 characters';
    }
    
    if (formData.company_city.trim()) {
      if (formData.company_city.trim().length > 50) {
        errors.company_city = 'City name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.company_city.trim())) {
        errors.company_city = 'City name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    if (formData.company_state.trim()) {
      if (formData.company_state.trim().length > 50) {
        errors.company_state = 'State/Province name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.company_state.trim())) {
        errors.company_state = 'State/Province name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    if (formData.company_country.trim()) {
      if (formData.company_country.trim().length > 50) {
        errors.company_country = 'Country name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.company_country.trim())) {
        errors.company_country = 'Country name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    // Contact location validations
    if (formData.contact_city.trim()) {
      if (formData.contact_city.trim().length > 50) {
        errors.contact_city = 'City name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.contact_city.trim())) {
        errors.contact_city = 'City name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    if (formData.contact_state.trim()) {
      if (formData.contact_state.trim().length > 50) {
        errors.contact_state = 'State/Province name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.contact_state.trim())) {
        errors.contact_state = 'State/Province name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    if (formData.contact_country.trim()) {
      if (formData.contact_country.trim().length > 50) {
        errors.contact_country = 'Country name must be less than 50 characters';
      } else if (!/^[a-zA-Z\s\-'\.]+$/.test(formData.contact_country.trim())) {
        errors.contact_country = 'Country name can only contain letters, spaces, hyphens, apostrophes, and periods';
      }
    }
    
    if (formData.region.trim() && formData.region.trim().length > 100) {
      errors.region = 'Region must be less than 100 characters';
    }
    
    // Investment criteria validations
    if (formData.investment_criteria_country.trim() && formData.investment_criteria_country.trim().length > 200) {
      errors.investment_criteria_country = 'Investment criteria country must be less than 200 characters';
    }
    
    if (formData.investment_criteria_state.trim() && formData.investment_criteria_state.trim().length > 200) {
      errors.investment_criteria_state = 'Investment criteria state must be less than 200 characters';
    }
    
    if (formData.investment_criteria_city.trim() && formData.investment_criteria_city.trim().length > 200) {
      errors.investment_criteria_city = 'Investment criteria city must be less than 200 characters';
    }
    
    if (formData.investment_criteria_property_type.trim() && formData.investment_criteria_property_type.trim().length > 200) {
      errors.investment_criteria_property_type = 'Property type must be less than 200 characters';
    }
    
    if (formData.investment_criteria_asset_type.trim() && formData.investment_criteria_asset_type.trim().length > 200) {
      errors.investment_criteria_asset_type = 'Asset type must be less than 200 characters';
    }
    
    if (formData.investment_criteria_loan_type.trim() && formData.investment_criteria_loan_type.trim().length > 200) {
      errors.investment_criteria_loan_type = 'Loan type must be less than 200 characters';
    }
    
    if (formData.investment_criteria_deal_size.trim() && formData.investment_criteria_deal_size.trim().length > 100) {
      errors.investment_criteria_deal_size = 'Deal size must be less than 100 characters';
    }
    
    // Notes validation
    if (formData.notes.trim() && formData.notes.trim().length > 1000) {
      errors.notes = 'Notes must be less than 1000 characters';
    }
    

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      if (searchTerm.length < 2) {
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const response = await fetch(`/api/companies/search?q=${encodeURIComponent(searchTerm)}`);
        if (response.ok) {
          const suggestions = await response.json();
          setCompanySuggestions(suggestions);
          setShowSuggestions(suggestions.length > 0);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        toast.error('Failed to search companies');
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  // Real-time field validation
  const validateField = (name: string, value: string) => {
    const errors: Record<string, string> = {};
    
    switch (name) {
      case 'first_name':
        if (value.trim() && value.trim().length < 2) {
          errors[name] = 'First name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          errors[name] = 'First name must be less than 50 characters';
        } else if (value.trim() && !/^[a-zA-Z\s'-]+$/.test(value.trim())) {
          errors[name] = 'First name can only contain letters, spaces, hyphens, and apostrophes';
        }
        break;
        
      case 'last_name':
        if (value.trim() && value.trim().length < 2) {
          errors[name] = 'Last name must be at least 2 characters';
        } else if (value.trim().length > 50) {
          errors[name] = 'Last name must be less than 50 characters';
        } else if (value.trim() && !/^[a-zA-Z\s'-]+$/.test(value.trim())) {
          errors[name] = 'Last name can only contain letters, spaces, hyphens, and apostrophes';
        }
        break;
        
      case 'email':
        if (value.trim()) {
          const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
          if (!emailPattern.test(value.trim())) {
            errors[name] = 'Please enter a valid business email address';
          } else if (value.trim().length > 100) {
            errors[name] = 'Email address must be less than 100 characters';
          }
        }
        break;
        
      case 'personal_email':
        if (value.trim()) {
          const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
          if (!emailPattern.test(value.trim())) {
            errors[name] = 'Please enter a valid personal email address';
          } else if (value.trim().length > 100) {
            errors[name] = 'Email address must be less than 100 characters';
          } else if (formData.email.trim() && value.trim().toLowerCase() === formData.email.trim().toLowerCase()) {
            errors[name] = 'Personal email should be different from business email';
          }
        }
        break;
        
      case 'linkedin_url':
        if (value.trim()) {
          const linkedinPattern = /^https?:\/\/(?:www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/;
          if (!linkedinPattern.test(value.trim())) {
            errors[name] = 'Please enter a valid LinkedIn profile URL (e.g., https://linkedin.com/in/username)';
          }
        }
        break;
        
      case 'phone_number':
        if (value.trim()) {
          const phonePattern = /^[\+]?[1-9][\d]{0,15}$|^[\+]?[(]?[\d\s\-\(\)\.]{10,20}$/;
          const cleanPhone = value.replace(/[\s\-\(\)\.]/g, '');
          if (!phonePattern.test(cleanPhone) || cleanPhone.length < 10 || cleanPhone.length > 15) {
            errors[name] = 'Please enter a valid phone number (10-15 digits)';
          }
        }
        break;
        
      case 'company_name':
        if (value.trim() && value.trim().length < 2) {
          errors[name] = 'Company name must be at least 2 characters';
        } else if (value.trim().length > 100) {
          errors[name] = 'Company name must be less than 100 characters';
        }
        break;
        
      case 'company_website':
        if (value.trim()) {
          const urlPattern = /^https?:\/\/(?:[-\w.])+(?:\.[a-zA-Z]{2,})+(?:\/[^?\s]*)?(?:\?[^#\s]*)?(?:#[^\s]*)?$/;
          if (!urlPattern.test(value.trim())) {
            errors[name] = 'Please enter a valid website URL (e.g., https://example.com)';
          }
        }
        break;
        
      case 'title':
        if (value.trim().length > 100) {
          errors[name] = 'Job title must be less than 100 characters';
        }
        break;
        
      case 'company_address':
        if (value.trim().length > 200) {
          errors[name] = 'Company address must be less than 200 characters';
        }
        break;
        
      case 'company_city':
      case 'company_state':
      case 'company_country':
      case 'contact_city':
      case 'contact_state':
      case 'contact_country':
        if (value.trim()) {
          if (value.trim().length > 50) {
            errors[name] = 'Location name must be less than 50 characters';
          } else if (!/^[a-zA-Z\s\-'\.]+$/.test(value.trim())) {
            errors[name] = 'Location name can only contain letters, spaces, hyphens, apostrophes, and periods';
          }
        }
        break;
        
             case 'region':
         if (value.trim().length > 100) {
           errors[name] = 'Region must be less than 100 characters';
         }
         break;
        
      case 'investment_criteria_country':
      case 'investment_criteria_state':
      case 'investment_criteria_city':
      case 'investment_criteria_property_type':
      case 'investment_criteria_asset_type':
      case 'investment_criteria_loan_type':
        if (value.trim().length > 200) {
          errors[name] = 'Investment criteria must be less than 200 characters';
        }
        break;
        
      case 'investment_criteria_deal_size':
        if (value.trim().length > 100) {
          errors[name] = 'Deal size must be less than 100 characters';
        }
        break;
        
      case 'notes':
        if (value.trim().length > 1000) {
          errors[name] = 'Notes must be less than 1000 characters';
        }
        break;
    }
    
    return errors[name] || '';
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Real-time validation
    const fieldError = validateField(name, value);
    setValidationErrors({
      ...validationErrors,
      [name]: fieldError
    });

    // Trigger company search when company_name changes
    if (name === 'company_name') {
      setSelectedCompany(null);
      setSelectedSuggestionIndex(-1);
      if (value.trim()) {
        debouncedSearchCompanies(value);
      } else {
        setShowSuggestions(false);
        setCompanySuggestions([]);
      }
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear validation error
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: ''
      });
    }
  };

  // Helper function to determine capital type
  const determineCapitalType = (companyType: string): string => {
    const type = companyType.toLowerCase();
    if (type.includes('debt') || type.includes('lending')) {
      return 'Debt';
    } else if (type.includes('equity') || type.includes('investment')) {
      return 'Equity';
    } else if (type.includes('mezzanine')) {
      return 'Mezzanine';
    }
    return '';
  };

  // Helper function to render criteria comparison
  const renderCriteriaComparison = (label: string, companyValue: string, currentValue: string, fieldName: keyof typeof formData): React.ReactNode => {
    // Only show if there's a company value
    if (!companyValue) return null;
    
    // Check if the values are different
    const isDifferent = currentValue?.trim() !== '' && currentValue !== companyValue;
    
    // Handle when to apply company data
    const handleApplyCompanyValue = () => {
      setFormData({
        ...formData,
        [fieldName]: companyValue
      });
      toast.success(`Updated ${label.toLowerCase()} with company data`);
    };
    
    return (
      <div className="border rounded-lg p-3 bg-slate-50">
        <div className="flex justify-between items-center mb-2">
          <span className="text-xs text-gray-500 uppercase tracking-wide">{label}</span>
          {isDifferent && (
            <Button 
              type="button"
              variant="ghost" 
              size="sm" 
              onClick={handleApplyCompanyValue}
              className="h-6 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2"
            >
              Use company data
            </Button>
          )}
        </div>
        
        <div className={`text-sm ${isDifferent ? 'flex justify-between gap-2' : ''}`}>
          {isDifferent ? (
            <>
              <div className="flex-1">
                <div className="text-xs text-gray-500 mb-1">Current:</div>
                <div className="border rounded p-1.5 bg-white">{currentValue}</div>
              </div>
              <div className="flex-1">
                <div className="text-xs text-gray-500 mb-1">Company Data:</div>
                <div className="border border-blue-200 rounded p-1.5 bg-blue-50 text-blue-700">
                  {companyValue}
                </div>
              </div>
            </>
          ) : (
            currentValue || companyValue
          )}
        </div>
      </div>
    );
  };

  // Update the handleCompanySelect function to use more comprehensive data
  const handleCompanySelect = (company: CompanySuggestion) => {
    setSelectedCompany(company);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    
    const extracted = company.extracted_data;
    
    // Store current form data values before auto-populating to compare later
    const previousFormData = { ...formData };
    
    // Auto-populate company-related fields with basic and extracted data
    const newFormData = {
      ...formData,
      company_name: company.company_name,
      company_website: company.company_website || '',
      industry: company.industry || '',
      company_address: company.company_address || (extracted?.headquarters || ''),
      company_city: company.company_city || '',
      company_state: company.company_state || '',
      company_country: company.company_country || ''
    };

    // Auto-populate investment criteria from extracted data
    if (extracted) {
      // Use extracted deal size data - consider more options including AUM
      if (extracted.dealsize) {
        newFormData.investment_criteria_deal_size = extracted.dealsize;
      } else if (extracted.minimumdealsize && extracted.maximumdealsize) {
        newFormData.investment_criteria_deal_size = `${extracted.minimumdealsize} - ${extracted.maximumdealsize}`;
      } else if (extracted.minimumdealsize) {
        newFormData.investment_criteria_deal_size = `${extracted.minimumdealsize}+`;
      } else if (extracted.aum) {
        // Consider AUM if dealsize isn't available
        newFormData.investment_criteria_deal_size = extracted.aum;
      }

      // Property type selection logic - prioritize investment_criteria_property_types but fall back to propertytypes
      const propertyTypes = extracted.investment_criteria_property_types?.length ? 
                            extracted.investment_criteria_property_types :
                            extracted.propertytypes;
                            
      if (propertyTypes && propertyTypes.length > 0) {
        newFormData.investment_criteria_property_type = propertyTypes.join(', ');
      }

      // Asset type selection logic - prioritize investment_criteria_asset_types but fall back to assetclasses
      const assetTypes = extracted.investment_criteria_asset_types?.length ? 
                         extracted.investment_criteria_asset_types :
                         extracted.assetclasses;
                         
      if (assetTypes && assetTypes.length > 0) {
        newFormData.investment_criteria_asset_type = assetTypes.join(', ');
      }

      // Loan types directly from investment_criteria_loan_types
      if (extracted.investment_criteria_loan_types && extracted.investment_criteria_loan_types.length > 0) {
        newFormData.investment_criteria_loan_type = extracted.investment_criteria_loan_types.join(', ');
      }

      // Geographic focus - combine targetmarkets and geographicfocus for more comprehensive data
      const geographicData = [
        ...(extracted.geographicfocus || []),
        ...(extracted.targetmarkets || []).filter(market => 
          !(extracted.geographicfocus || []).includes(market)
        )
      ];
      
      if (geographicData.length > 0) {
        // For investment criteria country
        newFormData.investment_criteria_country = geographicData.join(', ');
        
        // For region, use geographic focus if not already populated
        if (!formData.region.trim()) {
          // Convert specific countries to regions where appropriate
          const regions = new Set<string>();
          geographicData.forEach((location: string) => {
            // North America
            if (['United States', 'USA', 'US', 'America', 'Canada', 'Mexico'].some(country => 
              location.includes(country))) {
              regions.add('North America');
            } 
            // Europe
            else if (['UK', 'United Kingdom', 'France', 'Germany', 'Spain', 'Italy', 'Europe', 
                     'Netherlands', 'Belgium', 'Sweden', 'Norway', 'Finland'].some(country => 
              location.includes(country))) {
              regions.add('Europe');
            } 
            // Asia Pacific
            else if (['China', 'Japan', 'India', 'Singapore', 'Australia', 'Korea', 'Asia', 
                      'Hong Kong', 'Taiwan', 'Pacific'].some(country => 
              location.includes(country))) {
              regions.add('Asia Pacific');
            }
            // Middle East
            else if (['UAE', 'Saudi', 'Qatar', 'Dubai', 'Middle East', 'Israel'].some(country => 
              location.includes(country))) {
              regions.add('Middle East');
            }
            // Latin America
            else if (['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru', 'Latin America', 
                      'South America', 'Central America'].some(country => 
              location.includes(country))) {
              regions.add('Latin America');
            }
            // Africa
            else if (['Africa', 'Nigeria', 'Kenya', 'South Africa', 'Egypt'].some(country => 
              location.includes(country))) {
              regions.add('Africa');
            }
          });
          
          if (regions.size > 0) {
            newFormData.region = Array.from(regions).join(', ');
          }
        }
      }

      // Auto-populate capital type based on company type and investment approach
      if (extracted.companytype) {
        const companyType = extracted.companytype.toLowerCase();
        
        if (companyType.includes('debt') || companyType.includes('lending') || 
            companyType.includes('loan') || companyType.includes('credit')) {
          newFormData.capital_type = 'Debt';
        } else if (companyType.includes('equity') || companyType.includes('investment') || 
                  companyType.includes('venture') || companyType.includes('private equity')) {
          newFormData.capital_type = 'Equity';
        } else if (companyType.includes('mezzanine')) {
          newFormData.capital_type = 'Mezzanine';
        } else if (extracted.approach) {
          // Fall back to investment approach if company type doesn't provide clear indication
          const approach = extracted.approach.toLowerCase();
          if (approach.includes('debt') || approach.includes('lending')) {
            newFormData.capital_type = 'Debt';
          } else if (approach.includes('equity')) {
            newFormData.capital_type = 'Equity';
          } else if (approach.includes('mezzanine')) {
            newFormData.capital_type = 'Mezzanine';
          }
        }
      }
      
      // Determine appropriate contact category based on extracted data
      if (extracted.companytype) {
        const type = extracted.companytype.toLowerCase();
        if (type.includes('investor') || type.includes('investment') || type.includes('fund') || 
            type.includes('capital') || type.includes('asset manager') || type.includes('venture') ||
            type.includes('private equity')) {
          newFormData.contact_category = 'investor';
        } else if (type.includes('developer') || type.includes('operator') || 
                  type.includes('owner') || type.includes('property manager') ||
                  type.includes('reit') || type.includes('construction')) {
          newFormData.contact_category = 'sponsor';
        } else if (type.includes('broker') || type.includes('advisor') || 
                  type.includes('consultant') || type.includes('service provider') ||
                  type.includes('law') || type.includes('lawyer') || type.includes('attorney') ||
                  type.includes('accountant') || type.includes('bank')) {
          newFormData.contact_category = 'third party';
        }
      }
    }
    
    // Keep track of which fields had conflicting data for showing suggestions
    const suggestions: SuggestionData = {};
    
    // If user had already manually entered data that differs from extracted data,
    // preserve their input but store the extracted data as suggestions
    Object.entries(newFormData).forEach(([key, value]) => {
      // Only check relevant investment criteria fields
      const isInvestmentField = key.startsWith('investment_criteria_') || 
                               key === 'capital_type' || 
                               key === 'region';
      
      if (
        isInvestmentField &&
        typeof previousFormData[key as keyof typeof previousFormData] === 'string' && 
        typeof value === 'string' &&
        previousFormData[key as keyof typeof previousFormData]?.trim() !== '' && 
        previousFormData[key as keyof typeof previousFormData] !== value
      ) {
        // Keep user's input
        newFormData[key as keyof typeof newFormData] = previousFormData[key as keyof typeof previousFormData];
        
        // Store the extracted value as a suggestion
        suggestions[key] = value;
      }
    });

    setFormData(newFormData);
    
    // If there are suggestions, show them to the user
    if (Object.keys(suggestions).length > 0) {
      // Display suggestions as toast
      toast.info(
        <div>
          <p className="font-medium">Company data differs from your entries</p>
          <ul className="mt-2 text-sm">
            {Object.entries(suggestions).map(([key, value]) => (
              <li key={key} className="mb-1">
                <span className="font-medium">{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {value}
              </li>
            ))}
          </ul>
          <p className="mt-2 text-xs">Your entries have been preserved. You can update if needed.</p>
        </div>,
        { duration: 8000 }
      );
    } else {
      // Clear any validation errors for auto-populated fields
      const newErrors = { ...validationErrors };
      delete newErrors.company_name;
      if (company.company_website || extracted?.headquarters) delete newErrors.company_website;
      setValidationErrors(newErrors);

      const extractedInfo = extracted ? ' with enhanced investment criteria' : '';
      toast.success(`Auto-populated company fields from ${company.company_name}${extractedInfo}`);
    }
  };

  // Keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || companySuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev < companySuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex((prev) => 
          prev > 0 ? prev - 1 : companySuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          handleCompanySelect(companySuggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      email: '',
      personal_email: '',
      title: '',
      linkedin_url: '',
      phone_number: '',
      company_name: '',
      company_website: '',
      industry: '',
      company_address: '',
      company_city: '',
      company_state: '',
      company_country: '',
      contact_city: '',
      contact_state: '',
      contact_country: '',
      region: '',
      capital_type: '',
      contact_category: '',
      investment_criteria_country: '',
      investment_criteria_state: '',
      investment_criteria_city: '',
      investment_criteria_property_type: '',
      investment_criteria_asset_type: '',
      investment_criteria_loan_type: '',
      investment_criteria_deal_size: '',
      notes: ''
    });
    setSelectedCompany(null);
    setValidationErrors({});
    setShowSuggestions(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      const errorCount = Object.keys(validationErrors).length;
      const errorFields = Object.keys(validationErrors).join(', ');
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? 's' : ''} in: ${errorFields}`);
      
      // Scroll to first error field
      const firstErrorField = document.querySelector(`[name="${Object.keys(validationErrors)[0]}"]`) as HTMLElement;
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstErrorField.focus();
      }
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          // Ensure company gets created/linked properly
          company_id: selectedCompany?.company_id
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to add contact: ${errorData}`);
      }

      toast.success('Contact added successfully!');
      resetForm();
      onBack(); // Return to contacts list after successful addition
    } catch (error) {
      console.error('Error adding contact:', error);
      toast.error((error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={onBack} 
                className="hover:bg-slate-100 rounded-xl transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
                  Add New Contact
                </h1>
                <p className="text-slate-600 mt-1">Create a comprehensive contact profile with company insights</p>
              </div>
            </div>
            <Button 
              type="button" 
              variant="outline" 
              onClick={resetForm}
              className="hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-all duration-200 rounded-xl"
            >
              <X className="h-4 w-4 mr-2" />
              Reset Form
            </Button>
          </div>
        </div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid gap-8 transition-all duration-300 grid-cols-1 lg:grid-cols-2">
          {/* Left Column: Contact Information (Editable) */}
          <div className="space-y-6">

        {/* Personal Information Card */}
        <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-xl mr-3">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">Personal Information</div>
                <div className="text-sm text-slate-600">(Required fields marked with *)</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="first_name" className="text-sm font-medium text-slate-700">
                  First Name <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="first_name" 
                  name="first_name" 
                  value={formData.first_name} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.first_name 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="Enter first name"
                  required
                />
                {validationErrors.first_name && (
                  <p className="text-sm text-red-500">{validationErrors.first_name}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name" className="text-sm font-medium text-slate-700">
                  Last Name <span className="text-red-500">*</span>
                </Label>
                <Input 
                  id="last_name" 
                  name="last_name" 
                  value={formData.last_name} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.last_name 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="Enter last name"
                  required
                />
                {validationErrors.last_name && (
                  <p className="text-sm text-red-500">{validationErrors.last_name}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-slate-700">Business Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.email 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="<EMAIL> (Optional)"
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-500">{validationErrors.email}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="personal_email" className="text-sm font-medium text-slate-700">Personal Email</Label>
                <Input 
                  id="personal_email" 
                  name="personal_email" 
                  type="email" 
                  value={formData.personal_email} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.personal_email 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="<EMAIL> (Optional)"
                />
                {validationErrors.personal_email && (
                  <p className="text-sm text-red-500">{validationErrors.personal_email}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium text-slate-700">Job Title</Label>
                <Input 
                  id="title" 
                  name="title" 
                  value={formData.title} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.title 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., Managing Director, Investment Associate (Optional)"
                />
                {validationErrors.title && (
                  <p className="text-sm text-red-500">{validationErrors.title}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone_number" className="text-sm font-medium text-slate-700">Phone Number</Label>
                <Input 
                  id="phone_number" 
                  name="phone_number" 
                  value={formData.phone_number} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                    validationErrors.phone_number 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., +**************** (Optional)"
                />
                {validationErrors.phone_number && (
                  <p className="text-sm text-red-500">{validationErrors.phone_number}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="linkedin_url" className="text-sm font-medium text-slate-700">LinkedIn Profile</Label>
              <Input 
                id="linkedin_url" 
                name="linkedin_url" 
                value={formData.linkedin_url} 
                onChange={handleChange}
                className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-blue-100 ${
                  validationErrors.linkedin_url 
                    ? 'border-red-300 focus:border-red-500' 
                    : 'border-slate-200 focus:border-blue-500 hover:border-slate-300'
                }`}
                placeholder="https://linkedin.com/in/username (Optional)"
              />
              {validationErrors.linkedin_url && (
                <p className="text-sm text-red-500">{validationErrors.linkedin_url}</p>
              )}
            </div>
          </CardContent>
        </Card>
        
        {/* Contact Location Card */}
        <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-xl mr-3">
                <MapPin className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">Contact Location</div>
                <div className="text-sm text-slate-600">Where this contact is located</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="contact_city" className="text-sm font-medium text-slate-700">City</Label>
                <Input 
                  id="contact_city" 
                  name="contact_city" 
                  value={formData.contact_city} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-purple-100 ${
                    validationErrors.contact_city 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-purple-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., New York, London, Tokyo (Optional)"
                />
                {validationErrors.contact_city && (
                  <p className="text-sm text-red-500">{validationErrors.contact_city}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact_state" className="text-sm font-medium text-slate-700">State/Province</Label>
                <Input 
                  id="contact_state" 
                  name="contact_state" 
                  value={formData.contact_state} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-purple-100 ${
                    validationErrors.contact_state 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-purple-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., New York, California, Ontario (Optional)"
                />
                {validationErrors.contact_state && (
                  <p className="text-sm text-red-500">{validationErrors.contact_state}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="contact_country" className="text-sm font-medium text-slate-700">Country</Label>
                <Input 
                  id="contact_country" 
                  name="contact_country" 
                  value={formData.contact_country} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-purple-100 ${
                    validationErrors.contact_country 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-purple-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., United States, Canada, United Kingdom (Optional)"
                />
                {validationErrors.contact_country && (
                  <p className="text-sm text-red-500">{validationErrors.contact_country}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="region" className="text-sm font-medium text-slate-700">Region</Label>
                <Input 
                  id="region" 
                  name="region" 
                  value={formData.region} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-purple-100 ${
                    validationErrors.region 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-purple-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., North America, Europe, Asia Pacific (Optional)"
                />
                {validationErrors.region && (
                  <p className="text-sm text-red-500">{validationErrors.region}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Categories Card */}
        <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 border-b border-orange-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-xl mr-3">
                <DollarSign className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">Categories</div>
                <div className="text-sm text-slate-600">Investment type and contact classification</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="capital_type" className="text-sm font-medium text-slate-700">Capital Type</Label>
                <Input 
                  id="capital_type" 
                  name="capital_type" 
                  value={formData.capital_type} 
                  onChange={handleChange}
                  className="h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-orange-100 border-slate-200 focus:border-orange-500 hover:border-slate-300"
                  placeholder="e.g., Debt, Equity, Mezzanine (Optional)"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact_category" className="text-sm font-medium text-slate-700">Contact Category</Label>
                <Select 
                  value={formData.contact_category} 
                  onValueChange={(value) => handleSelectChange('contact_category', value)}
                >
                  <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-orange-500 hover:border-slate-300 focus:ring-4 focus:ring-orange-100">
                    <SelectValue placeholder="Select category (Optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {contactCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Investment Criteria (Editable) */}
        <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-green-100 rounded-xl mr-3">
                <Target className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">Contact Investment Criteria</div>
                <div className="text-sm text-slate-600">Specific investment preferences for this contact (may differ from company-wide criteria)</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_country" className="text-sm font-medium text-slate-700">Country</Label>
                <Input 
                  id="investment_criteria_country" 
                  name="investment_criteria_country" 
                  value={formData.investment_criteria_country} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_country 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., United States, Canada (Optional)"
                />
                {validationErrors.investment_criteria_country && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_country}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_state" className="text-sm font-medium text-slate-700">State</Label>
                <Input 
                  id="investment_criteria_state" 
                  name="investment_criteria_state" 
                  value={formData.investment_criteria_state} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_state 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., California, Texas (Optional)"
                />
                {validationErrors.investment_criteria_state && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_state}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_city" className="text-sm font-medium text-slate-700">City</Label>
                <Input 
                  id="investment_criteria_city" 
                  name="investment_criteria_city" 
                  value={formData.investment_criteria_city} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_city 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., Los Angeles, Austin (Optional)"
                />
                {validationErrors.investment_criteria_city && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_city}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_property_type" className="text-sm font-medium text-slate-700">Property Type</Label>
                <Input 
                  id="investment_criteria_property_type" 
                  name="investment_criteria_property_type" 
                  value={formData.investment_criteria_property_type} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_property_type 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., Multifamily, Office, Retail (Optional)"
                />
                {validationErrors.investment_criteria_property_type && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_property_type}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_asset_type" className="text-sm font-medium text-slate-700">Asset Type</Label>
                <Input 
                  id="investment_criteria_asset_type" 
                  name="investment_criteria_asset_type" 
                  value={formData.investment_criteria_asset_type} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_asset_type 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., Core, Value-Add, Opportunistic (Optional)"
                />
                {validationErrors.investment_criteria_asset_type && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_asset_type}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_loan_type" className="text-sm font-medium text-slate-700">Loan Type</Label>
                <Input 
                  id="investment_criteria_loan_type" 
                  name="investment_criteria_loan_type" 
                  value={formData.investment_criteria_loan_type} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_loan_type 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., Bridge, Permanent, Construction (Optional)"
                />
                {validationErrors.investment_criteria_loan_type && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_loan_type}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="investment_criteria_deal_size" className="text-sm font-medium text-slate-700">Deal Size</Label>
                <Input 
                  id="investment_criteria_deal_size" 
                  name="investment_criteria_deal_size" 
                  value={formData.investment_criteria_deal_size} 
                  onChange={handleChange}
                  className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 ${
                    validationErrors.investment_criteria_deal_size 
                      ? 'border-red-300 focus:border-red-500' 
                      : 'border-slate-200 focus:border-green-500 hover:border-slate-300'
                  }`}
                  placeholder="e.g., $1M - $10M (Optional)"
                />
                {validationErrors.investment_criteria_deal_size && (
                  <p className="text-sm text-red-500">{validationErrors.investment_criteria_deal_size}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Notes Card */}
        <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 border-b border-slate-100">
            <CardTitle className="flex items-center">
              <div className="p-2 bg-slate-100 rounded-xl mr-3">
                <Calendar className="h-5 w-5 text-slate-600" />
              </div>
              <div>
                <div className="text-lg font-semibold text-slate-900">Notes</div>
                <div className="text-sm text-slate-600">Additional information about this contact</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 bg-gradient-to-b from-white to-slate-50">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="notes" className="text-sm font-medium text-slate-700">Additional Notes</Label>
                <span className="text-xs text-slate-500">
                  {formData.notes.length}/1000 characters
                </span>
              </div>
              <Textarea 
                id="notes" 
                name="notes" 
                value={formData.notes} 
                onChange={handleChange}
                rows={4}
                className={`resize-none rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-slate-100 ${
                  validationErrors.notes 
                    ? 'border-red-300 focus:border-red-500' 
                    : 'border-slate-200 focus:border-slate-500 hover:border-slate-300'
                }`}
                placeholder="Add any additional notes about this contact, their investment preferences, communication style, or other relevant information..."
              />
              {validationErrors.notes && (
                <p className="text-sm text-red-500">{validationErrors.notes}</p>
              )}
            </div>
          </CardContent>
        </Card>
        
        {/* Validation Summary */}
        {Object.keys(validationErrors).length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-2xl p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <X className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Please fix the following validation errors:
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc list-inside space-y-1">
                    {Object.entries(validationErrors).map(([field, error]) => (
                      <li key={field}>
                        <span className="font-medium">{field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</span> {error}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2 text-sm text-slate-600">
              {Object.keys(validationErrors).length === 0 ? (
                <>
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>{selectedCompany ? 'Company insights available - Ready to save' : 'Form validated - Ready to save contact'}</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span>{Object.keys(validationErrors).length} validation error{Object.keys(validationErrors).length > 1 ? 's' : ''} found</span>
                </>
              )}
            </div>
            <div className="flex space-x-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onBack}
                className="h-12 px-6 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || Object.keys(validationErrors).length > 0}
                className="h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 min-w-[140px] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Add Contact
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
        
        </div>

        {/* Right Column: Company Selection and Information */}
        <div className="space-y-6">
          {/* Company Selection Card */}
          <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-100">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="p-2 bg-emerald-100 rounded-xl mr-3">
                    <Building2 className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-slate-900">Company Selection</div>
                    <div className="text-sm text-slate-600">Search and select the company</div>
                  </div>
                </div>
                {selectedCompany && (
                  <div className="flex items-center bg-green-100 px-3 py-1 rounded-full">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
                    <span className="text-xs font-medium text-green-700">Selected</span>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
              <div className="space-y-2 relative">
                <Label htmlFor="company_name" className="text-sm font-medium text-slate-700">
                  Company Name <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input 
                    ref={companyInputRef}
                    id="company_name" 
                    name="company_name" 
                    value={formData.company_name} 
                    onChange={handleChange}
                    onKeyDown={handleKeyDown}
                    placeholder="Start typing company name..."
                    className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 ${
                      validationErrors.company_name 
                        ? 'border-red-300 focus:border-red-500' 
                        : 'border-slate-200 focus:border-emerald-500 hover:border-slate-300'
                    }`}
                    required
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {isSearching ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    ) : (
                      <Search className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>
                
                {validationErrors.company_name && (
                  <p className="text-sm text-red-500">{validationErrors.company_name}</p>
                )}
              </div>

              {/* Company Search Results - Show directly below search input */}
              {showSuggestions && companySuggestions.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-slate-700">Search Results ({companySuggestions.length} found)</Label>
                  <div className="space-y-2 max-h-80 overflow-y-auto border rounded-xl p-2 bg-slate-50" data-search-results>
                    {companySuggestions.map((company, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                          index === selectedSuggestionIndex 
                            ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                            : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                        }`}
                        onClick={() => handleCompanySelect(company)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 text-sm">{company.company_name}</div>
                            {company.extracted_data && (
                              <div className="text-xs text-emerald-600 font-medium mt-1 flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Enhanced Data
                              </div>
                            )}
                            {(company.industry || company.extracted_data?.companytype) && (
                              <div className="text-xs text-gray-600 mt-1">
                                {company.extracted_data?.companytype || company.industry}
                              </div>
                            )}
                            {(company.company_city && company.company_state) || company.extracted_data?.headquarters ? (
                              <div className="text-xs text-gray-500 mt-1 flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {company.extracted_data?.headquarters || `${company.company_city}, ${company.company_state}`}
                              </div>
                            ) : null}
                          </div>
                          <div className="ml-3">
                            {company.extracted_data?.fundsize && (
                              <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                {company.extracted_data.fundsize}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {!selectedCompany && (
                <>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company_website" className="text-sm font-medium text-slate-700">
                        Website <span className="text-red-500">*</span>
                      </Label>
                      <Input 
                        id="company_website" 
                        name="company_website" 
                        type="url"
                        value={formData.company_website} 
                        onChange={handleChange}
                        placeholder="https://example.com"
                        className={`h-12 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-emerald-100 ${
                          validationErrors.company_website 
                            ? 'border-red-300 focus:border-red-500' 
                            : 'border-slate-200 focus:border-emerald-500 hover:border-slate-300'
                        }`}
                        required
                      />
                      {validationErrors.company_website && (
                        <p className="text-sm text-red-500">{validationErrors.company_website}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="industry" className="text-sm font-medium text-slate-700">Industry</Label>
                      <Select 
                        value={formData.industry} 
                        onValueChange={(value) => handleSelectChange('industry', value)}
                      >
                        <SelectTrigger className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300">
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="real_estate">Real Estate</SelectItem>
                          <SelectItem value="finance">Finance</SelectItem>
                          <SelectItem value="technology">Technology</SelectItem>
                          <SelectItem value="banking">Banking</SelectItem>
                          <SelectItem value="insurance">Insurance</SelectItem>
                          <SelectItem value="investment">Investment</SelectItem>
                          <SelectItem value="construction">Construction</SelectItem>
                          <SelectItem value="property_management">Property Management</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="company_address" className="text-sm font-medium text-slate-700">Company Address</Label>
                    <Input 
                      id="company_address" 
                      name="company_address" 
                      value={formData.company_address} 
                      onChange={handleChange}
                      className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                      placeholder="Enter company address"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company_city" className="text-sm font-medium text-slate-700">City</Label>
                      <Input 
                        id="company_city" 
                        name="company_city" 
                        value={formData.company_city} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                        placeholder="City"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company_state" className="text-sm font-medium text-slate-700">State/Province</Label>
                      <Input 
                        id="company_state" 
                        name="company_state" 
                        value={formData.company_state} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                        placeholder="State/Province"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="company_country" className="text-sm font-medium text-slate-700">Country</Label>
                      <Input 
                        id="company_country" 
                        name="company_country" 
                        value={formData.company_country} 
                        onChange={handleChange}
                        className="h-12 rounded-xl border-2 border-slate-200 focus:border-emerald-500 hover:border-slate-300 focus:ring-4 focus:ring-emerald-100"
                        placeholder="Country"
                      />
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>



          {/* Company Profile - Show when company is selected */}
          {selectedCompany && (
            <Card className="sticky top-4">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2 text-blue-600" />
                    Company Profile
                  </div>
                  <Link 
                    href={`/dashboard/companies/${selectedCompany.company_id}`} 
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                  >
                    View Details
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Company Basic Info */}
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900">{selectedCompany.company_name}</h3>
                      {selectedCompany.company_website && (
                        <a 
                          href={selectedCompany.company_website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline flex items-center mt-1"
                        >
                          {selectedCompany.company_website}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                      )}
                    </div>
                  </div>

                  {(selectedCompany.industry || selectedCompany.extracted_data?.companytype) && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Target className="h-4 w-4 mr-2" />
                      {selectedCompany.extracted_data?.companytype || selectedCompany.industry}
                    </div>
                  )}

                  {((selectedCompany.company_city && selectedCompany.company_state) || selectedCompany.extracted_data?.headquarters) && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {selectedCompany.extracted_data?.headquarters || `${selectedCompany.company_city}, ${selectedCompany.company_state}`}
                    </div>
                  )}
                </div>

                {/* Enhanced Company Data */}
                {selectedCompany.extracted_data && (
                  <>
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <DollarSign className="h-4 w-4 mr-2" />
                        Financial Information
                      </h4>
                      <div className="grid grid-cols-1 gap-3">
                        {(selectedCompany.extracted_data.fundsize || selectedCompany.extracted_data.aum) && (
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">{selectedCompany.extracted_data.fundsize ? 'Fund Size:' : 'AUM:'}</span>
                            <Badge variant="secondary">{selectedCompany.extracted_data.fundsize || selectedCompany.extracted_data.aum}</Badge>
                          </div>
                        )}
                        {selectedCompany.extracted_data.foundedyear && (
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Founded:</span>
                            <span className="text-sm font-medium">{selectedCompany.extracted_data.foundedyear}</span>
                          </div>
                        )}
                        {selectedCompany.extracted_data.numberofemployees && (
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Employees:</span>
                            <span className="text-sm font-medium">{selectedCompany.extracted_data.numberofemployees}</span>
                          </div>
                        )}
                        {selectedCompany.extracted_data.targetreturn && (
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Target Return:</span>
                            <span className="text-sm font-medium">{selectedCompany.extracted_data.targetreturn}</span>
                          </div>
                        )}
                        {selectedCompany.extracted_data.holdperiod && (
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Hold Period:</span>
                            <span className="text-sm font-medium">{selectedCompany.extracted_data.holdperiod}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Investment Criteria */}
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <Target className="h-4 w-4 mr-2" />
                        Investment Focus
                      </h4>
                      <div className="space-y-3">
                        {/* Geographic Focus - combine geographicfocus and targetmarkets */}
                        {((selectedCompany.extracted_data?.geographicfocus?.length || 0) > 0 || 
                          (selectedCompany.extracted_data?.targetmarkets?.length || 0) > 0) && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Geographic Focus</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {[
                                ...(selectedCompany.extracted_data?.geographicfocus || []),
                                ...(selectedCompany.extracted_data?.targetmarkets || []).filter(market => 
                                  !(selectedCompany.extracted_data?.geographicfocus || []).includes(market)
                                )
                              ].map((region: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{region}</Badge>
                              ))}
                            </div>
                          </div>
                        )}
                        
                        {/* Property Types - prefer investment_criteria_property_types but fall back to propertytypes */}
                        {((selectedCompany.extracted_data?.investment_criteria_property_types?.length || 0) > 0 || 
                          (selectedCompany.extracted_data?.propertytypes?.length || 0) > 0) && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Property Types</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {((selectedCompany.extracted_data?.investment_criteria_property_types?.length || 0) > 0 ? 
                                selectedCompany.extracted_data?.investment_criteria_property_types : 
                                selectedCompany.extracted_data?.propertytypes
                              )?.map((type: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Asset Types - prefer investment_criteria_asset_types but fall back to assetclasses */}
                        {((selectedCompany.extracted_data?.investment_criteria_asset_types?.length || 0) > 0 || 
                          (selectedCompany.extracted_data?.assetclasses?.length || 0) > 0) && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Asset Types</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {((selectedCompany.extracted_data?.investment_criteria_asset_types?.length || 0) > 0 ? 
                                selectedCompany.extracted_data?.investment_criteria_asset_types : 
                                selectedCompany.extracted_data?.assetclasses
                              )?.map((type: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Loan Types */}
                        {(selectedCompany.extracted_data?.investment_criteria_loan_types?.length || 0) > 0 && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Loan Types</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedCompany.extracted_data.investment_criteria_loan_types?.map((type: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{type}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Deal Size */}
                        {(selectedCompany.extracted_data.dealsize || 
                          selectedCompany.extracted_data.minimumdealsize ||
                          selectedCompany.extracted_data.aum) && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Deal Size Range</span>
                            <div className="mt-1">
                              <Badge variant="secondary" className="text-xs">
                                {selectedCompany.extracted_data.dealsize || 
                                 (selectedCompany.extracted_data.minimumdealsize && selectedCompany.extracted_data.maximumdealsize 
                                   ? `${selectedCompany.extracted_data.minimumdealsize} - ${selectedCompany.extracted_data.maximumdealsize}`
                                   : selectedCompany.extracted_data.minimumdealsize 
                                     ? `${selectedCompany.extracted_data.minimumdealsize}+`
                                     : selectedCompany.extracted_data.aum)}
                              </Badge>
                            </div>
                          </div>
                        )}

                        {/* Investment Strategies */}
                        {(selectedCompany.extracted_data?.strategies?.length || 0) > 0 && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Investment Strategies</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedCompany.extracted_data?.strategies?.map((strategy: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{strategy}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Value Creation */}
                        {(selectedCompany.extracted_data?.valuecreation?.length || 0) > 0 && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Value Creation Approach</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {selectedCompany.extracted_data?.valuecreation?.map((approach: string, idx: number) => (
                                <Badge key={idx} variant="outline" className="text-xs">{approach}</Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Risk Profile */}
                        {selectedCompany.extracted_data.riskprofile && (
                          <div>
                            <span className="text-xs text-gray-500 uppercase tracking-wide">Risk Profile</span>
                            <div className="mt-1">
                              <Badge variant="outline" className="text-xs">{selectedCompany.extracted_data.riskprofile}</Badge>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Investment Criteria Comparison - Use the renderCriteriaComparison helper */}
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                        <Target className="h-4 w-4 mr-2" />
                        Contact Investment Criteria
                      </h4>
                      <div className="space-y-3">
                        {/* Geographic Focus */}
                        {renderCriteriaComparison('Geographic Focus', 
                          [
                            ...(selectedCompany.extracted_data.geographicfocus || []),
                            ...(selectedCompany.extracted_data.targetmarkets || []).filter(market => 
                              !(selectedCompany.extracted_data?.geographicfocus || []).includes(market)
                            )
                          ].join(', '), 
                          formData.investment_criteria_country,
                          'investment_criteria_country')}
                        
                        {/* Region Comparison */}
                        {renderCriteriaComparison('Region', 
                          determineRegionsFromLocations([
                            ...(selectedCompany.extracted_data.geographicfocus || []),
                            ...(selectedCompany.extracted_data.targetmarkets || [])
                          ]),
                          formData.region,
                          'region')}
                        
                        {/* Deal Size */}
                        {renderCriteriaComparison('Deal Size', 
                          selectedCompany.extracted_data.dealsize || 
                          (selectedCompany.extracted_data.minimumdealsize && selectedCompany.extracted_data.maximumdealsize 
                            ? `${selectedCompany.extracted_data.minimumdealsize} - ${selectedCompany.extracted_data.maximumdealsize}`
                            : selectedCompany.extracted_data.minimumdealsize 
                              ? `${selectedCompany.extracted_data.minimumdealsize}+`
                              : selectedCompany.extracted_data.aum || ''), 
                          formData.investment_criteria_deal_size,
                          'investment_criteria_deal_size')}
                        
                        {/* Property Types */}
                        {renderCriteriaComparison('Property Types', 
                          (() => {
                            // Use a function to safely handle undefined checks
                            const propertyTypes = selectedCompany.extracted_data?.investment_criteria_property_types;
                            const fallbackTypes = selectedCompany.extracted_data?.propertytypes;
                            
                            if (propertyTypes && propertyTypes.length > 0) {
                              return propertyTypes.join(', ');
                            } else if (fallbackTypes && fallbackTypes.length > 0) {
                              return fallbackTypes.join(', ');
                            }
                            return '';
                          })(), 
                          formData.investment_criteria_property_type,
                          'investment_criteria_property_type')}
                        
                        {/* Asset Types */}
                        {renderCriteriaComparison('Asset Types', 
                          (() => {
                            // Use a function to safely handle undefined checks
                            const assetTypes = selectedCompany.extracted_data?.investment_criteria_asset_types;
                            const fallbackTypes = selectedCompany.extracted_data?.assetclasses;
                            
                            if (assetTypes && assetTypes.length > 0) {
                              return assetTypes.join(', ');
                            } else if (fallbackTypes && fallbackTypes.length > 0) {
                              return fallbackTypes.join(', ');
                            }
                            return '';
                          })(), 
                          formData.investment_criteria_asset_type,
                          'investment_criteria_asset_type')}
                        
                        {/* Loan Types */}
                        {renderCriteriaComparison('Loan Types', 
                          (selectedCompany.extracted_data.investment_criteria_loan_types || []).join(', '), 
                          formData.investment_criteria_loan_type,
                          'investment_criteria_loan_type')}
                        
                        {/* Capital Type */}
                        {selectedCompany.extracted_data.companytype && (
                          renderCriteriaComparison('Capital Type', 
                            determineCapitalType(selectedCompany.extracted_data.companytype), 
                            formData.capital_type,
                            'capital_type')
                        )}
                        
                        {/* Contact Category */}
                        {selectedCompany.extracted_data.companytype && (
                          renderCriteriaComparison('Contact Category', 
                            determineContactCategory(selectedCompany.extracted_data.companytype), 
                            formData.contact_category,
                            'contact_category')
                        )}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </div>
        </div>
      </form>
      </div>
    </div>
  );
};

export default AddContact;

// Helper function to determine regions from location data
const determineRegionsFromLocations = (locations: string[]): string => {
  if (!locations || locations.length === 0) return '';
  
  const regions = new Set<string>();
  
  locations.forEach((location: string) => {
    // North America
    if (['United States', 'USA', 'US', 'America', 'Canada', 'Mexico'].some(country => 
      location.includes(country))) {
      regions.add('North America');
    } 
    // Europe
    else if (['UK', 'United Kingdom', 'France', 'Germany', 'Spain', 'Italy', 'Europe', 
            'Netherlands', 'Belgium', 'Sweden', 'Norway', 'Finland'].some(country => 
      location.includes(country))) {
      regions.add('Europe');
    } 
    // Asia Pacific
    else if (['China', 'Japan', 'India', 'Singapore', 'Australia', 'Korea', 'Asia', 
              'Hong Kong', 'Taiwan', 'Pacific'].some(country => 
      location.includes(country))) {
      regions.add('Asia Pacific');
    }
    // Middle East
    else if (['UAE', 'Saudi', 'Qatar', 'Dubai', 'Middle East', 'Israel'].some(country => 
      location.includes(country))) {
      regions.add('Middle East');
    }
    // Latin America
    else if (['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru', 'Latin America', 
              'South America', 'Central America'].some(country => 
      location.includes(country))) {
      regions.add('Latin America');
    }
    // Africa
    else if (['Africa', 'Nigeria', 'Kenya', 'South Africa', 'Egypt'].some(country => 
      location.includes(country))) {
      regions.add('Africa');
    }
  });
  
  return Array.from(regions).join(', ');
};

// Helper function to determine contact category
const determineContactCategory = (companyType: string): string => {
  const type = companyType.toLowerCase();
  if (type.includes('investor') || type.includes('investment') || type.includes('fund') || 
      type.includes('capital') || type.includes('asset manager') || type.includes('venture') ||
      type.includes('private equity')) {
    return 'investor';
  } else if (type.includes('developer') || type.includes('operator') || 
            type.includes('owner') || type.includes('property manager') ||
            type.includes('reit') || type.includes('construction')) {
    return 'sponsor';
  } else if (type.includes('broker') || type.includes('advisor') || 
            type.includes('consultant') || type.includes('service provider') ||
            type.includes('law') || type.includes('lawyer') || type.includes('attorney') ||
            type.includes('accountant') || type.includes('bank')) {
    return 'third party';
  }
  return '';
};