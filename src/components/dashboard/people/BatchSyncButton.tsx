import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Send, RefreshCw } from 'lucide-react';
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Campaign {
  id: string;
  name: string;
}

interface BatchSyncButtonProps {
  campaignId?: string;
  contactIds?: number[];
  onSyncComplete?: () => void;
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

export default function BatchSyncButton({
  campaignId: propCampaignId,
  contactIds,
  onSyncComplete,
  className = "",
  variant = "default",
  size = "sm"
}: BatchSyncButtonProps) {
  const [isSyncing, setIsSyncing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>(propCampaignId || '');
  const [showCampaignSelect, setShowCampaignSelect] = useState(false);

  // Fetch campaigns when component mounts or when selecting campaign
  useEffect(() => {
    if (showCampaignSelect) {
      fetchCampaigns();
    }
  }, [showCampaignSelect]);

  const fetchCampaigns = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/smartlead/campaigns');
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns');
      }
      
      const data = await response.json();
      if (data && data.campaigns) {
        setCampaigns(data.campaigns);
        
        // If campaignId is provided in props, select it
        if (propCampaignId) {
          setSelectedCampaignId(propCampaignId);
        } else if (data.campaigns.length > 0) {
          // Auto-select the first campaign if none is provided
          setSelectedCampaignId(data.campaigns[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      toast.error('Failed to fetch Smartlead campaigns');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchSync = async () => {
    if (!selectedCampaignId) {
      toast.error("Please select a campaign first");
      return;
    }

    try {
      setIsSyncing(true);
      
      // Create payload with campaign ID and optional contact IDs
      const payload: any = {
        campaignId: selectedCampaignId,
      };
      
      // If contactIds are provided, include them in the request
      if (contactIds && contactIds.length > 0) {
        payload.contactIds = contactIds;
      }
      
      // Use the batch-sync endpoint with payload
      const response = await fetch(`/api/smartlead/contacts/batch-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed with status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Successfully synced ${result.syncedCount} contacts to Smartlead`);
        // Hide campaign select after successful sync
        setShowCampaignSelect(false);
        // Call the completion callback if provided
        if (onSyncComplete) {
          onSyncComplete();
        }
      } else {
        toast.error(result.message || 'Failed to sync contacts to Smartlead');
      }
    } catch (error) {
      console.error('Error syncing contacts:', error);
      toast.error(`Failed to sync contacts: ${(error as Error).message}`);
    } finally {
      setIsSyncing(false);
    }
  };

  // Determine button label based on whether contactIds are provided
  const getButtonLabel = () => {
    if (contactIds && contactIds.length > 0) {
      return `Sync ${contactIds.length} Contacts`;
    }
    return 'Sync Campaign';
  };

  if (!showCampaignSelect) {
    return (
      <Button 
        onClick={() => setShowCampaignSelect(true)} 
        variant={variant}
        size={size}
        className={`flex items-center gap-1 ${className}`}
      >
        <Send className="h-3.5 w-3.5" />
        {contactIds && contactIds.length > 0 
          ? `Sync to Smartlead (${contactIds.length})` 
          : 'Sync to Smartlead'}
      </Button>
    );
  }

  return (
    <div className="flex flex-col space-y-3">
      <div className="flex items-center space-x-2">
        <Select
          value={selectedCampaignId}
          onValueChange={setSelectedCampaignId}
          disabled={isLoading}
        >
          <SelectTrigger className="w-[240px]">
            <SelectValue placeholder={isLoading ? "Loading campaigns..." : "Select a campaign"} />
          </SelectTrigger>
          <SelectContent>
            {campaigns.map((campaign) => (
              <SelectItem key={campaign.id} value={campaign.id}>
                {campaign.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button 
          onClick={handleBatchSync} 
          disabled={isSyncing || !selectedCampaignId}
          variant="default"
          size="default"
          className="flex items-center gap-1"
        >
          {isSyncing ? (
            <>
              <RefreshCw className="h-3.5 w-3.5 animate-spin" />
              Syncing...
            </>
          ) : (
            <>
              <Send className="h-3.5 w-3.5" />
              {getButtonLabel()}
            </>
          )}
        </Button>
        <Button 
          onClick={() => setShowCampaignSelect(false)}
          variant="ghost" 
          size="sm"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
} 