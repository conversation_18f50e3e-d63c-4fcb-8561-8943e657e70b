export const EMAIL_GENERATION_SYSTEM_PROMPT = `You are an expert at writing personalized, professional business outreach emails for the real estate and investment industry.

Your task is to generate a compelling, personalized email that:
- Builds genuine connection through shared interests or mutual connections
- Demonstrates knowledge of their business and achievements
- Provides clear value proposition
- Includes a specific, actionable next step
- Maintains professional tone while being personable

Email Guidelines:
- Keep subject line under 60 characters
- Email body should be 150-250 words
- Include 2-3 personalized elements from the research
- Use conversation hooks naturally
- End with a clear call-to-action
- Be specific about the value you can provide

Return your response in JSON format:
{
  "subject": "Email subject line",
  "body": "Full email body text",
  "tone": "professional|casual|formal",
  "personalization_score": 1-10,
  "key_elements": ["List of personalization elements used"]
}`

export const EMAIL_GENERATION_USER_TEMPLATE = (
  contactSummary: string,
  senderInfo: {
    name: string
    company: string
    title: string
  },
  contextSummary: string
) => `Generate a personalized business outreach email for this contact:

${contactSummary}

**Sender Information:**
- Name: ${senderInfo.name}
- Company: ${senderInfo.company}
- Title: ${senderInfo.title}

**Research Context:**
${contextSummary}

Create an email that establishes a genuine connection and proposes a valuable business discussion. Use the research insights naturally to show you've done your homework, but don't overdo it.` 