import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, TrendingUp, TrendingDown, Users, Mail, Eye, Reply, AlertTriangle, ExternalLink } from 'lucide-react';

interface CampaignStatsProps {
  campaignId?: string;
  onContactSelect?: (contactId: number) => void;
}

// Status color mapping
const statusColors: Record<string, string> = {
  SENT: '#9333ea',        // Purple
  DELIVERED: '#2563eb',   // Blue
  OPENED: '#16a34a',      // Green
  REPLIED: '#15803d',     // Dark Green
  BOUNCED: '#dc2626',     // Red
  UNSUBSCRIBED: '#9ca3af', // Gray
  STARTED: '#f59e0b',     // Amber
  INPROGRESS: '#3b82f6',  // Blue
  COMPLETED: '#10b981',   // Emerald
  BLOCKED: '#ef4444',     // Red
};

const leadStatusColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

export function CampaignStats({ campaignId, onContactSelect }: CampaignStatsProps) {
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>(campaignId || '');
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sequenceFilter, setSequenceFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Fetch available campaigns
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        const response = await fetch('/api/smartlead/campaigns');
        if (!response.ok) {
          throw new Error(`Failed to fetch campaigns: ${response.status}`);
        }
        const result = await response.json();
        if (result.campaigns) {
          setCampaigns(result.campaigns);
          // If no campaign is selected and we have campaigns, select the first one
          if (!selectedCampaignId && result.campaigns.length > 0) {
            setSelectedCampaignId(result.campaigns[0].id);
          }
        }
      } catch (err) {
        console.error('Error fetching campaigns:', err);
      }
    };

    fetchCampaigns();
  }, [selectedCampaignId]);

  // Fetch campaign stats
  useEffect(() => {
    if (!selectedCampaignId) return;

    const fetchCampaignStats = async () => {
      try {
        setIsLoading(true);
        
        // Build query parameters for filters
        const params = new URLSearchParams();
        if (sequenceFilter) params.append('email_sequence_number', sequenceFilter);
        if (statusFilter) params.append('email_status', statusFilter);
        
        const queryString = params.toString();
        const url = `/api/smartlead/campaigns/${selectedCampaignId}/stats${queryString ? `?${queryString}` : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch campaign stats: ${response.status}`);
        }
        const result = await response.json();
        // console.log(`Stats: ${JSON.stringify(result, null, 2)}`);
        setData(result);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchCampaignStats();
  }, [selectedCampaignId, sequenceFilter, statusFilter]);

  const refreshStats = () => {
    if (selectedCampaignId) {
      const fetchStats = async () => {
        try {
          setIsLoading(true);
          
          // Build query parameters for filters
          const params = new URLSearchParams();
          if (sequenceFilter) params.append('email_sequence_number', sequenceFilter);
          if (statusFilter) params.append('email_status', statusFilter);
          
          const queryString = params.toString();
          const url = `/api/smartlead/campaigns/${selectedCampaignId}/stats${queryString ? `?${queryString}` : ''}`;
          
          const response = await fetch(url);
          if (!response.ok) {
            throw new Error(`Failed to fetch campaign stats: ${response.status}`);
          }
          const result = await response.json();
          setData(result);
          setError(null);
        } catch (err) {
          setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        } finally {
          setIsLoading(false);
        }
      };
      fetchStats();
    }
  };

  if (!selectedCampaignId && campaigns.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">No campaigns available</div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-red-500">Error loading campaign stats: {error.message}</div>
        </CardContent>
      </Card>
    );
  }

  // Transform data for charts
  const emailStatsData = data?.emailStats ? [
    { name: 'Sent', value: data.emailStats.sent, fill: statusColors.SENT },
    { name: 'Delivered', value: data.emailStats.delivered, fill: statusColors.DELIVERED },
    { name: 'Opened', value: data.emailStats.opened, fill: statusColors.OPENED },
    { name: 'Replied', value: data.emailStats.replied, fill: statusColors.REPLIED },
    { name: 'Bounced', value: data.emailStats.bounced, fill: statusColors.BOUNCED },
    { name: 'Unsubscribed', value: data.emailStats.unsubscribed, fill: statusColors.UNSUBSCRIBED },
  ].filter(item => item.value > 0) : [];

  const leadStatusData = data?.leadStats?.statusDistribution ? 
    Object.entries(data.leadStats.statusDistribution)
      .filter(([_, count]) => (count as number) > 0)
      .map(([status, count], index) => ({
        name: status,
        value: count as number,
        fill: leadStatusColors[index % leadStatusColors.length]
      })) : [];

  const dailyActivityData = data?.dailyActivity ? 
    data.dailyActivity.map((item: any) => ({
      date: new Date(item.date).toLocaleDateString(),
      sent: parseInt(item.sent || item.count || 0) || 0,
      opened: parseInt(item.opened || item.opened_count || 0) || 0,
      replied: parseInt(item.replied || item.replied_count || 0) || 0,
      clicked: parseInt(item.clicked || item.clicked_count || 0) || 0,
    })) : [];

  return (
    <div className="space-y-6">
      {/* Campaign Selector */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Campaign Statistics</CardTitle>
              <CardDescription>
                Detailed analytics and performance metrics for your email campaigns
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Select value={selectedCampaignId} onValueChange={setSelectedCampaignId}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Select a campaign" />
                </SelectTrigger>
                <SelectContent>
                  {campaigns.map((campaign) => (
                    <SelectItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={refreshStats}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">Email Sequence:</label>
              <Select value={sequenceFilter || 'all'} onValueChange={(value) => setSequenceFilter(value === 'all' ? '' : value)}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sequences</SelectItem>
                  <SelectItem value="1">Sequence 1</SelectItem>
                  <SelectItem value="2">Sequence 2</SelectItem>
                  <SelectItem value="3">Sequence 3</SelectItem>
                  <SelectItem value="4">Sequence 4</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">Email Status:</label>
              <Select value={statusFilter || 'all'} onValueChange={(value) => setStatusFilter(value === 'all' ? '' : value)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="opened">Opened</SelectItem>
                  <SelectItem value="clicked">Clicked</SelectItem>
                  <SelectItem value="replied">Replied</SelectItem>
                  <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                  <SelectItem value="bounced">Bounced</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {(sequenceFilter || statusFilter) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSequenceFilter('');
                  setStatusFilter('');
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {data && (
        <>
          {/* Campaign Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{data.campaign?.name}</span>
                <Badge 
                  variant="outline"
                  className={`font-medium ${
                    data.campaign?.status === 'ACTIVE' ? 'bg-green-100 text-green-800 border-green-300' :
                    data.campaign?.status === 'COMPLETED' ? 'bg-blue-100 text-blue-800 border-blue-300' :
                    data.campaign?.status === 'PAUSED' ? 'bg-yellow-100 text-yellow-800 border-yellow-300' :
                    'bg-gray-100 text-gray-800 border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-1">
                    {data.campaign?.status === 'ACTIVE' && <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />}
                    {data.campaign?.status === 'COMPLETED' && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                    {data.campaign?.status === 'PAUSED' && <div className="w-2 h-2 bg-yellow-500 rounded-full" />}
                    <span>{data.campaign?.status}</span>
                  </div>
                </Badge>
              </CardTitle>
              <CardDescription>
                <div className="flex items-center gap-4 text-sm">
                  <span>Campaign ID: {data.campaign?.id}</span>
                  <span>•</span>
                  <span>Created: {new Date(data.campaign?.created_at).toLocaleDateString()}</span>
                  <span>•</span>
                  <span>Updated: {new Date(data.campaign?.updated_at).toLocaleDateString()}</span>
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 grid-cols-2 md:grid-cols-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <p className="text-sm font-medium text-blue-600">Total Leads</p>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">{Number(data.leadStats?.totalLeads) || 0}</p>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-5 w-5 text-purple-600" />
                    <p className="text-sm font-medium text-purple-600">Emails Sent</p>
                  </div>
                  <p className="text-2xl font-bold text-purple-900">{Number(data.emailStats?.sent) || 0}</p>
                </div>

                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-5 w-5 text-green-600" />
                    <p className="text-sm font-medium text-green-600">Open Rate</p>
                  </div>
                  <p className="text-2xl font-bold text-green-900">{Number(data.performanceMetrics?.openRate) || 0}%</p>
                </div>

                <div className="bg-emerald-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Reply className="h-5 w-5 text-emerald-600" />
                    <p className="text-sm font-medium text-emerald-600">Reply Rate</p>
                  </div>
                  <p className="text-2xl font-bold text-emerald-900">{Number(data.performanceMetrics?.replyRate) || 0}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Open Rate</span>
                    <div className="flex items-center space-x-1">
                      <span className="font-semibold">{Number(data.performanceMetrics?.openRate) || 0}%</span>
                      {(Number(data.performanceMetrics?.openRate) || 0) > 20 ? 
                        <TrendingUp className="h-4 w-4 text-green-500" /> : 
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Reply Rate</span>
                    <div className="flex items-center space-x-1">
                      <span className="font-semibold">{Number(data.performanceMetrics?.replyRate) || 0}%</span>
                      {(Number(data.performanceMetrics?.replyRate) || 0) > 5 ? 
                        <TrendingUp className="h-4 w-4 text-green-500" /> : 
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Bounce Rate</span>
                    <div className="flex items-center space-x-1">
                      <span className="font-semibold">{Number(data.performanceMetrics?.bounceRate) || 0}%</span>
                      {(Number(data.performanceMetrics?.bounceRate) || 0) < 5 ? 
                        <TrendingUp className="h-4 w-4 text-green-500" /> : 
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Email Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Email Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={emailStatsData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {emailStatsData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Lead Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Lead Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={leadStatusData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {leadStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Daily Activity Chart */}
          {dailyActivityData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Daily Email Activity (Last 30 Days)</CardTitle>
                <CardDescription>Email sending, opening, and reply trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={dailyActivityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="date" 
                        angle={-45} 
                        textAnchor="end" 
                        height={80} 
                        tick={{ fontSize: 10 }} 
                      />
                      <YAxis />
                      <Tooltip />
                      <Area 
                        type="monotone" 
                        dataKey="sent" 
                        stackId="1" 
                        stroke="#8884d8" 
                        fill="#8884d8" 
                        fillOpacity={0.6}
                        name="Sent"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="opened" 
                        stackId="2" 
                        stroke="#82ca9d" 
                        fill="#82ca9d" 
                        fillOpacity={0.6}
                        name="Opened"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="replied" 
                        stackId="3" 
                        stroke="#ffc658" 
                        fill="#ffc658" 
                        fillOpacity={0.6}
                        name="Replied"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Email Sequence */}
          {data.sequence && Array.isArray(data.sequence) && data.sequence.length > 0 && (
            <Card className="bg-white shadow-sm border border-gray-200">
              <CardHeader className="pb-3 border-b border-gray-100">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-lg font-medium">Campaign Sequence</CardTitle>
                    <CardDescription className="text-xs text-gray-500">
                      Configure and send campaign messages
                    </CardDescription>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {data.sequence.length} Email{data.sequence.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="p-0">
                {data.sequence.map((sequenceItem: any, index: number) => (
                  <div key={sequenceItem.id || index} className="flex flex-col md:flex-row border-b border-gray-100 last:border-b-0">
                    {/* Left Column: Sequence Info */}
                    <div className="w-full md:w-1/3 p-4 border-r border-gray-100">
                      <div className="mb-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
                            {sequenceItem.seq_number || index + 1}
                          </div>
                          <h3 className="text-sm font-medium">
                            Email {sequenceItem.seq_number || index + 1}
                          </h3>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              Day {sequenceItem.seq_delay_details?.delayInDays || 0}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              ID: {sequenceItem.id}
                            </Badge>
                          </div>
                          
                          <div className="text-xs text-gray-500">
                            <div>Created: {new Date(sequenceItem.created_at).toLocaleDateString()}</div>
                            <div>Updated: {new Date(sequenceItem.updated_at).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Right Column: Template Content */}
                    <div className="w-full md:w-2/3 p-0">
                      <div className="p-4">
                        {/* Subject Template */}
                        <div className="mb-4">
                          <label className="text-xs font-medium mb-1 block">Subject Template</label>
                          <div className="p-2 border rounded bg-gray-50 text-sm font-mono">
                            {sequenceItem.subject || 'No subject'}
                          </div>
                        </div>
                        
                        {/* Message Template */}
                        <div className="mb-4">
                          <label className="text-xs font-medium mb-1 block">Message Template</label>
                          <div className="border rounded bg-gray-50 h-[200px] overflow-hidden">
                            <div className="w-full h-full p-3 overflow-y-auto">
                              {sequenceItem.email_body ? (
                                <div 
                                  className="prose prose-sm max-w-none text-xs"
                                  style={{ 
                                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif' 
                                  }}
                                  dangerouslySetInnerHTML={{ 
                                    __html: sequenceItem.email_body
                                  }} 
                                />
                              ) : (
                                <div className="text-gray-500 italic text-xs">No email content available</div>
                              )}
                            </div>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            Variables like {"{{"} variable_name {"}}"} will be replaced with values
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Campaign Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Max Leads Per Day</p>
                  <p className="text-lg">{data.campaign?.max_leads_per_day || 'Not set'}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Min Time Between Emails</p>
                  <p className="text-lg">{data.campaign?.min_time_btwn_emails || 'Not set'} minutes</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Follow-up Percentage</p>
                  <p className="text-lg">{data.campaign?.follow_up_percentage || 'Not set'}%</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">AI ESP Matching</p>
                  <Badge variant={data.campaign?.enable_ai_esp_matching ? 'default' : 'secondary'}>
                    {data.campaign?.enable_ai_esp_matching ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Send as Plain Text</p>
                  <Badge variant={data.campaign?.send_as_plain_text ? 'default' : 'secondary'}>
                    {data.campaign?.send_as_plain_text ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Stop Lead Settings</p>
                  <p className="text-sm text-muted-foreground">{data.campaign?.stop_lead_settings || 'Not set'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

                    {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest email interactions for this campaign</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Contact</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sequence</TableHead>
                    <TableHead>Last Activity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.isArray(data?.recentActivity) && data.recentActivity.map((activity: any, index: number) => (
                    <TableRow key={`activity-${index}-${activity.email}-${activity.last_email_sent_at}`}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{activity.first_name} {activity.last_name}</span>
                          {activity.contact_id && (
                            <>
                              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 text-xs">
                                ✓ DB
                              </Badge>
                              <button
                                onClick={() => {
                                  if (onContactSelect && activity.contact_id) {
                                    onContactSelect(parseInt(activity.contact_id));
                                  }
                                }}
                                className="text-blue-600 hover:text-blue-800 transition-colors"
                                title="View contact details"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                        {activity.contact_info && (
                          <div className="text-xs text-muted-foreground mt-1">
                            DB: {activity.contact_info.contact_first_name} {activity.contact_info.contact_last_name}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{activity.company_name || 'N/A'}</span>
                          {activity.contact_info?.industry && (
                            <span className="text-xs text-muted-foreground">{activity.contact_info.industry}</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{activity.email}</TableCell>
                      <TableCell className="max-w-xs truncate">{activity.email_subject || 'N/A'}</TableCell>
                      <TableCell>
                        <Badge 
                          variant="outline"
                          className={`font-medium ${
                            activity.smartlead_status === 'SENT' ? 'bg-purple-100 text-purple-800 border-purple-300' :
                            activity.smartlead_status === 'DELIVERED' ? 'bg-blue-100 text-blue-800 border-blue-300' :
                            activity.smartlead_status === 'OPENED' ? 'bg-green-100 text-green-800 border-green-300' :
                            activity.smartlead_status === 'REPLIED' ? 'bg-emerald-100 text-emerald-800 border-emerald-300' :
                            activity.smartlead_status === 'BOUNCED' ? 'bg-red-100 text-red-800 border-red-300' :
                            activity.smartlead_status === 'UNSUBSCRIBED' ? 'bg-gray-100 text-gray-800 border-gray-300' :
                            'bg-gray-100 text-gray-800 border-gray-300'
                          }`}
                        >
                          {activity.smartlead_status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="text-xs">
                          {activity.sequence_number}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">
                        {activity.last_email_sent_at ? 
                          new Date(activity.last_email_sent_at).toLocaleString() : 
                          'N/A'
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
} 