"use client"

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle, Card<PERSON>ooter } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { ArrowLeft } from "lucide-react"

interface AddList44ContactProps {
  onBack: () => void;
  onSuccess: () => void;
}

const contactCategories = [
  { value: "investor", label: "Investor" },
  { value: "sponsor", label: "Sponsor" },
  { value: "third party", label: "Third Party" }
]

const AddList44Contact: React.FC<AddList44ContactProps> = ({ onBack, onSuccess }) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    job_title: '',
    company: '',
    company_website: '',
    capital_type: '',
    contact_category: '',
    linkedin_profile: '',
    company_address: '',
    industry: '',
    notes: '',
    investment_criteria_country: '',
    investment_criteria_state: '',
    investment_criteria_city: '',
    investment_criteria_property_type: '',
    investment_criteria_asset_type: '',
    investment_criteria_loan_type: '',
    investment_criteria_deal_size: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/list44', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to add contact');
      }
      
      const data = await response.json();
      toast.success('Contact added successfully');
      onSuccess();
    } catch (error) {
      console.error('Error adding contact:', error);
      toast.error('Failed to add contact');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" onClick={onBack} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h2 className="text-xl font-semibold">Add New Contact</h2>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="first_name">First Name <span className="text-red-500">*</span></Label>
                <Input 
                  id="first_name" 
                  name="first_name" 
                  value={formData.first_name} 
                  onChange={handleChange} 
                  required
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="last_name">Last Name <span className="text-red-500">*</span></Label>
                <Input 
                  id="last_name" 
                  name="last_name" 
                  value={formData.last_name} 
                  onChange={handleChange} 
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="phone_number">Phone Number</Label>
                <Input 
                  id="phone_number" 
                  name="phone_number" 
                  value={formData.phone_number} 
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="job_title">Job Title</Label>
                <Input 
                  id="job_title" 
                  name="job_title" 
                  value={formData.job_title} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="linkedin_profile">LinkedIn Profile</Label>
                <Input 
                  id="linkedin_profile" 
                  name="linkedin_profile" 
                  value={formData.linkedin_profile} 
                  onChange={handleChange}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Company Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="company">Company Name</Label>
                <Input 
                  id="company" 
                  name="company" 
                  value={formData.company} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="company_website">Website <span className="text-red-500">*</span></Label>
                <Input 
                  id="company_website" 
                  name="company_website" 
                  type="url"
                  value={formData.company_website} 
                  onChange={handleChange}
                  placeholder="https://example.com"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="industry">Industry</Label>
                <Input 
                  id="industry" 
                  name="industry" 
                  value={formData.industry} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="company_address">Company Address</Label>
                <Input 
                  id="company_address" 
                  name="company_address" 
                  value={formData.company_address} 
                  onChange={handleChange}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Categories Card */}
        <Card>
          <CardHeader>
            <CardTitle>Categories</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="capital_type">Capital Type</Label>
                <Input 
                  id="capital_type" 
                  name="capital_type" 
                  value={formData.capital_type} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="contact_category">Contact Category</Label>
                <Select 
                  value={formData.contact_category} 
                  onValueChange={(value) => handleSelectChange('contact_category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {contactCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Investment Criteria Card */}
        <Card>
          <CardHeader>
            <CardTitle>Investment Criteria</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_country">Country</Label>
                <Input 
                  id="investment_criteria_country" 
                  name="investment_criteria_country" 
                  value={formData.investment_criteria_country} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_state">State</Label>
                <Input 
                  id="investment_criteria_state" 
                  name="investment_criteria_state" 
                  value={formData.investment_criteria_state} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_city">City</Label>
                <Input 
                  id="investment_criteria_city" 
                  name="investment_criteria_city" 
                  value={formData.investment_criteria_city} 
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_property_type">Property Type</Label>
                <Input 
                  id="investment_criteria_property_type" 
                  name="investment_criteria_property_type" 
                  value={formData.investment_criteria_property_type} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_asset_type">Asset Type</Label>
                <Input 
                  id="investment_criteria_asset_type" 
                  name="investment_criteria_asset_type" 
                  value={formData.investment_criteria_asset_type} 
                  onChange={handleChange}
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_loan_type">Loan Type</Label>
                <Input 
                  id="investment_criteria_loan_type" 
                  name="investment_criteria_loan_type" 
                  value={formData.investment_criteria_loan_type} 
                  onChange={handleChange}
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="investment_criteria_deal_size">Deal Size</Label>
                <Input 
                  id="investment_criteria_deal_size" 
                  name="investment_criteria_deal_size" 
                  value={formData.investment_criteria_deal_size} 
                  onChange={handleChange}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Notes Card */}
        <Card>
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea 
              id="notes" 
              name="notes" 
              value={formData.notes} 
              onChange={handleChange}
              rows={4}
              className="resize-none"
            />
          </CardContent>
        </Card>
        
        <CardFooter className="flex justify-end space-x-4 px-0">
          <Button type="button" variant="outline" onClick={onBack}>Cancel</Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Adding...' : 'Add Contact'}
          </Button>
        </CardFooter>
      </form>
    </div>
  );
};

export default AddList44Contact; 