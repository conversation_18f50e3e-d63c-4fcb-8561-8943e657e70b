import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface Params {
  params: { threadId: string }
}

export async function GET(request: Request, { params }: Params) {
  try {
    const threadId = parseInt(params.threadId, 10)
    const result = await pool.query(
      `SELECT id, thread_id, parent_message_id, user_id, content, attachments, created_at, updated_at
       FROM team_messages
       WHERE thread_id = $1
       ORDER BY created_at ASC`,
      [threadId]
    )
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error fetching messages:', error)
    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })
  }
}

export async function POST(request: Request, { params }: Params) {
  try {
    const threadId = parseInt(params.threadId, 10)
    const { parent_message_id, content, user_id, attachments } = await request.json()
    if (!content) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }
    const createdBy = user_id || 1
    const insert = await pool.query(
      `INSERT INTO team_messages (thread_id, parent_message_id, user_id, content, attachments)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id, thread_id, parent_message_id, user_id, content, attachments, created_at, updated_at`,
      [threadId, parent_message_id || null, createdBy, content, attachments || null]
    )
    return NextResponse.json(insert.rows[0])
  } catch (error) {
    console.error('Error creating message:', error)
    return NextResponse.json({ error: 'Failed to create message' }, { status: 500 })
  }
}