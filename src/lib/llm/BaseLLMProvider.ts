/**
 * Base class for LLM (Large Language Model) providers
 * Provides common interfaces and methods for different LLM implementations
 */

export type LLMMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export type LLMRequestOptions = {
  temperature?: number;
  maxTokens?: number;
  model?: string;
  timeoutMs?: number;
};

export type LLMResponse = {
  content: string;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
  provider: string;
  model: string;
};

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LoggerInterface {
  log(level: LogLevel, message: string): void;
}

export abstract class BaseLLMProvider {
  protected name: string;
  protected logger: LoggerInterface;
  protected lastCallTimestamp: number = 0;
  protected defaultRateLimitMs: number = 1000; // 1 second default rate limit
  protected defaultModel: string;
  protected defaultOptions: LLMRequestOptions;

  constructor(name: string, logger: LoggerInterface, defaultOptions: LLMRequestOptions = {}) {
    this.name = name;
    this.logger = logger;
    this.defaultModel = defaultOptions.model || 'default';
    this.defaultOptions = {
      temperature: 0.1,
      maxTokens: 2000,
      timeoutMs: 30000,
      ...defaultOptions
    };
  }

  /**
   * Primary method to call the LLM with a set of messages
   */
  public abstract callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse>;

  /**
   * Utility method to enforce rate limiting between API calls
   */
  protected async rateLimitedCall<T>(
    fn: () => Promise<T>,
    rateLimitMs: number = this.defaultRateLimitMs
  ): Promise<T> {
    const now = Date.now();
    const elapsed = now - this.lastCallTimestamp;
    
    if (elapsed < rateLimitMs) {
      const delay = rateLimitMs - elapsed;
      this.logger.log('debug', `Rate limiting: waiting ${delay}ms before next API call`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastCallTimestamp = Date.now();
    return fn();
  }

  /**
   * Simple method to get provider name
   */
  public getName(): string {
    return this.name;
  }

  /**
   * Method to handle errors
   */
  protected handleError(error: unknown): never {
    const errorMessage = error instanceof Error ? error.message : String(error);
    this.logger.log('error', `${this.name} API error: ${errorMessage}`);
    throw new Error(`${this.name} API error: ${errorMessage}`);
  }
} 