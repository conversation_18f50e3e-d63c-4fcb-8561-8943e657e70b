import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch leads for a specific campaign with contact information joined by email
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    const url = new URL(req.url);
    const offset = url.searchParams.get('offset') || '0';
    const limit = url.searchParams.get('limit') || '50';
    
    // Build API URL with parameters
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}&offset=${offset}&limit=${limit}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error fetching leads for campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch leads: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Leads Response:`, data);
    if (data.total_leads > 0) {
      // Extract all email addresses from the leads
      const leadEmails = data.data
        .map((leadItem: any) => leadItem.lead?.email)
        .filter((email: string) => email)
        .map((email: string) => email.toLowerCase());

      let contactsMap = new Map();
      
      if (leadEmails.length > 0) {
        // Query contacts table to get contact_id, company_id, and company_name for matching emails
        const placeholders = leadEmails.map((_: string, index: number) => `$${index + 1}`).join(', ');
        const query = `
          SELECT 
            c.email,
            c.contact_id,
            c.company_id,
            c.first_name as contact_first_name,
            c.last_name as contact_last_name,
            c.title as contact_title,
            comp.company_name,
            comp.company_website,
            comp.industry
          FROM contacts c
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE LOWER(c.email) = ANY(ARRAY[${placeholders}])
        `;
        
        try {
          const { rows } = await pool.query(query, leadEmails);
          
          // Create a map of email -> contact info for fast lookup
          rows.forEach(contact => {
            contactsMap.set(contact.email.toLowerCase(), {
              contact_id: contact.contact_id,
              company_id: contact.company_id,
              contact_first_name: contact.contact_first_name,
              contact_last_name: contact.contact_last_name,
              contact_title: contact.contact_title,
              company_name: contact.company_name,
              company_website: contact.company_website,
              industry: contact.industry
            });
          });
        } catch (dbError) {
          console.error('Error querying contacts:', dbError);
          // Continue without contact info if DB query fails
        }
      }

      // Enhance the lead data with contact information
      const enhancedLeads = data.data.map((leadItem: any) => {
        const leadEmail = leadItem.lead?.email?.toLowerCase();
        const contactInfo = leadEmail ? contactsMap.get(leadEmail) : null;
        
        return {
          ...leadItem,
          contact_info: contactInfo || null
        };
      });
      // console.log(`Enhanced leads:`, enhancedLeads);
      return NextResponse.json(enhancedLeads);
    } else {
      return NextResponse.json(
        { error: `No leads found for campaign ${campaignId}` },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error(`Error fetching leads for campaign:`, error);
    return NextResponse.json(
      { error: `Error fetching leads: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Add leads to a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;
    
    const body = await req.json();
    const { leads } = body;
    
    if (!leads || !Array.isArray(leads) || leads.length === 0) {
      return NextResponse.json(
        { error: 'Leads array is required' },
        { status: 400 }
      );
    }
    
    // Build API URL
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/${campaignId}/leads?api_key=${SMARTLEAD_API_KEY}`;
    
    // Format payload according to Smartlead API requirements
    const payload = {
      lead_list: leads.map(lead => ({
        email: lead.email,
        first_name: lead.first_name || '',
        last_name: lead.last_name || '',
        company_name: lead.company_name || '',
        custom_fields: lead.custom_fields || {}
      }))
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error adding leads to campaign ${campaignId}: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to add leads: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error adding leads to campaign:`, error);
    return NextResponse.json(
      { error: `Error adding leads: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 