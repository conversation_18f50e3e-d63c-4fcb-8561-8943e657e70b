"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { RefreshCw } from 'lucide-react';
import { Campaign } from '../types';
import { fetchCampaigns } from '../services/campaignService';

interface CampaignSelectorProps {
  selectedCampaignId: string;
  onCampaignSelect: (campaignId: string) => void;
}

const CampaignSelector: React.FC<CampaignSelectorProps> = ({
  selectedCampaignId,
  onCampaignSelect
}) => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(false);

  // Load available campaigns on mount
  useEffect(() => {
    const loadCampaigns = async () => {
      setLoading(true);
      try {
        const campaignData = await fetchCampaigns();
        setCampaigns(campaignData);
      } catch (error) {
        console.error('Error loading campaigns:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCampaigns();
  }, []);

  return (
    <div className="mb-4 flex flex-wrap gap-2">
      {/* All Campaigns tab */}
      <Button
        variant={selectedCampaignId === 'all' ? 'default' : 'outline'}
        onClick={() => onCampaignSelect('all')}
        disabled={loading}
      >
        All Campaigns
      </Button>
      
      {/* Individual campaign tabs */}
      {loading ? (
        <Button variant="outline" disabled>
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Loading...
        </Button>
      ) : (
        <>
          {campaigns.map(campaign => (
            <Button
              key={campaign.id}
              variant={selectedCampaignId === campaign.id ? 'default' : 'outline'}
              onClick={() => onCampaignSelect(campaign.id)}
            >
              {campaign.name}
            </Button>
          ))}
        </>
      )}
    </div>
  );
};

export default CampaignSelector; 