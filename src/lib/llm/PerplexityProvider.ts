import { Base<PERSON><PERSON>rovider, <PERSON>MMessage, LLMRequestOptions, LLMResponse, LoggerInterface } from './BaseLLMProvider';

/**
 * Perplexity AI implementation of the LLM provider
 */
export class PerplexityProvider extends BaseLLMProvider {
  private apiKey: string;
  private readonly defaultPerplexityModel = 'r1-1776';
  private readonly apiUrl = 'https://api.perplexity.ai/chat/completions';

  constructor(logger: LoggerInterface, apiKey?: string, defaultOptions: LLMRequestOptions = {}) {
    super('Perplexity', logger, {
      model: defaultOptions.model || 'r1-1776',
      ...defaultOptions
    });

    // Use API key from env if not provided
    const perplexityApiKey = apiKey || process.env.PERPLEXITY_API_KEY;
    
    if (!perplexityApiKey) {
      throw new Error('Perplexity API key is required. Set PERPLEXITY_API_KEY environment variable or pass as parameter');
    }

    this.apiKey = perplexityApiKey;
  }

  /**
   * Call Perplexity API with fetch
   */
  public async callLLM(
    messages: LLMMessage[],
    options?: LLMRequestOptions
  ): Promise<LLMResponse> {
    try {
      const mergedOptions = {
        ...this.defaultOptions,
        ...options
      };

      const model = mergedOptions.model || this.defaultPerplexityModel;
      // Convert our generic messages to Perplexity format (similar to OpenAI)
      const perplexityMessages = messages.map(message => ({
        role: message.role,
        content: message.content
      }));

      // Build request payload
      const payload = {
        model,
        messages: perplexityMessages,
        temperature: mergedOptions.temperature,
        max_tokens: mergedOptions.maxTokens
      };
      // Call Perplexity with rate limiting
      const responseData = await this.rateLimitedCall(async () => {
        const response = await fetch(this.apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify(payload)
        });

        this.logger.log('debug', `PerplexityProvider: Response status: ${response.status}`);

        if (!response.ok) {
          const errorData = await response.text();
          throw new Error(`Perplexity API error (${response.status}): ${errorData}`);
        }

        const responseData = await response.json();
        this.logger.log('debug', `PerplexityProvider: Response data: ${JSON.stringify(responseData)}`);
        
        // Validate response structure
        if (!responseData.choices || !responseData.choices[0] || !responseData.choices[0].message) {
          throw new Error(`Invalid response structure from Perplexity: ${JSON.stringify(responseData)}`);
        }

        return responseData;
      }, 3000); // Use 3s rate limit for Perplexity

      // Extract the content from the response
      const content = responseData.choices[0].message.content;
      
      if (!content || content.trim() === '') {
        throw new Error('Empty response content from Perplexity');
      }

      // Return in our standardized format
      return {
        content,
        usage: {
          promptTokens: responseData.usage?.prompt_tokens,
          completionTokens: responseData.usage?.completion_tokens,
          totalTokens: responseData.usage?.total_tokens
        },
        provider: this.name,
        model: responseData.model || model
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
} 