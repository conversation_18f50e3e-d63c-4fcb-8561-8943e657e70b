import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/db';

/**
 * GET: Fetch messages for a specific campaign
 */
export async function GET(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const { campaignId } = await context.params;
    
    // Get the contact_id from query parameters
    const url = new URL(req.url);
    const contactIdParam = url.searchParams.get('contact_id');
    
    // Require contact_id parameter
    if (!contactIdParam) {
      return NextResponse.json(
        { error: 'contact_id is required' },
        { status: 400 }
      );
    }
    
    // Parse contact_id to number
    const contactId = parseInt(contactIdParam, 10);
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { error: 'Invalid contact_id format' },
        { status: 400 }
      );
    }
    
    // Build the query with required contact_id filter
    const query = `
      SELECT 
        m.message_id, 
        m.thread_id,
        m.subject, 
        m.body, 
        m.from_email,
        m.to_email,
        m.direction,
        m.role,
        m.sent_at,
        m.smartlead_campaign_id,
        m.metadata, 
        m.created_at,
        tp.contact_id
      FROM messages m
      JOIN threads t ON m.thread_id = t.thread_id
      JOIN thread_participants tp ON t.thread_id = tp.thread_id AND tp.participant_type = 'contact'
      WHERE m.smartlead_campaign_id = $1
      AND tp.contact_id = $2
      ORDER BY m.created_at DESC
      LIMIT 50`;
      
    // Parameters for query
    const queryParams = [campaignId, contactId];
    
    const result = await pool.query(query, queryParams);
    
    // Ensure all messages have a direction property
    const messages = result.rows.map(msg => ({
      ...msg,
      direction: msg.direction || 'outbound' // Default to outbound if missing
    }));
    
    return NextResponse.json(messages);
    
  } catch (error) {
    console.error('Error fetching campaign messages:', error);
    return NextResponse.json(
      { error: `Error fetching campaign messages: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Create or update a message for a campaign
 */
export async function POST(
  req: NextRequest,
  context: { params: { campaignId: string } }
) {
  try {
    const { campaignId } = await context.params;
    const body = await req.json();
    const { message_id, subject, message_body, sequence_type } = body;
    
    if (!subject || !message_body) {
      return NextResponse.json(
        { error: 'Subject and message body are required' },
        { status: 400 }
      );
    }
    
    // Store metadata including the sequence_type (initial, followup1, followup2)
    const metadata = {
      sequence_type,
      is_template: true,
      last_updated: new Date().toISOString()
    };
    
    let result;
    
    if (message_id) {
      // Update existing message
      result = await pool.query(
        `UPDATE messages 
         SET subject = $1, body = $2, metadata = $3
         WHERE message_id = $4 AND smartlead_campaign_id = $5
         RETURNING *`,
        [subject, message_body, metadata, message_id, campaignId]
      );
      
      if (result.rowCount === 0) {
        return NextResponse.json(
          { error: 'Message not found or does not belong to this campaign' },
          { status: 404 }
        );
      }
    } else {
      // Create new message
      // Generate a new UUID for message_id and thread_id
      result = await pool.query(
        `INSERT INTO messages 
         (message_id, thread_id, subject, body, metadata, smartlead_campaign_id, created_at)
         VALUES (gen_random_uuid(), gen_random_uuid(), $1, $2, $3, $4, CURRENT_TIMESTAMP)
         RETURNING *`,
        [subject, message_body, metadata, campaignId]
      );
    }
    
    return NextResponse.json({
      success: true,
      message: message_id ? 'Message updated successfully' : 'Message created successfully',
      data: result.rows[0]
    });
    
  } catch (error) {
    console.error('Error saving campaign message:', error);
    return NextResponse.json(
      { error: `Error saving campaign message: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 