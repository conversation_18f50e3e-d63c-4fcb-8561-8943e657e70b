import { NextResponse } from 'next/server';
import pLimit from 'p-limit';
import { pool } from '@/lib/db';
import { OpenAIProvider } from '@/lib/llm/OpenAIProvider';
import { LoggerInterface, LogLevel } from '@/lib/llm/BaseLLMProvider';


// up to 10 articles in parallel
export const dynamic = 'force-dynamic';

type ExtractedInfo = {
  article_classification: {
    contains_deals: boolean;
    deal_count: number;
    primary_focus: string;
    confidence_score: number;
  };
  deals: Array<{
    deal_type?: string;
    deal_value?: number | string;
    deal_value_confidence?: number;  // optional
    property_details: {
      type?: string;
      size?: number | string;
      address?: string;
      additional_details?: Record<string, any>;
    };
    financial_details: {
      cap_rate?: number | string;
      ltv?: number | string;
      additional_terms?: Record<string, any>;
    };
    participants: {
      companies: Array<{
        name: string;
        role?: string;             // buyer, seller, etc.
        confidence_score?: number;
        key_people?: Array<{
          name: string;
          title?: string;
          role_in_deal?: string;
        }>;
      }>;
    };
    deal_date?: string;     // e.g. "2025-01-02"
    confidence_score?: number;
    status?: string;        // e.g. "planned", "completed"
  }>;
  mentioned_companies: Array<{
    name: string;
    context: string;
    confidence_score?: number;
  }>;
  mentioned_people: Array<{
    name: string;
    title?: string;
    company?: string;
    context?: string;
    confidence_score?: number;
  }>;
};

function parseNumberSafely(value: any): number | null {
  if (value === null || value === undefined) return null;
  // If GPT sends it as a string, parse it:
  const n = parseFloat(String(value));
  return isNaN(n) ? null : n;
}

// Parse and normalize dates to YYYY-MM-DD format
function parseDateSafely(dateString: any): string | null {
  if (!dateString) return null;

  const dateStr = String(dateString).trim();

  // Try standard ISO format first
  const isoDate = new Date(dateStr);
  if (!isNaN(isoDate.getTime())) {
    return isoDate.toISOString().split('T')[0]; // YYYY-MM-DD
  }

  // Try to handle month year formats like "June 2022"
  const monthYearRegex = /^([a-zA-Z]+)\s+(\d{4})$/;
  const match = dateStr.match(monthYearRegex);
  if (match) {
    const month = new Date(Date.parse(`${match[1]} 1, ${match[2]}`)).getMonth() + 1;
    if (!isNaN(month)) {
      return `${match[2]}-${month.toString().padStart(2, '0')}-01`; // YYYY-MM-01
    }
  }

  // Try other common formats with US date parsing
  try {
    // MM/DD/YYYY
    const parts = dateStr.split(/[/.-]/);
    if (parts.length === 3) {
      // Assume MM/DD/YYYY or similar format
      const parsedDate = new Date(
        parseInt(parts[2]), // year
        parseInt(parts[0]) - 1, // month (0-based)
        parseInt(parts[1]) // day
      );
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.toISOString().split('T')[0];
      }
    }
  } catch (e) {
    // Continue to next format if this fails
  }

  console.log(`Warning: Could not parse date string: "${dateStr}"`);
  return null;
}

// Normalize the extracted data to ensure consistent format
function normalizeExtractedData(data: any): ExtractedInfo {
  // Ensure article_classification is an object
  if (!data.article_classification || typeof data.article_classification !== 'object') {
    data.article_classification = {
      contains_deals: Array.isArray(data.deals) && data.deals.length > 0,
      deal_count: Array.isArray(data.deals) ? data.deals.length : 0,
      primary_focus: "unknown",
      confidence_score: 0.5
    };
  }

  // Ensure deals is an array
  if (!Array.isArray(data.deals)) {
    data.deals = [];
  }

  // Normalize mentioned_companies
  if (!Array.isArray(data.mentioned_companies)) {
    data.mentioned_companies = [];
  } else {
    // Convert any string items to objects
    data.mentioned_companies = data.mentioned_companies.map((company: any) => {
      if (typeof company === 'string') {
        return {
          name: company,
          context: 'mentioned',
          confidence_score: 0.5
        };
      }
      return {
        name: company.name || null,
        context: company.context || 'mentioned',
        confidence_score: company.confidence_score || 0.5
      };
    });
  }

  // Normalize mentioned_people
  if (!Array.isArray(data.mentioned_people)) {
    data.mentioned_people = [];
  } else {
    // Ensure all people have required fields
    data.mentioned_people = data.mentioned_people.map((person: any) => {
      if (typeof person === 'string') {
        return {
          name: person,
          title: null,
          company: null,
          context: 'mentioned',
          confidence_score: 0.5
        };
      }
      return {
        name: person.name || null,
        title: person.title || person.role || null,
        company: person.company || person.affiliation || null,
        context: person.context || 'mentioned',
        confidence_score: person.confidence_score || 0.5
      };
    });
  }

  return data as ExtractedInfo;
}

// Create a logger for OpenAI provider
const logger: LoggerInterface = {
  log: (level: LogLevel, message: string) => {
    console.log(`[OpenAI ExtractDeals] [${level.toUpperCase()}] ${message}`);
  }
};

// Initialize OpenAIProvider
const openaiProvider = new OpenAIProvider(logger);

// 1) EXTRACT: prompt GPT and parse the JSON
async function extractDealInformation(
  article: {
    id: number;
    news_date: string;
    news_title: string;
    news_text: string;
  }
): Promise<ExtractedInfo> {
  console.log(`Extracting deal information for article ${article.id}...`);
  
  const truncateText = (text: string, maxLength: number = 100000) => {
    return text.length > maxLength 
      ? text.substring(0, maxLength) + "..." 
      : text;
  };

  const modelPrompt = `
You are a sophisticated AI system specialized in extracting structured information about real estate deals from news articles.
Carefully analyze the following article about real estate and extract all mentioned deals and relevant entities.

Output ONLY valid JSON in the exact format specified below:

{
  "article_classification": {
    "contains_deals": true/false,
    "deal_count": <integer>,
    "primary_focus": "<e.g. acquisition, development, financing, market report>",
    "confidence_score": <float between 0 and 1>
  },
  "deals": [
    {
      "deal_type": "<acquisition, sale, lease, financing, refinancing, joint venture, or development>",
      "deal_value": <numeric amount in USD or null if not specified>,
      "deal_value_confidence": <float between 0 and 1>,
      "property_details": {
        "type": "<e.g. office, retail, multifamily, industrial, mixed-use, land>",
        "size": <square footage or unit count with units>,
        "address": "<full address or partial location info>",
        "additional_details": {}
      },
      "financial_details": {
        "cap_rate": <percentage or null>,
        "ltv": <percentage or null>,
        "additional_terms": {}
      },
      "participants": {
        "companies": [
          {
            "name": "<company name>",
            "role": "<e.g. buyer, seller, broker, lender>",
            "confidence_score": <float between 0 and 1>,
            "key_people": [
              {
                "name": "<person name>",
                "title": "<job title>",
                "role_in_deal": "<specific role in this transaction>"
              }
            ]
          }
        ]
      },
      "deal_date": "<date in YYYY-MM-DD format or as specific as possible>",
      "confidence_score": <float between 0 and 1>,
      "status": "<planned, in progress, completed, etc>"
    }
  ],
  "mentioned_companies": [
    {
      "name": "<company name>",
      "context": "<brief description of how company was mentioned>",
      "confidence_score": <float between 0 and 1>
    }
  ],
  "mentioned_people": [
    {
      "name": "<person name>",
      "title": "<job title>",
      "company": "<employer>",
      "context": "<brief description of how person was mentioned>",
      "confidence_score": <float between 0 and 1>
    }
  ]
}

Article Title: ${article.news_title}
Article Date: ${article.news_date || 'Unknown'}
Article Text:
${truncateText(article.news_text)}
`;

  try {
    // Use the OpenAI provider to extract information
    const response = await openaiProvider.callLLM([
      {
        role: 'system',
        content: 'You extract structured information about real estate deals from news articles. Always respond with complete, valid JSON.',
      },
      {
        role: 'user',
        content: modelPrompt
      }
    ], {
      model: 'gpt-4.1-mini',
      temperature: 0.1,
      maxTokens: 4000,
    });

    // Get the response content
    const content = response.content;
    
    // Log the raw response for debugging
    console.log(`Raw response for article ${article.id}: ${content.slice(0, 200)}...`);
    
    try {
      // Clean up any markdown formatting
      const cleanJson = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*$/g, '')
        .trim();
      
      // Parse and normalize the extracted data
      const extractedData = JSON.parse(cleanJson);
      const normalizedData = normalizeExtractedData(extractedData);
      
      console.log(`Successfully extracted deal information for article ${article.id}`);
      return normalizedData;
    } catch (parseError) {
      console.error(`JSON parse error for article ${article.id}:`, parseError);
      console.log('Raw content:', content);
      
      // Return empty structure on parsing error
      return {
        article_classification: {
          contains_deals: false,
          deal_count: 0,
          primary_focus: "parsing_error",
          confidence_score: 0
        },
        deals: [],
        mentioned_companies: [],
        mentioned_people: []
      };
    }
  } catch (error) {
    console.error(`API error for article ${article.id}:`, error);
    throw error;
  }
}

// 2) SAVE to DB: deals, companies, persons
async function saveDealInformation(
  client: any,
  articleId: number,
  extractedInfo: ExtractedInfo
) {
  console.log(`Saving extracted info for article ${articleId}`);

  try {
    await client.query('BEGIN');

    // 2.1 If GPT says there are deals, insert them:
    if (extractedInfo.article_classification.contains_deals) {
      for (const deal of extractedInfo.deals ?? []) {
        // console.log(`Saving deal ${JSON.stringify(deal, null, 2)}`);
        // parse numeric fields
        const dealValue = parseNumberSafely(deal.deal_value);
        const capRate = parseNumberSafely(deal.financial_details?.cap_rate);
        const ltv = parseNumberSafely(deal.financial_details?.ltv);

        // Parse date properly
        const dealDate = parseDateSafely(deal.deal_date);

        try {
          // Insert into deal_news_deals
          const dealResult = await client.query(
            `
            INSERT INTO deal_news_deals (
              news_id,
              deal_type,
              deal_status,
              deal_date,
              deal_value,
              cap_rate,
              ltv,
              confidence_score,
              property_type,
              property_size,
              property_size_unit,
              property_address,
              deal_details
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            RETURNING id
            `,
            [
              articleId,
              deal.deal_type ?? null,
              deal.status ?? null,
              dealDate,
              dealValue,
              capRate,
              ltv,
              deal.confidence_score ?? null,
              deal.property_details?.type ?? null,
              parseNumberSafely(deal.property_details?.size),
              // If you prefer to parse a size unit from the text, do it. Otherwise store "sqft" in property_size_unit:
              null, // or "sqft" if GPT provides it
              deal.property_details?.address ?? null,
              {
                deal_value_confidence: deal.deal_value_confidence ?? null,
                property_additional: deal.property_details?.additional_details ?? {},
                financial_additional: deal.financial_details?.additional_terms ?? {}
              }
            ]
          );

          const newDealId = dealResult.rows[0].id;

          // 2.1.1 Insert participant companies
          for (const company of deal.participants?.companies ?? []) {
            // Skip if company name is null or empty
            if (!company.name) {
              console.log(`Skipping participant company without name in article ${articleId}, deal ${newDealId}`);
              continue;
            }
            // console.log(`Saving company ${JSON.stringify(company, null, 2)}`)
            const companyResult = await client.query(
              `
              INSERT INTO deal_news_companies (
                news_id,
                deal_id,
                company_name,
                role,
                confidence_score,
                participation_details
              )
              VALUES ($1, $2, $3, $4, $5, $6)
              RETURNING id
              `,
              [
                articleId,
                newDealId,
                company.name,
                company.role ?? null,
                company.confidence_score ?? null,
                {
                  key_people: company.key_people ?? []
                }
              ]
            );

            const newCompanyId = companyResult.rows[0].id;

            // 2.1.2 Insert the key people for each company
            for (const person of company.key_people ?? []) {
              // Skip if person name is null or empty
              if (!person.name) {
                console.log(`Skipping key person without name in article ${articleId}, company ${newCompanyId}`);
                continue;
              }

              // console.log(`Saving person ${JSON.stringify(person, null, 2)}`)

              await client.query(
                `
                INSERT INTO deal_news_persons (
                  news_id,
                  deal_id,
                  company_id,
                  person_name,
                  title,
                  role,
                  confidence_score,
                  involvement_details
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                `,
                [
                  articleId,
                  newDealId,
                  newCompanyId,
                  person.name,
                  person.title ?? null,
                  person.role_in_deal ?? null,
                  // We'll assume person's confidence is same as company's?
                  company.confidence_score ?? null,
                  { context: 'deal_participant' }
                ]
              );
            }
          }
        } catch (error) {
          console.error(`Error saving deal for article ${articleId}:`, error);
          throw error;
        }
      }
    }

    // 2.2 Mentioned companies (general, no deal_id)
    for (const mc of extractedInfo.mentioned_companies ?? []) {
      // Skip if company name is null or empty
      if (!mc.name) {
        console.log(`Skipping company without name in article ${articleId}`);
        continue;
      }
      // console.log(`Saving company ${JSON.stringify(mc, null, 2)}`)
      await client.query(
        `
        INSERT INTO deal_news_companies (
          news_id,
          deal_id,
          company_name,
          confidence_score,
          participation_details
        )
        VALUES ($1, $2, $3, $4, $5)
        `,
        [
          articleId,
          null, // no deal
          mc.name,
          mc.confidence_score ?? null,
          { context: mc.context ?? 'mentioned' }
        ]
      );
    }

    // 2.3 Mentioned people (general, no deal_id or company_id)
    for (const mp of extractedInfo.mentioned_people ?? []) {
      // Skip if person name is null or empty
      if (!mp.name) {
        console.log(`Skipping person without name in article ${articleId}`);
        continue;
      }
      // console.log(`Saving person ${JSON.stringify(mp, null, 2)}`)
      await client.query(
        `
        INSERT INTO deal_news_persons (
          news_id,
          deal_id,
          company_id,
          person_name,
          title,
          confidence_score,
          involvement_details
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        `,
        [
          articleId,
          null, // no deal
          null, // no company
          mp.name,
          mp.title ?? null,
          mp.confidence_score ?? null,
          {
            context: mp.context ?? 'mentioned',
            raw_company: mp.company ?? null
          }
        ]
      );
    }

    // 2.4 Update extraction_metadata
    await client.query(
      `
      UPDATE deal_news
      SET extracted = true,
          extraction_metadata = $1
      WHERE id = $2
      `,
      [
        {
          classification: extractedInfo.article_classification,
        },
        articleId
      ]
    );

    await client.query('COMMIT');
    console.log(`Successfully saved information for article ${articleId}`);
    return true;
  } catch (error) {
    console.error(`Error saving article ${articleId} information:`, error);

    // Rollback transaction on error
    try {
      await client.query('ROLLBACK');
      console.log(`Transaction rolled back for article ${articleId}`);
    } catch (rollbackError) {
      console.error(`Failed to rollback transaction for article ${articleId}:`, rollbackError);
    }

    throw error;
  }
}

// 3) Process a single article
async function processSingleArticle(
  client: any,
  article: any,
  testMode: boolean
) {
  try {
    console.log(`\nProcessing article ${article.id}: ${article.news_title}`);
    
    // STEP 1: Extract deal information
    const extractedInfo = await extractDealInformation(article);
    
    // STEP 2: Save the information if not in test mode
    if (!testMode) {
      await saveDealInformation(client, article.id, extractedInfo);
      console.log(`Saved deal information for article ${article.id}`);
    } else {
      console.log(`Test mode: NOT saving deal information for article ${article.id}`);
    }
    
    return {
      id: article.id,
      title: article.news_title,
      success: true,
      extracted: extractedInfo
    };
  } catch (error: any) {
    console.error(`Error processing article ${article.id}:`, error);
    return {
      id: article.id,
      title: article.news_title,
      success: false,
      error: error.message
    };
  }
}

// 4) POST route to handle up to 10 articles in parallel
export async function POST(request: Request) {
  // Processing state
  let processingResults: any[] = [];
  let errors: any[] = [];
  
  // Get a client from the pool
  let client: PoolClient;
  
  try {
    const { testMode = false, limit = 5, articleIds = [] } = await request.json();
    console.log(`Request: testMode=${testMode}, limit=${limit}, specific articleIds=${articleIds.join(',')}`);
    
    // Validate parameters
    const validLimit = Math.min(Math.max(1, parseInt(String(limit))), 50);
    
    // Get a dedicated client
    client = await pool.connect();
    
    // Build the query based on parameters
    let query = `
      SELECT id, news_title, news_text, news_date
      FROM deal_news
      WHERE processed = true 
        AND news_text IS NOT NULL
        AND is_relevant = true
    `;
    
    let queryParams = [];
    
    // If specific article IDs were provided, use those
    if (articleIds && articleIds.length > 0) {
      query += ` AND id = ANY($1)`;
      queryParams.push(articleIds);
    }
    
    // Add order and limit
    query += ` ORDER BY id DESC LIMIT $${queryParams.length + 1}`;
    queryParams.push(validLimit);
    
    // Get articles to process
    const { rows: articles } = await client.query(query, queryParams);
    console.log(`Found ${articles.length} articles to process`);
    
    if (articles.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No articles found to process',
        processed: 0
      });
    }
    
    // Set up concurrency limit (process up to 3 articles in parallel)
    const concurrencyLimit = pLimit(3);
    
    // Process articles with concurrency limit
    const processingPromises = articles.map(article => 
      concurrencyLimit(() => processSingleArticle(client, article, testMode))
    );
    
    // Wait for all processing to complete
    const results = await Promise.all(processingPromises);
    
    // Collect results and errors
    processingResults = results.filter(r => r.success);
    errors = results.filter(r => !r.success);
    
    // Return results
    return NextResponse.json({
      success: true,
      message: `Processed ${articles.length} articles`,
      processed: processingResults.length,
      errors: errors.length,
      results: testMode ? processingResults : processingResults.map(r => ({ id: r.id, title: r.title })),
      errorDetails: errors
    });
    
  } catch (error: any) {
    console.error('Error:', error);
    return NextResponse.json({
      success: false,
      message: error.message,
      processed: processingResults.length,
      errors: errors.length + 1,
      results: processingResults,
      errorDetails: [...errors, { error: error.message }]
    }, { status: 500 });
  }
}

