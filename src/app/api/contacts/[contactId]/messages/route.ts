import { NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params;
    console.log('API route called for messages with contact ID:', contactId);

    // First get all threads where this contact is a participant
    const threadsQuery = `
      SELECT 
        t.thread_id,
        t.subject,
        t.status,
        t.created_at,
        t.updated_at,
        t.metadata
      FROM threads t
      JOIN thread_participants tp ON t.thread_id = tp.thread_id
      WHERE tp.contact_id = $1
      ORDER BY t.updated_at DESC
    `;

    const threadsResult = await pool.query(threadsQuery, [contactId]);
    
    if (threadsResult.rows.length === 0) {
      return NextResponse.json({
        threads: [],
        messages: []
      });
    }

    // Get all thread IDs
    const threadIds = threadsResult.rows.map(thread => thread.thread_id);
    
    // Get all messages for these threads
    const messagesQuery = `
      SELECT 
        m.message_id,
        m.thread_id,
        m.from_email,
        m.to_email,
        m.subject,
        m.direction,
        m.role,
        m.body,
        m.created_at,
        m.smartlead_campaign_id,
        m.sent_at
      FROM messages m
      WHERE m.thread_id = ANY($1::uuid[])
      ORDER BY m.created_at ASC
    `;

    const messagesResult = await pool.query(messagesQuery, [threadIds]);

    // Organize messages by thread
    const messagesByThread = threadsResult.rows.map(thread => {
      const threadMessages = messagesResult.rows.filter(message => 
        message.thread_id === thread.thread_id
      );
      
      return {
        ...thread,
        messages: threadMessages
      };
    });

    return NextResponse.json({
      threads: messagesByThread
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages', details: (error as Error).message }, 
      { status: 500 }
    );
  }
} 