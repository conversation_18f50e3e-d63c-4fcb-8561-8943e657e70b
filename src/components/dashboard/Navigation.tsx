import React, { useState } from 'react'
import { 
  CircleDollarSign, 
  Code,
  Newspaper,
  Users,
  User,
  Mail,
  Building2,
  Handshake,
  FileText,
  Calculator,
  Settings,
  LogOut,
  Database,
  Briefcase,
  FileCheck,
  HandshakeIcon,
  FileIcon,
  LineChart,
  Zap,
  Network,
  Send,
  Activity,
  BarChart3
} from 'lucide-react'

export const Navigation = ({ 
  activeSection, 
  setActiveSection, 
  isCollapsed, 
  setIsCollapsed,
  accessLevel
}: {
  activeSection: string;
  setActiveSection: (section: string) => void;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  accessLevel: 'admin' | 'restricted' | null;
}) => {
  const [showProfile, setShowProfile] = useState(false)

  // Define which sections are available for restricted users
  const RESTRICTED_SECTIONS = ['projections'] // Only allow projections and whitepaper

  const canAccessSection = (sectionName: string) => {
    if (accessLevel === 'admin') return true
    if (accessLevel === 'restricted') {
      return RESTRICTED_SECTIONS.includes(sectionName)
    }
    return false
  }

  const handleWhitepaperClick = () => {
    const domain = window.location.hostname
    const wpDomain = domain.includes('anax.my') ? 'wp.anax.my' : 'wp.anax.cloud'
    window.open(`https://${wpDomain}`, '_blank')
  }

  const handleLogout = () => {
    localStorage.removeItem('dashboardAccess')
    window.location.reload() // This will force a refresh and return to login
  }

  const navItems = [
    { id: 'people', name: 'People', icon: User, color: 'text-purple-500' },
    { id: 'companies', name: 'Companies', icon: Building2, color: 'text-green-500' },
    { id: 'upload', name: 'Data Upload', icon: Database, color: 'text-blue-500' },
    { id: 'engagement', name: 'Engagement', icon: Mail, color: 'text-yellow-500' },
    { id: 'smartlead', name: 'Smartlead', icon: Send, color: 'text-pink-500' },
    { id: 'processing', name: 'Processing', icon: Activity, color: 'text-cyan-500' },
    { id: 'status', name: 'Status', icon: BarChart3, color: 'text-indigo-500' },
    { id: 'db', name: 'DB', icon: FileText, color: 'text-orange-500' },
    { id: 'dealnews', name: 'Deal News', icon: Newspaper, color: 'text-gray-500' },
    { id: 'tech', name: 'Tech Stuff', icon: Code, color: 'text-gray-500' },
    { id: 'deals', name: 'Deals', icon: Handshake, color: 'text-blue-500' },
    { id: 'projections', name: 'Projections', icon: LineChart, color: 'text-violet-500' }
  ]

  return (
    <div className="bg-white p-3 fixed left-0 top-0 h-full w-40 border-r flex flex-col">
      <button 
        onClick={() => setActiveSection('dashboard')}
        className="relative h-8 w-full mb-8 hover:opacity-80 transition-opacity"
      >
        <CircleDollarSign className={`h-8 w-8 text-blue-600 ${isCollapsed ? 'mx-auto' : 'ml-2'}`} />
        <span className={`absolute left-12 top-1/2 -translate-y-1/2 text-lg font-bold transition-opacity duration-300 ${
          isCollapsed ? 'opacity-0' : 'opacity-100'
        }`}>
          ANAX
        </span>
      </button>
      
      <nav className="space-y-2 flex-1">
        {navItems.map((item) => (
          canAccessSection(item.id) && (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`w-full flex items-center space-x-2.5 px-3 py-2.5 rounded-lg transition-all ${
                activeSection === item.id 
                  ? 'bg-blue-50 text-blue-600' 
                  : 'hover:bg-gray-50'
              }`}
            >
              <item.icon className={`h-6 w-6 ${item.color}`} />
              <span className="text-base font-medium">{item.name}</span>
            </button>
          )
        ))}
      </nav>

      <button
        onClick={handleWhitepaperClick}
        className="w-full flex items-center space-x-2.5 px-3 py-2.5 rounded-lg transition-all hover:bg-gray-50 mb-2"
      >
        <Zap className="h-6 w-6 text-yellow-500" />
        <span className="text-base font-medium">Whitepaper</span>
      </button>

      <div className="border-t pt-3">
        <button 
          onClick={handleLogout}
          className="w-full flex items-center justify-center space-x-2 p-2 hover:bg-gray-50 rounded-lg text-sm text-red-600"
        >
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  )
}