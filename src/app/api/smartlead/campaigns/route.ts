import { NextRequest, NextResponse } from 'next/server';

// Smartlead API configuration
const SMARTLEAD_API_KEY = process.env.SMARTLEAD_API_KEY || '9d17182d-9e03-4629-9305-c0dc3b41c2de_01d8fqr';
const SMARTLEAD_BASE_URL = process.env.SMARTLEAD_BASE_URL || 'https://server.smartlead.ai/api/v1/';

/**
 * GET: Fetch all campaigns from Smartlead
 */
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const nameFilter = url.searchParams.get('name');
    
    // Build API URL with API key
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns?api_key=${SMARTLEAD_API_KEY}`;

    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log(`API URL: ${apiUrl}`);
      console.error(`Error fetching campaigns: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch campaigns: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    // Filter campaigns by name if filter is provided
    let campaigns = Array.isArray(data) ? data : [];
    
    if (nameFilter && nameFilter.trim() !== '') {
      const filter = nameFilter.toLowerCase();
      campaigns = campaigns.filter(campaign => 
        campaign.name && campaign.name.toLowerCase().includes(filter)
      );
    }
    
    return NextResponse.json({ campaigns });
  } catch (error) {
    console.error('Error in campaigns API:', error);
    return NextResponse.json(
      { error: `Error fetching campaigns: ${(error as Error).message}` },
      { status: 500 }
    );
  }
}

/**
 * POST: Create a new campaign in Smartlead
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, client_id } = body;
    
    if (!name) {
      return NextResponse.json(
        { error: 'Campaign name is required' },
        { status: 400 }
      );
    }
    
    // Build API URL with API key
    const apiUrl = `${SMARTLEAD_BASE_URL}campaigns/create?api_key=${SMARTLEAD_API_KEY}`;
    
    const payload = {
      name,
      client_id: client_id || null
    };
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error creating campaign: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to create campaign: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in create campaign API:', error);
    return NextResponse.json(
      { error: `Error creating campaign: ${(error as Error).message}` },
      { status: 500 }
    );
  }
} 