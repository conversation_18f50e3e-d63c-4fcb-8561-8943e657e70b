# Processing System Documentation

## Overview

The Processing System is a comprehensive TypeScript-based pipeline that automates contact and company data processing. It migrates functionality from Python scripts to a unified dashboard with scheduling, monitoring, and manual execution capabilities.

## Architecture

### Core Components

1. **BaseProcessor** - Abstract base class for all processors
2. **ProcessorScheduler** - Manages scheduled and manual job execution
3. **Individual Processors** - Specialized processors for each stage
4. **API Integration** - REST endpoints for triggering and monitoring
5. **Dashboard UI** - Beautiful interface for management and monitoring

### Processing Pipeline

#### Contact Processing Flow
```
email_unverified → email_verified → osint_completed → classification_completed → email_generated → email_sent
```

#### Company Processing Flow
```
website_unprocessed → website_scraped → overview_completed
```

## Processors

### 1. EmailValidatorProcessor
- **Purpose**: Validates email addresses for deliverability
- **Input State**: `email_unverified`
- **Output State**: `email_verified`
- **Features**: 
  - Email format validation
  - Domain verification
  - Deliverability checking

### 2. CompanyEnrichmentProcessor
- **Purpose**: Enriches company data with additional information
- **Input State**: `website_unprocessed`
- **Output State**: `website_scraped`
- **Features**:
  - Website content scraping
  - Company information extraction
  - Data normalization

### 3. CompanyOverviewProcessor
- **Purpose**: Extracts structured business information from company content
- **Input State**: `website_scraped`
- **Output State**: `overview_completed`
- **AI Features**:
  - Company type classification
  - Business model analysis
  - Investment focus extraction
  - Financial metrics identification

### 4. ContactSearchProcessor
- **Purpose**: Performs OSINT research on contacts
- **Input State**: `email_verified`
- **Output State**: `osint_completed`
- **AI Features**:
  - Professional profile generation
  - Achievement identification
  - Conversation hook creation
  - Background research

### 5. ContactClassificationProcessor
- **Purpose**: Classifies contacts into business categories
- **Input State**: `osint_completed`
- **Output State**: `classification_completed`
- **AI Features**:
  - Capital Source identification
  - Sponsor classification
  - Service provider detection
  - Confidence scoring

### 6. EmailGenerationProcessor
- **Purpose**: Generates personalized outreach emails
- **Input State**: `classification_completed`
- **Output State**: `email_generated`
- **AI Features**:
  - Personalization using all collected data
  - Context-aware messaging
  - Professional tone optimization
  - Call-to-action generation

## Prompt Management

All AI prompts are organized in the `/src/lib/prompts/` directory:

- `company-overview.ts` - Company analysis prompts
- `contact-classification.ts` - Contact classification prompts
- `contact-search.ts` - OSINT research prompts
- `email-generation.ts` - Email generation prompts
- `index.ts` - Centralized exports

### Prompt Structure
Each prompt file contains:
- **System Prompt**: Defines the AI's role and capabilities
- **User Template**: Function that formats input data into prompts

## API Endpoints

### Manual Processing
```typescript
POST /api/processing/trigger
{
  "action": "execute_manual",
  "stage": "email_validation",
  "options": {
    "limit": 50,        // Batch size
    "singleId": 123     // Process single entity
  }
}
```

### Scheduled Job Management
```typescript
POST /api/processing/trigger
{
  "action": "toggle_scheduled_job",
  "options": {
    "jobId": "email_validation_job",
    "enabled": true
  }
}
```

### Statistics
```typescript
GET /api/processing/stats
// Returns processing pipeline statistics
```

### Job Status
```typescript
GET /api/processing/trigger?action=jobs
// Returns all scheduled jobs and their status
```

## Dashboard Features

### Manual Processing Tab
- **Batch Processing**: Process multiple entities with configurable limits
- **Real-time Status**: Live updates on processing progress
- **Stage-specific Controls**: Individual triggers for each processing stage
- **Visual Indicators**: Color-coded stages with progress tracking

### Scheduled Jobs Tab
- **Job Management**: Enable/disable scheduled jobs
- **Status Monitoring**: View job schedules and last run times
- **Real-time Updates**: Live status of running jobs

### Single Entity Tab
- **Targeted Processing**: Process specific contacts or companies by ID
- **Stage Selection**: Choose which processing stage to execute
- **Immediate Execution**: Direct processing without queuing

### Statistics Overview
- **Pipeline Metrics**: Real-time counts for each processing stage
- **Progress Tracking**: Visual progress bars and percentages
- **Performance Monitoring**: Processing rates and completion stats

## Scheduling

The ProcessorScheduler manages automatic job execution:

```typescript
// Email validation every 30 minutes
'*/30 * * * *' - EmailValidatorProcessor

// Company enrichment every 2 hours
'0 */2 * * *' - CompanyEnrichmentProcessor

// Company overview every 2 hours
'0 */2 * * *' - CompanyOverviewProcessor

// Contact search every hour
'0 * * * *' - ContactSearchProcessor

// Contact classification every hour
'0 * * * *' - ContactClassificationProcessor

// Email generation every 2 hours
'0 */2 * * *' - EmailGenerationProcessor
```

## Database Integration

### Contact States
- `email_unverified` - New contacts needing email validation
- `email_verified` - Contacts with validated emails
- `osint_completed` - Contacts with completed research
- `classification_completed` - Contacts with assigned categories
- `email_generated` - Contacts with generated emails
- `email_sent` - Contacts with sent emails

### Company States
- `website_unprocessed` - Companies needing website processing
- `website_scraped` - Companies with scraped content
- `overview_completed` - Companies with extracted overviews

### Data Tables
- `contacts` - Main contact information and processing state
- `companies` - Company information and processing state
- `contact_searched_data` - OSINT research results
- `contact_achievements` - Contact achievements
- `contact_hooks` - Conversation starters
- `company_extracted_data` - Structured company information
- `generated_emails` - Generated email content

## Error Handling

### Processor-Level
- Automatic retry mechanisms
- Error logging and tracking
- Graceful failure handling
- State preservation on errors

### System-Level
- Rate limiting for API calls
- Connection pooling for database
- Memory management for large batches
- Timeout handling for long operations

## Configuration

### Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_key

# Database Configuration
DATABASE_URL=******************************

# Email Sender Configuration (for email generation)
SENDER_NAME="Your Name"
SENDER_COMPANY="Your Company"
SENDER_TITLE="Your Title"

# Processing Configuration
PROCESSING_BATCH_SIZE=50
PROCESSING_TIMEOUT=300000
```

### Processor Options
```typescript
interface ProcessorOptions {
  batchSize?: number      // Default batch size
  timeout?: number        // Processing timeout
  retryAttempts?: number  // Retry attempts on failure
  logLevel?: string       // Logging verbosity
}
```

## Usage Examples

### Manual Batch Processing
```typescript
// Process 100 contacts for email validation
await executeJob('email_validation', { limit: 100 })

// Process single contact by ID
await executeJob('contact_search', { singleId: 12345 })
```

### Programmatic Access
```typescript
import { processorScheduler } from './ProcessorScheduler'

// Start the scheduler
processorScheduler.start()

// Execute manual job
const result = await processorScheduler.executeManualJob(
  'email_generation',
  { limit: 25 }
)

// Get job status
const jobs = processorScheduler.getJobs()
```

## Monitoring and Analytics

### Real-time Metrics
- Processing rates per stage
- Success/failure ratios
- Queue depths and wait times
- Resource utilization

### Historical Analytics
- Daily/weekly processing volumes
- Performance trends
- Error patterns
- Completion rates

## Best Practices

### Performance Optimization
- Use appropriate batch sizes (50-100 entities)
- Monitor API rate limits
- Implement connection pooling
- Cache frequently accessed data

### Error Management
- Log all errors with context
- Implement exponential backoff
- Monitor error rates
- Set up alerting for failures

### Data Quality
- Validate input data before processing
- Implement data consistency checks
- Monitor output quality
- Regular data audits

## Troubleshooting

### Common Issues
1. **Rate Limiting**: Reduce batch sizes or increase delays
2. **Memory Issues**: Process smaller batches
3. **Database Timeouts**: Optimize queries and use connection pooling
4. **API Failures**: Implement retry logic and error handling

### Debugging
- Check processor logs for detailed error information
- Monitor database query performance
- Verify API key validity and quotas
- Test individual processors in isolation

## Future Enhancements

### Planned Features
- Advanced analytics dashboard
- Custom prompt management UI
- A/B testing for email generation
- Integration with external CRM systems
- Advanced scheduling options
- Performance optimization tools

### Scalability Improvements
- Distributed processing support
- Queue-based architecture
- Horizontal scaling capabilities
- Advanced caching strategies 