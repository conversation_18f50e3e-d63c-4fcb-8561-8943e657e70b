// Processing State Management Types

export type ContactProcessingState = 
  | 'email_unverified'
  | 'email_verified'
  | 'osint_pending'
  | 'osint_completed'
  | 'classification_pending'
  | 'classification_completed'
  | 'email_generation_pending'
  | 'email_generated'
  | 'email_sent'
  | 'failed'
  | 'error';

export type CompanyProcessingState = 
  | 'website_unprocessed'
  | 'website_scraped'
  | 'overview_pending'
  | 'overview_completed'
  | 'failed'
  | 'error';

// Individual processing stage status
export type ProcessingStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'error';

export type ProcessingStage = 
  | 'email_validation'
  | 'company_overview'
  | 'company_web_crawler'
  | 'contact_search'
  | 'contact_overview'
  | 'contact_classification'
  | 'email_generation';

export interface ProcessingJob {
  id: string;
  type: ProcessingStage;
  entity_type: 'contact' | 'company';
  entity_id: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  metadata?: Record<string, any>;
  progress?: number;
}

// Status-based stats using database columns
export interface StageStats {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  error: number;
}

export interface ProcessingStats {
  contacts: {
    email_verification: StageStats;
    osint_research: StageStats;
    overview_extraction: StageStats;
    classification: StageStats;
    email_generation: StageStats;
    email_sending: StageStats;
  };
  companies: {
    website_scraping: StageStats;
    company_overview: StageStats;
  };
  totals: {
    total_contacts: number;
    total_companies: number;
  };
  errors: {
    contact_errors: ProcessingError[];
    company_errors: ProcessingError[];
  };
  metadata?: {
    statsSource: string;
    lastUpdated: string;
    queryInfo?: string;
    explanations?: {
      contacts: Record<string, string>;
      companies: Record<string, string>;
    };
  };
}

export interface ProcessingError {
  entity_type: 'contact' | 'company';
  entity_id: number;
  stage: ProcessingStage;
  error_message: string;
  occurred_at?: string;
  retry_count?: number;
}

export interface TimelineEntry {
  date: string;
  stage: ProcessingStage;
  entity_type: 'contact' | 'company';
  processed_count: number;
  failed_count: number;
}

export interface ProcessingFilter {
  entity_type?: 'contact' | 'company' | 'all';
  state?: string;
  date_range?: {
    start: string;
    end: string;
  };
  stage?: ProcessingStage;
  status?: ProcessingJob['status'];
}

export interface ProcessingConfig {
  batch_size: number;
  concurrency: number;
  retry_attempts: number;
  enabled_stages: ProcessingStage[];
  auto_progression: boolean;
}

// Contact with processing information
export interface ContactWithProcessing {
  contact_id: number;
  first_name: string;
  last_name: string;
  email: string;
  company_name: string;
  processing_state: ContactProcessingState;
  email_verification_status: ProcessingStatus;
  email_verification_error?: string;
  osint_status: ProcessingStatus;
  osint_error?: string;
  overview_extraction_status: ProcessingStatus;
  overview_extraction_error?: string;
  classification_status: ProcessingStatus;
  classification_error?: string;
  email_generation_status: ProcessingStatus;
  email_generation_error?: string;
  email_sending_status: ProcessingStatus;
  email_sending_error?: string;
  processing_error_count: number;
  last_processed_at?: string;
  can_advance: boolean;
}

// Company with processing information  
export interface CompanyWithProcessing {
  company_id: number;
  company_name: string;
  company_website: string;
  processing_state: CompanyProcessingState;
  website_scraping_status: ProcessingStatus;
  website_scraping_error?: string;
  company_overview_status: ProcessingStatus;
  company_overview_error?: string;
  processing_error_count: number;
  last_processed_at?: string;
  can_advance: boolean;
  contact_count: number;
}

// API Response types
export interface ProcessingResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
} 