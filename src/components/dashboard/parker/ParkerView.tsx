'use client';

import React from 'react';
import { useLivekitToken } from '@/hooks/UseLivekitToken';
import { FloatingAssistant } from '@/components/FloatingAssistant';

const ParkerView = () => {
  const livekitConfig = useLivekitToken();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold"><PERSON>, Senior Associate AI Agent</h1>
        </div>
      </div>

      {livekitConfig && (
        <FloatingAssistant
          token={livekitConfig.token}
          serverUrl={livekitConfig.serverUrl}
          showFloatingButton={true}
        />
      )}
    </div>
  );
};

export default ParkerView; 