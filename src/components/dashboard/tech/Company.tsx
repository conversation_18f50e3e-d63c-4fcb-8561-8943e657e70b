"use client"

import React from 'react';
import { 
  Building2, 
  Target, 
  Database,
  Activity,
  GitBranch,
  FileText,
  MessageSquare,
  Workflow,
  Users,
  MailIcon,
  PhoneIcon,
  Star,
  Code,
  Calendar,
  LucideIcon
} from 'lucide-react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

interface CardSectionProps {
  children: React.ReactNode;
}

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary';
  className?: string;
}

interface Stakeholder {
  name: string;
  initials: string;
  title: string;
  role: 'Primary' | 'Secondary';
  email: string;
  phone: string;
  address: string;
  responsibilities: string;
  areas: string[];
}

interface StakeholderCardProps {
  stakeholder: Stakeholder;
}

interface Meeting {
  name: string;
  schedule: string;
  type: string;
  icon: LucideIcon;
}

interface KeyStatistic {
  label: string;
  value: string;
}

const Card: React.FC<CardProps> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<CardSectionProps> = ({ children }) => (
  <div className="border-b border-gray-200 p-4 sm:p-6">{children}</div>
);

const CardContent: React.FC<CardSectionProps> = ({ children }) => (
  <div className="p-4 sm:p-6">{children}</div>
);

const Badge: React.FC<BadgeProps> = ({ children, variant = 'default', className = '' }) => {
  const variants = {
    default: 'bg-blue-100 text-blue-700',
    secondary: 'bg-gray-100 text-gray-700',
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}>
      {children}
    </span>
  );
};

const StakeholderCard: React.FC<StakeholderCardProps> = ({ stakeholder }) => {
  return (
    <Card>
      <CardContent>
        <div className="flex items-start gap-4">
          <div className="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
            <span className="text-sm font-medium text-white">{stakeholder.initials}</span>
          </div>
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-base font-semibold text-gray-900">{stakeholder.name}</h3>
                <p className="text-sm text-gray-500">{stakeholder.title}</p>
              </div>
              <Badge variant={stakeholder.role === 'Primary' ? 'default' : 'secondary'}>
                {stakeholder.role}
              </Badge>
            </div>
            <div className="mt-2 space-y-1">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <MailIcon className="h-4 w-4" />
                <span>{stakeholder.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <PhoneIcon className="h-4 w-4" />
                <span>{stakeholder.phone}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Building2 className="h-4 w-4" />
                <span>{stakeholder.address}</span>
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm text-gray-600">{stakeholder.responsibilities}</p>
            </div>
            <div className="mt-3 flex flex-wrap gap-2">
              {stakeholder.areas.map((area, idx) => (
                <Badge key={idx} variant="secondary">{area}</Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 

const Company: React.FC = () => {
    const stakeholders: Stakeholder[] = [
      {
        name: "Eric Brody",
        initials: "EB",
        title: "Founder & CEO",
        role: "Primary",
        email: "<EMAIL>",
        phone: "+****************",
        address: "105 Madison Ave, 2nd Floor, New York, NY",
        responsibilities: "Project sponsor and final decision maker. Oversees strategic direction, business development, and client relationships.",
        areas: ["Executive Sponsor", "Strategy", "Business Development", "Deal Flow"]
      },
      {
        name: "Marc Dupiton",
        initials: "MD",
        title: "Head of Technology & Operations",
        role: "Primary",
        email: "<EMAIL>",
        phone: "+****************",
        address: "105 Madison Ave, 2nd Floor, New York, NY",
        responsibilities: "Leads technical implementation, infrastructure development, and operational processes. Primary point of contact for system integration.",
        areas: ["Technical Lead", "Infrastructure", "Operations", "Data Architecture"]
      }
    ];
  
    const meetings: Meeting[] = [
      {
        name: "Weekly Status Update",
        schedule: "Every Monday",
        type: "Operational",
        icon: MessageSquare
      },
      {
        name: "Technical Implementation",
        schedule: "Every Wednesday",
        type: "Technical",
        icon: Code
      },
      {
        name: "Executive Review",
        schedule: "Monthly",
        type: "Strategic",
        icon: Star
      }
    ];
  
    const keyStats: KeyStatistic[] = [
      { label: "Founded", value: "2022" },
      { label: "Location", value: "New York, NY" },
      { label: "Team Size", value: "2 employees" },
      { label: "Revenue", value: "< $1M" }
    ];
  
    return (
      <div className="space-y-6">
        {/* Company Summary */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-gray-400" />
              <h3 className="text-base font-semibold text-gray-900">Company Summary</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Business Overview</h4>
                  <p className="text-sm text-gray-600">
                    Anax Capital Advisory is a boutique capital advisory firm specializing in connecting real estate 
                    developers and owners with appropriate financing sources across the capital stack. The company 
                    leverages technology and industry expertise to facilitate efficient deal matching and execution.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Core Services</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Capital sourcing and placement</li>
                    <li>• Deal structuring and advisory</li>
                    <li>• Debt and equity arrangement</li>
                    <li>• Market intelligence and analytics</li>
                  </ul>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Key Statistics</h4>
                  <dl className="grid grid-cols-2 gap-4 text-sm">
                    {keyStats.map((stat, idx) => (
                      <div key={idx}>
                        <dt className="text-gray-500">{stat.label}</dt>
                        <dd className="font-medium text-gray-900">{stat.value}</dd>
                      </div>
                    ))}
                  </dl>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Project Stakeholders</h2>
          <p className="mt-1 text-sm text-gray-500">
            Key team members and communication channels
          </p>
        </div>
  
        {/* Stakeholder Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {stakeholders.map((stakeholder, idx) => (
            <StakeholderCard key={idx} stakeholder={stakeholder} />
          ))}
        </div>
  
        {/* Communication Channels */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-gray-400" />
              <h3 className="text-base font-semibold text-gray-900">Communication Channels</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="divide-y divide-gray-100">
              {meetings.map((meeting, idx) => (
                <div key={idx} className={`${idx > 0 ? 'pt-4' : ''} ${idx < meetings.length - 1 ? 'pb-4' : ''}`}>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-50">
                        <meeting.icon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{meeting.name}</p>
                        <p className="text-sm text-gray-500">{meeting.schedule}</p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="sm:ml-auto self-start sm:self-auto">
                      {meeting.type}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
  
        {/* Business Processes */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Workflow className="h-5 w-5 text-gray-400" />
              <h3 className="text-base font-semibold text-gray-900">Core Business Processes</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Deal Sourcing & Origination</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Market research and lead generation</li>
                  <li>• Network development and relationship management</li>
                  <li>• Deal qualification and initial assessment</li>
                  <li>• Client onboarding and documentation</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Capital Matching</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Investor/lender database management</li>
                  <li>• Deal packaging and marketing</li>
                  <li>• Term sheet negotiation</li>
                  <li>• Due diligence coordination</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
  
        {/* Systems & Data */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5 text-gray-400" />
              <h3 className="text-base font-semibold text-gray-900">Systems & Data Infrastructure</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Current Systems</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Custom Excel-based deal tracking</li>
                  <li>• Basic CRM implementation</li>
                  <li>• Email marketing platform</li>
                  <li>• Document management system</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Data Assets</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 35,000+ industry contacts</li>
                  <li>• Historical deal database</li>
                  <li>• Market research reports</li>
                  <li>• Property and transaction records</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
  
        {/* Pain Points & Goals */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-gray-400" />
              <h3 className="text-base font-semibold text-gray-900">Implementation Goals</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Current Pain Points</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Manual deal matching process</li>
                  <li>• Inefficient contact management</li>
                  <li>• Limited market intelligence capabilities</li>
                  <li>• Scalability constraints</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Key Objectives</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Automate deal sourcing and matching</li>
                  <li>• Implement AI-driven market intelligence</li>
                  <li>• Develop scalable contact management</li>
                  <li>• Create efficient data analysis pipeline</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };
  
  export { Company };
  export default Company;