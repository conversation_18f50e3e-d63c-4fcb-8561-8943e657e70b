"use client"

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import CampaignsTab from './CampaignsTab';
import LeadsTab from './LeadsTab';
import MessageHistoryTab from './MessageHistoryTab';
import { CampaignStats } from './CampaignStats';
import ContactDetail from '../people/ContactDetail';

const SmartleadView: React.FC = () => {
  const [activeTab, setActiveTab] = useState('campaigns');
  const [selectedContactId, setSelectedContactId] = useState<number | null>(null);

  const handleContactSelect = (contactId: number) => {
    setSelectedContactId(contactId);
  };

  const handleBackToSmartlead = () => {
    setSelectedContactId(null);
  };

  // Show ContactDetail if a contact is selected
  if (selectedContactId !== null) {
    return (
      <ContactDetail 
        contactId={selectedContactId} 
        onBack={handleBackToSmartlead} 
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Smartlead</h1>
      </div>
      
      <Tabs defaultValue="campaigns" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 w-[600px]">
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="leads">Leads</TabsTrigger>
          <TabsTrigger value="stats">Campaign Stats</TabsTrigger>
          <TabsTrigger value="messages">Message History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="campaigns">
          <CampaignsTab />
        </TabsContent>
        
        <TabsContent value="leads">
          <LeadsTab onContactSelect={handleContactSelect} />
        </TabsContent>
        
        <TabsContent value="stats">
          <CampaignStats onContactSelect={handleContactSelect} />
        </TabsContent>
        
        <TabsContent value="messages">
          <MessageHistoryTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SmartleadView; 