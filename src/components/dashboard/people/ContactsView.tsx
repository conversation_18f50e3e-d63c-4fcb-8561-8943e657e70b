import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Search, Building2, UserPlus, MapPin, Mail, Link, SortAsc, ArrowDown, ArrowUp, CheckCircle, XCircle, AlertCircle, Database, CheckCircle2, Send, RefreshCw } from 'lucide-react';
import ContactDetail from './ContactDetail';
import AddContact from './AddContact';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import BatchSyncButton from './BatchSyncButton';

// Simple debounce implementation
const debounce = (fn: Function, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

// Add proper typing for contacts
interface Contact {
  contact_id: number;  // Changed from string to number to properly handle numeric IDs
  first_name: string;
  last_name: string;
  full_name: string;
  job_title: string; // This is mapped from 'title' in the API
  headline: string;
  seniority: string;
  email: string;
  personal_email: string;
  email_status: string;
  linkedin_url: string;
  contact_city: string;
  contact_state: string;
  contact_country: string;
  company_name: string;
  company_city: string;
  company_state: string;
  company_website: string;
  industry: string;
  category?: string; // Added category field which may be undefined
  source: string;
  extracted: boolean;
  searched: boolean;
  email_generated: boolean;
  smartlead_lead_id?: string;
  smartlead_status?: string;
}

const ContactsView = () => {
  const [selectedContact, setSelectedContact] = useState<number | null>(null);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddContact, setShowAddContact] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(50);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [contactType, setContactType] = useState('all');
  const ALL_SOURCES_VALUE = 'all';
  const [sources, setSources] = useState<{source:string,count:number}[]>([]);
  const [selectedSource, setSelectedSource] = useState<string>(ALL_SOURCES_VALUE);
  
  // Add sort state
  const [sortField, setSortField] = useState<string>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Add state for extraction filter
  const [showExtractedOnly, setShowExtractedOnly] = useState<boolean>(false);

  // Add filters state
  const [filters, setFilters] = useState({
    emailGenerated: false,
    smartleadSynced: false
  });

  // Add new state for contact selection and syncing
  const [selectedContactIds, setSelectedContactIds] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Add state for campaigns and selected campaign
  const [smartleadCampaigns, setSmartleadCampaigns] = useState<{id: string, name: string}[]>([]);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('all');

  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setDebouncedSearchTerm(term);
    }, 300),
    []
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  // Fetch sources on mount
  useEffect(()=>{
    fetch('/api/contacts/sources')
      .then(res=> {
        if (!res.ok) {
          throw new Error(`Failed to fetch sources: ${res.status}`);
        }
        return res.json();
      })
      .then(data => {
        console.log('Fetched sources:', data);
        setSources(Array.isArray(data) ? data : []);
        setLoading(false);
      })
      .catch(err => {
        console.error('Error fetching sources:', err);
        setSources([]);
        setLoading(false);
      });
  },[]);

  // Fetch campaigns from Smartlead API on mount
  useEffect(() => {
    fetch('/api/smartlead/campaigns')
      .then(res => res.json())
      .then(data => setSmartleadCampaigns(data.campaigns || []))
      .catch(err => {
        setSmartleadCampaigns([]);
        toast.error('Failed to fetch campaigns');
      });
  }, []);

  useEffect(() => {
    const fetchContacts = async () => {
      try {
        setLoading(true);
        let url = '';
        // Always include pagination and sorting params
        const baseParams = `page=${currentPage}&limit=${pageSize}&sort=${sortField}&direction=${sortDirection}`;
        if (selectedCampaignId !== 'all') {
          url = `/api/contacts?campaign_id=${selectedCampaignId}&${baseParams}`;
        } else {
          url = `/api/contacts?${baseParams}`;
          if (debouncedSearchTerm) url += `&search=${encodeURIComponent(debouncedSearchTerm)}`;
          if (showExtractedOnly) url += '&extracted_only=true';
          if (contactType !== 'all') url += `&type=${contactType}`;
          if (selectedSource !== ALL_SOURCES_VALUE) url += `&source=${encodeURIComponent(selectedSource)}`;
          if (filters.emailGenerated) url += '&email_generated=true';
          if (filters.smartleadSynced) url += '&smartlead_lead_id=not_null';
        }
        const response = await fetch(url);
        if (!response.ok) throw new Error(`Failed to fetch contacts: ${response.status}`);
        const data = await response.json();
        const contactsArray = data.contacts || [];
        setContacts(contactsArray);
        const count = typeof data.totalCount === 'string' ? parseInt(data.totalCount, 10) : (data.totalCount || 0);
        setTotalCount(count);
        setTotalPages(data.totalPages || 1);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchContacts();
  }, [currentPage, pageSize, debouncedSearchTerm, sortField, sortDirection, showExtractedOnly, contactType, selectedSource, filters, selectedCampaignId]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    setLoading(true);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, set default direction
      setSortField(field);
      setSortDirection('asc');
    }
    // Reset to first page when sort changes
    setCurrentPage(1);
  };

  // Handler for individual contact selection
  const handleContactSelection = (contactId: number, event: React.MouseEvent) => {
    // Prevent card click from triggering when clicking the checkbox
    event.stopPropagation();
    
    setSelectedContactIds(prevSelected => {
      const isCurrentlySelected = prevSelected.includes(contactId);
      if (isCurrentlySelected) {
        return prevSelected.filter(id => id !== contactId);
      } else {
        return [...prevSelected, contactId];
      }
    });
  };

  // Handler for select all contacts
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedContactIds([]);
    } else {
      const allContactIds = contacts.map(contact => contact.contact_id);
      setSelectedContactIds(allContactIds);
    }
    setSelectAll(!selectAll);
  };

  // Handler to sync selected contacts to Smartlead
  const handleBatchSync = async () => {
    if (selectedContactIds.length === 0) {
      toast.error("Please select at least one contact to sync");
      return;
    }

    try {
      setIsSyncing(true);
      const response = await fetch('/api/smartlead/contacts/batch-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactIds: selectedContactIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed with status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Successfully synced ${result.syncedCount} contacts to Smartlead`);
        // Refresh the contacts list to show updated sync status
        setCurrentPage(1);
        // Clear selection
        setSelectedContactIds([]);
        setSelectAll(false);
      } else {
        toast.error(result.message || 'Failed to sync contacts to Smartlead');
      }
    } catch (error) {
      console.error('Error syncing contacts:', error);
      toast.error(`Failed to sync contacts: ${(error as Error).message}`);
    } finally {
      setIsSyncing(false);
    }
  };

  // Handler for sync completion
  const handleSyncComplete = () => {
    // Clear selection
    setSelectedContactIds([]);
    setSelectAll(false);
    // Refresh the contacts list
    setCurrentPage(1);
  };

  // Handler for checkbox selection (for inline checkbox)
  const handleCheckboxChange = (contactId: number, checked: boolean) => {
    setSelectedContactIds(prevSelected => {
      if (checked) {
        return [...prevSelected, contactId];
      } else {
        return prevSelected.filter(id => id !== contactId);
      }
    });
  };

  if (showAddContact) {
    return <AddContact onBack={() => setShowAddContact(false)} />;
  }

  if (selectedContact) {
    return <ContactDetail 
      contactId={selectedContact}
      onBack={() => setSelectedContact(null)} 
    />;
  }

  // Function to get initials from full name
  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  // Function to get category letter for the avatar
  const getCategoryLetter = (contact: Contact) => {
    // Use the category field if available
    if (contact.category && contact.category.length > 0) {
      return contact.category.charAt(0).toUpperCase();
    }
    // Fallback to industry if category is not available
    else if (contact.industry && contact.industry.length > 0) {
      return contact.industry.charAt(0).toUpperCase();
    }
    // Use first letter of first name as a last resort
    else {
      return contact.first_name.charAt(0).toUpperCase();
    }
  };

  // Helper function to get state abbreviation
  const getStateAbbreviation = (stateName: string): string => {
    const stateMap: {[key: string]: string} = {
      'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
      'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
      'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
      'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
      'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
      'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
      'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
      'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
      'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
      'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
    };
    
    // Check if the input is already an abbreviation (2 characters)
    if (stateName.length === 2 && stateName === stateName.toUpperCase()) {
      return stateName;
    }
    
    return stateMap[stateName.toLowerCase()] || stateName;
  };

  // Helper function to determine email status icon
  const getEmailStatusIcon = (status: string | null): React.ReactNode => {
    if (!status) return null;
    
    status = status.toLowerCase();
    
    if (status === 'valid' || status === 'verified') {
      return <CheckCircle className="h-4 w-4 text-green-500 ml-2" />;
    } else if (status === 'invalid' || status === 'bounced' || status === 'failed') {
      return <XCircle className="h-4 w-4 text-red-500 ml-2" />;
    } else if (status === 'pending' || status === 'unverified') {
      return <AlertCircle className="h-4 w-4 text-amber-500 ml-2" />;
    }
    
    return null;
  };

  return (
    <div className="flex w-full">
      {/* Left Sidebar */}
      <div className="w-72 border-r bg-white p-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium">Search & Filter</h2>
          <Button 
            onClick={() => setShowAddContact(true)} 
            size="sm" 
            variant="outline" 
            className="flex items-center"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Contact
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search contacts..." 
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-10 h-9"
          />
        </div>

        {/* Contact Type Filter */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Contact Type</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="contactType"
                checked={contactType === 'all'}
                onChange={() => setContactType('all')}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Show All</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="contactType"
                checked={contactType === 'investors'}
                onChange={() => setContactType('investors')}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Investors</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="contactType"
                checked={contactType === 'sponsors'}
                onChange={() => setContactType('sponsors')}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Sponsors</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="contactType"
                checked={contactType === 'thirdparty'}
                onChange={() => setContactType('thirdparty')}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Third Party</span>
            </label>
          </div>
        </div>

        {/* Extraction Status Filters - Simplified */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Extraction Status</h3>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="extractionFilter"
                checked={!showExtractedOnly && !filters.emailGenerated && !filters.smartleadSynced}
                onChange={() => {
                  setShowExtractedOnly(false);
                  setFilters({ emailGenerated: false, smartleadSynced: false });
                }}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Show All Contacts</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="extractionFilter"
                checked={showExtractedOnly && !filters.emailGenerated && !filters.smartleadSynced}
                onChange={() => {
                  setShowExtractedOnly(true);
                  setFilters({ emailGenerated: false, smartleadSynced: false });
                }}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Extracted Contacts Only</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="extractionFilter"
                checked={!showExtractedOnly && filters.emailGenerated && !filters.smartleadSynced}
                onChange={() => {
                  setShowExtractedOnly(false);
                  setFilters({ emailGenerated: true, smartleadSynced: false });
                }}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Email Generated Only</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="extractionFilter"
                checked={!showExtractedOnly && !filters.emailGenerated && filters.smartleadSynced}
                onChange={() => {
                  setShowExtractedOnly(false);
                  setFilters({ emailGenerated: false, smartleadSynced: true });
                }}
                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <span className="text-sm">Smartlead Synced Only</span>
            </label>
          </div>
        </div>

        {/* Add Sorting Options */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Sort By</h3>
          <Select 
            value={sortField} 
            onValueChange={(value) => {
              setSortField(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-full h-9">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="updated_at">Most Recent</SelectItem>
              <SelectItem value="full_name">Name</SelectItem>
              <SelectItem value="company_name">Company</SelectItem>
              <SelectItem value="source">Source</SelectItem>
              <SelectItem value="email_status">Email Status</SelectItem>
              <SelectItem value="extracted">Recently Extracted</SelectItem>
              <SelectItem value="searched">Recently Searched</SelectItem>
              <SelectItem value="email_generated">Recently Email Generated</SelectItem>
              <SelectItem value="smartlead_lead_id">Recently Synced to Smartlead</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex items-center mt-2 gap-2">
            <Button 
              variant={sortDirection === 'asc' ? 'default' : 'outline'} 
              size="sm"
              className="flex-1"
              onClick={() => setSortDirection('asc')}
            >
              <ArrowUp className="h-4 w-4 mr-1" />
              Ascending
            </Button>
            <Button 
              variant={sortDirection === 'desc' ? 'default' : 'outline'} 
              size="sm"
              className="flex-1"
              onClick={() => setSortDirection('desc')}
            >
              <ArrowDown className="h-4 w-4 mr-1" />
              Descending
            </Button>
          </div>
        </div>

        {/* Source Filter */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3">Source</h3>
          <Select value={selectedSource} onValueChange={setSelectedSource}>
            <SelectTrigger className="w-full h-9">
              <SelectValue placeholder="All Sources" />
            </SelectTrigger>
            <SelectContent className="max-h-64 overflow-auto">
              <SelectItem key={ALL_SOURCES_VALUE} value={ALL_SOURCES_VALUE}>All Sources</SelectItem>
              {sources.map(src=> (
                <SelectItem key={src.source || 'unknown'} value={src.source || 'unknown'}>
                  {`${src.source || 'Unknown'} (${src.count})`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Campaign Filter */}
        <div className="mb-4 flex items-center gap-2">
          <label className="text-sm font-medium">Filter by Campaign:</label>
          <Select value={selectedCampaignId} onValueChange={setSelectedCampaignId}>
            <SelectTrigger className="w-64 h-9">
              <SelectValue placeholder="All Campaigns" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Campaigns</SelectItem>
              {smartleadCampaigns.map(c => (
                <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-500 mt-auto pt-4">
          {loading 
            ? 'Loading contacts...' 
            : contacts.length > 0 
              ? `Showing ${((currentPage - 1) * pageSize) + 1}-${Math.min(currentPage * pageSize, totalCount)} of ${totalCount.toLocaleString()} contacts`
              : `No contacts found`
          }
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 bg-gray-50 p-4">
        {/* Top action bar */}
        <div className="flex justify-between mb-4">
          <h2 className="text-xl font-semibold">Contacts</h2>
          
          {/* Sorting info */}
          <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Sorted by:</span>
            <span className="font-medium">
              {sortField === 'updated_at' ? 'Most Recent' : 
               sortField === 'full_name' ? 'Name' : 
               sortField === 'company_name' ? 'Company' : 
               sortField === 'email_status' ? 'Email Status' :
               sortField === 'extracted' ? 'Recently Extracted' :
               sortField === 'searched' ? 'Recently Searched' :
               sortField === 'email_generated' ? 'Recently Email Generated' :
               sortField === 'smartlead_lead_id' ? 'Recently Synced to Smartlead' :
               'Source'}
            </span>
            <span>{sortDirection === 'asc' ? '(A-Z)' : '(Z-A)'}</span>
          </div>
          </div>
        </div>
        
        {/* Selection controls */}
        <div className="flex items-center justify-between mb-4 p-3 bg-white rounded-lg shadow-sm">
          <div className="flex items-center gap-2">
            <input 
              type="checkbox" 
              id="select-all" 
              checked={selectedContactIds.length > 0 && selectedContactIds.length === contacts.length}
              onChange={handleSelectAll}
              className="rounded text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="select-all" className="text-sm font-medium text-gray-700">
              Select All {selectedContactIds.length > 0 && `(${selectedContactIds.length} selected)`}
            </label>
          </div>
          
          {selectedContactIds.length > 0 && (
            <BatchSyncButton 
              contactIds={selectedContactIds}
              onSyncComplete={handleSyncComplete}
            />
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          {loading ? (
            <div className="col-span-2 text-center py-10">Loading contacts...</div>
          ) : (
            contacts.map((contact) => {
              const initials = getInitials(contact.first_name + ' ' + contact.last_name);
              const fullName = contact.full_name || `${contact.first_name} ${contact.last_name}`;
              
              // Format state abbreviation if in the US
              const stateAbbr = contact.contact_state ? getStateAbbreviation(contact.contact_state) : '';
              
              // Format location - don't show "United States"
              let location = '';
              if (contact.contact_city) {
                location += contact.contact_city;
              }
              
              if (stateAbbr) {
                if (location) location += ', ';
                location += stateAbbr;
              }
              
              if (contact.contact_country && contact.contact_country.toLowerCase() !== 'united states') {
                if (location) location += ', ';
                location += contact.contact_country;
              }
              
              // Use default if completely empty
              if (!location) {
                location = 'Location not specified';
              }
              
              const email = contact.email || contact.personal_email;
              
              // Check if contact can be synced (has validated email)
              const canSync = contact.email && contact.email_generated && !contact.smartlead_lead_id;
              const isSelected = selectedContactIds.includes(contact.contact_id);
              
              return (
                <Card 
                  key={contact.contact_id} 
                  className={`border ${selectedContactIds.includes(contact.contact_id) ? 'border-blue-500' : 'border-gray-200'} hover:border-gray-300 shadow-none transition-colors bg-white relative`}
                >
                  <CardContent 
                    className="p-3 pl-8 cursor-pointer"
                  onClick={() => {
                    // Log detailed contact information for debugging
                    console.log('Selected contact:', {
                      id: contact.contact_id,
                      type: typeof contact.contact_id,
                      name: `${contact.first_name} ${contact.last_name}`,
                      company: contact.company_name,
                      source: contact.source
                    });
                    
                    // Ensure contactId is a number before setting it
                    const contactId = typeof contact.contact_id === 'string' 
                      ? parseInt(contact.contact_id, 10) 
                      : contact.contact_id;
                    
                    setSelectedContact(contactId);
                  }}
                >
                    <div className="flex flex-col">
                      {/* Top section: Contact info */}
                      <div className="flex mb-2 items-start relative">
                        {/* Contact Details */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start gap-2">
                            <h3 className="text-base font-medium text-gray-900">{fullName}</h3>
                            <input 
                              type="checkbox"
                              checked={selectedContactIds.includes(contact.contact_id)}
                              onChange={e => handleCheckboxChange(contact.contact_id, e.target.checked)}
                              className="h-4 w-4 rounded border-gray-300 mt-0.5"
                            />
                          </div>
                          <div className="text-xs text-gray-700 mt-0.5 whitespace-nowrap overflow-hidden text-ellipsis">{contact.job_title || 'Partner'}</div>
                          <div className="text-sm font-semibold text-blue-600 mt-0.5 whitespace-nowrap overflow-hidden text-ellipsis">{contact.company_name}</div>
                          {location !== 'Location not specified' && (
                            <div className="text-xs text-gray-700 mt-0.5 flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span className="truncate max-w-[120px]">{location}</span>
                            </div>
                          )}
                        </div>

                        {/* Right side: Email and Location */}
                        <div className="text-right ml-2 flex-shrink-0 max-w-[180px]">
                          {email && (
                            <div className="text-xs text-gray-700 flex items-center justify-end">
                              <Mail className="h-3 w-3 mr-1 text-gray-400" />
                              <span>{email}</span>
                              {getEmailStatusIcon(contact.email_status)}
                            </div>
                          )}
                          <div className="text-xs text-gray-700 flex items-center justify-end mt-1">
                            <Link className="h-3 w-3 mr-1 text-gray-400" />
                            <span className="truncate max-w-[120px]">
                              {contact.company_website || 'No website available'}
                            </span>
                          </div>
                          <div className="mt-2 flex flex-col gap-1">
                            <div className="flex gap-1 justify-end">
                              {contact.extracted && (
                                <Badge className="bg-green-600 hover:bg-green-700 text-white border-0 flex items-center gap-1 px-2 py-0.5">
                                  <CheckCircle className="h-2.5 w-2.5" />
                                  Extracted
                                </Badge>
                              )}
                              {contact.searched && (
                                <Badge className="bg-blue-600 hover:bg-blue-700 text-white border-0 flex items-center gap-1 px-2 py-0.5">
                                  <Database className="h-2.5 w-2.5" />
                                  Searched
                                </Badge>
                              )}
                              {contact.email_status && (
                                <Badge className="bg-purple-600 hover:bg-purple-700 text-white border-0 flex items-center gap-1 px-2 py-0.5">
                                  <Mail className="h-2.5 w-2.5" />
                                  {contact.email_status}
                                </Badge>
                              )}
                            </div>
                            
                            {contact.smartlead_lead_id && (
                              <div className="flex justify-end gap-1 whitespace-nowrap">
                                <Badge className="bg-green-700 text-white border-0 flex items-center gap-1 px-2 py-0.5 whitespace-nowrap">
                                  <CheckCircle className="h-2.5 w-2.5" />
                                  Synced to Smartlead
                                </Badge>
                                {contact.email_generated && (
                                  <Badge className="bg-purple-600 hover:bg-purple-700 text-white border-0 flex items-center gap-1 px-2 py-0.5 whitespace-nowrap">
                                    <Mail className="h-2.5 w-2.5" />
                                    Email Generated
                                  </Badge>
                                )}
                              </div>
                            )}
                            {!contact.smartlead_lead_id && contact.email_generated && (
                              <div className="flex justify-end gap-1 whitespace-nowrap">
                                <Badge className="bg-purple-600 hover:bg-purple-700 text-white border-0 flex items-center gap-1 px-2 py-0.5 whitespace-nowrap">
                                  <Mail className="h-2.5 w-2.5" />
                                  Email Generated
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Divider */}
                      <div className="h-px bg-gray-100 my-3"></div>
                      
                      {/* Bottom section: Investment criteria */}
                      <div className="flex items-start">
                        {/* Category Avatar */}
                        <div className="mr-4 flex-shrink-0">
                          <Avatar className="h-12 w-12 bg-blue-50 text-blue-600 font-semibold">
                            <AvatarFallback className="text-lg">
                              {getCategoryLetter(contact)}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                        
                        {/* Investment Criteria */}
                        <div className="flex-1">
                          <div className="text-xs font-medium text-gray-700 mb-1">Investment Criteria:</div>
                          <div className="grid grid-cols-2 gap-x-8">
                            {/* Left side criteria */}
                            <div className="space-y-1">
                              <div className="text-xs text-gray-600">
                                <span className="font-medium">Capital Type:</span>
                              </div>
                              <div className="text-xs text-gray-600">
                                <span className="font-medium">Deal Size:</span>
                              </div>
                              <div className="text-xs text-gray-600">
                                <span className="font-medium">Property Type:</span>
                              </div>
                            </div>
                            
                            {/* Right side location criteria */}
                            <div className="space-y-1">
                              <div className="text-xs text-gray-600">
                                <span className="font-medium">Region:</span>
                              </div>
                              <div className="text-xs text-gray-600">
                                <span className="font-medium">State:</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-center space-x-2 mt-6">
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              size="sm"
            >
              Previous
            </Button>
            <div className="text-sm text-gray-500 px-4">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              size="sm"
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactsView;