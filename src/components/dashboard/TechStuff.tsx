"use client"

import React, { useState } from 'react'
import { Overview } from './tech/Overview'
import { Company } from './tech/Company'
import { Processes } from './tech/Processes'
import { Assessment } from './tech/Assessment'
import SequenceBuilder from './sequence/sequence-builder'
import <PERSON><PERSON>iew from './parker/ParkerView'

interface TechStuffProps {
  isActive: boolean;
}

export const TechStuff: React.FC<TechStuffProps> = ({ isActive }) => {
  const [activeTab, setActiveTab] = useState('team')
  
  if (!isActive) return null

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'company', name: 'Company' },
    { id: 'processes', name: 'Processes' },
    { id: 'assessment', name: 'Assessment' },
    { id: 'sequences', name: 'Sequences' },
    { id: 'parker', name: '<PERSON>' },
  ]

  return (
    <div className="space-y-6">
      <div className="flex space-x-4 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`pb-4 px-4 text-sm font-medium border-b-2 transition-all ${
              activeTab === tab.id 
                ? 'border-blue-600 text-blue-600' 
                : 'border-transparent hover:border-gray-200'
            }`}
          >
            {tab.name}
          </button>
        ))}
      </div>

      {activeTab === 'overview' && <Overview />}
      {activeTab === 'company' && <Company />}
      {activeTab === 'processes' && <Processes />}
      {activeTab === 'assessment' && <Assessment />}
      {activeTab === 'sequences' && <SequenceBuilder />}
      {activeTab === 'parker' && <ParkerView />}
    </div>
  )
}