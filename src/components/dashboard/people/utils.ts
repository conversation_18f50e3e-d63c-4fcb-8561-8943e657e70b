import { Contact } from './types';

/**
 * Replace variables in a template string with values from contact data
 * Supports both {variable} and {{variable}} formats.
 * For variables ending with 'body' or 'subject', uses dynamic values if provided.
 * @param template - The template string with placeholders
 * @param contact - The contact object containing replacement values
 * @param dynamic - Optional dynamic fields like subject, body, etc.
 * @returns - Template with variables replaced
 */
export function replaceVariables(
  template: string,
  contact: Contact | null | undefined,
  dynamic: Record<string, string> = {}
): string {
  if (!template) return '';
  let result = template;
  if (!contact) return result;

  // Build variables map
  const variables: Record<string, string> = {
    first_name: contact?.first_name || '',
    last_name: contact?.last_name || '',
    email: contact?.email || '',
    company_name: contact?.company_name || '',
    website: contact?.company_website || '',
    job_title: contact?.job_title || '',
    phone_number: contact?.phone_number || '',
    city: contact?.company_city || '',
    state: contact?.company_state || '',
    country: contact?.company_country || '',
    linkedin_url: (contact?.company_data?.scraped_contacts?.[0]?.linkedin_url as string) || '',
    subject: dynamic.subject || '',
    body: dynamic.body || '',
    html_body: dynamic.html_body || '',
    company: contact?.company_name || 'your company',
    industry: contact?.industry || 'real estate',
    ...dynamic // Add any additional dynamic fields
  };

  // Replace all {{variable}} and {variable} with the correct value
  result = result.replace(/{{\s*([\w.]+)\s*}}|{\s*([\w.]+)\s*}/g, (_match, doubleKey, singleKey) => {
    const key = doubleKey || singleKey;
    if (!key) return '';
    // If variable ends with 'body' or 'subject', prefer dynamic value
    if (/body$|subject$/i.test(key)) {
      return dynamic[key] ?? variables[key] ?? '';
    }
    return variables[key] ?? '';
  });

  return result;
} 