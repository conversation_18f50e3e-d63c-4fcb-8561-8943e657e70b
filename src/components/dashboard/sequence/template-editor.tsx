import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Mail, Phone, Calendar, MessageSquare, Wand2, Code, Eye, Save, Variable, Sparkles } from 'lucide-react';

const TemplateEditor = ({ onBack }) => {
  const [mode, setMode] = useState('edit');
  
  return (
    <div className="h-screen bg-gray-50 overflow-auto p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Button variant="ghost" onClick={onBack} className="mb-2">
            ← Back to Builder
          </Button>
          <h1 className="text-2xl font-bold">Template Editor</h1>
          <p className="text-gray-500 mt-1">Create and manage message templates</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => setMode(mode === 'edit' ? 'preview' : 'edit')}>
            {mode === 'edit' ? (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </>
            ) : (
              <>
                <Code className="h-4 w-4 mr-2" />
                Edit
              </>
            )}
          </Button>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Save Template
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2 space-y-6">
          {/* Template Builder */}
          <Card>
            <CardHeader>
              <CardTitle>Template Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Template Name</label>
                    <Input placeholder="e.g., Initial Outreach Email" className="mt-1" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Type</label>
                      <Select defaultValue="email">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="call">Phone Script</SelectItem>
                          <SelectItem value="meeting">Meeting Agenda</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Category</label>
                      <Select defaultValue="outreach">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="outreach">Initial Outreach</SelectItem>
                          <SelectItem value="follow_up">Follow-up</SelectItem>
                          <SelectItem value="nurture">Nurture</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Tabs defaultValue="content">
                  <TabsList>
                    <TabsTrigger value="content">Content</TabsTrigger>
                    <TabsTrigger value="personalization">Personalization</TabsTrigger>
                    <TabsTrigger value="conditions">Conditions</TabsTrigger>
                  </TabsList>

                  <TabsContent value="content" className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Subject Line</label>
                      <Input placeholder="Enter email subject" className="mt-1" />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Message Content</label>
                      <Textarea 
                        className="min-h-[300px] mt-1 font-mono"
                        placeholder="Enter your message content here..."
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </CardContent>
          </Card>

          {/* Preview Card */}
          {mode === 'preview' && (
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-w-2xl mx-auto bg-white p-6 rounded-lg border">
                  <div className="space-y-4">
                    <div className="font-medium text-lg">Investment Opportunity - Madison Ave Portfolio</div>
                    <div className="text-gray-600">
                      Dear {'{{contact.firstName}}'},<br/><br/>
                      I hope this email finds you well. Given your focus on prime office assets in the Manhattan market, 
                      I wanted to bring an exciting opportunity to your attention...<br/><br/>
                      Best regards,<br/>
                      {'{{sender.name}}'}<br/>
                      {'{{sender.title}}'}<br/>
                      Anax Capital Advisory
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          {/* AI Assistant */}
          <Card className="bg-gradient-to-br from-blue-50 to-white border-blue-100">
            <CardHeader>
              <CardTitle>AI Writing Assistant</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button className="w-full justify-start" variant="ghost">
                  <Wand2 className="h-4 w-4 mr-2" />
                  Improve writing style
                </Button>
                <Button className="w-full justify-start" variant="ghost">
                  <Wand2 className="h-4 w-4 mr-2" />
                  Add personalization
                </Button>
                <Button className="w-full justify-start" variant="ghost">
                  <Wand2 className="h-4 w-4 mr-2" />
                  Suggest follow-ups
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Variables Reference */}
          <Card>
            <CardHeader>
              <CardTitle>Available Variables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <VariableItem 
                  name="contact.firstName"
                  description="Contact's first name"
                />
                <VariableItem 
                  name="contact.company"
                  description="Company name"
                />
                <VariableItem 
                  name="deal.name"
                  description="Deal name"
                />
                <VariableItem 
                  name="sender.name"
                  description="Your full name"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const VariableItem = ({ name, description }) => (
  <div className="flex items-center justify-between p-2 rounded-lg border border-gray-100 bg-gray-50">
    <div>
      <div className="font-mono text-sm text-blue-600">{name}</div>
      <div className="text-sm text-gray-500">{description}</div>
    </div>
    <Button variant="ghost" size="sm">
      <Variable className="h-4 w-4" />
    </Button>
  </div>
);

export default TemplateEditor;